import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-neutral-50 p-4">
      <div className="text-center max-w-md">
        <div className="text-9xl font-bold text-primary-300">404</div>
        <h1 className="text-2xl font-bold text-neutral-800 mt-4 mb-2">الصفحة غير موجودة</h1>
        <p className="text-neutral-600 mb-6">
          عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
        </p>
        <Link 
          to="/" 
          className="btn btn-primary inline-flex items-center gap-2"
        >
          <ArrowRight className="w-4 h-4" />
          <span>العودة للرئيسية</span>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;