import { Fragment, useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import {
  LayoutDashboard,
  Target,
  Lightbulb,
  CheckSquare,
  LineChart,
  Building2,
  FileBarChart,
  Users,
  Brain,
  Trash2,
  X
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const navItems = [
  { path: '/dashboard', label: 'لوحة التحكم', icon: <LayoutDashboard className="w-5 h-5" /> },
  // التسلسل الهرمي للتخطيط الاستراتيجي
  { path: '/strategic-objectives', label: 'الأهداف الاستراتيجية', icon: <Target className="w-5 h-5" />, level: '1' },
  { path: '/kpis', label: 'مؤشرات الأداء', icon: <LineChart className="w-5 h-5" />, level: '2' },
  { path: '/initiatives', label: 'المشاريع', icon: <Lightbulb className="w-5 h-5" />, level: '3' },
  { path: '/activities', label: 'الأنشطة', icon: <CheckSquare className="w-5 h-5" />, level: '4' },
  // الإدارة والمستخدمون
  { path: '/users', label: 'المستخدمون', icon: <Users className="w-5 h-5" /> },
  { path: '/departments', label: 'الأقسام', icon: <Building2 className="w-5 h-5" /> },
  // التقارير
  { path: '/reports', label: 'التقارير', icon: <FileBarChart className="w-5 h-5" /> },
  // الاستيراد الذكي
  { path: '/smart-import', label: 'الاستيراد الذكي', icon: <Brain className="w-5 h-5" /> },
  // الحذف (مؤقت)
  { path: '/delete-data', label: 'الحذف', icon: <Trash2 className="w-5 h-5" /> },
];

const Sidebar = ({ open, setOpen }: SidebarProps) => {
  const { user } = useAuth();
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  if (!user) return null;
  
  return (
    <Fragment>
      {/* Overlay for mobile */}
      <AnimatePresence>
        {open && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setOpen(false)}
            className="fixed inset-0 bg-neutral-900/50 z-30 lg:hidden"
          />
        )}
      </AnimatePresence>
      
      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          x: isLargeScreen ? 0 : (open ? 0 : "100%")
        }}
        transition={{ type: "spring", bounce: 0, duration: 0.4 }}
        className={`fixed inset-y-0 right-0 bg-white shadow-xl border-l border-neutral-200 w-60 z-40 lg:relative lg:translate-x-0 lg:shadow-none lg:border-l-0 lg:border-r lg:border-neutral-200 ${
          isLargeScreen ? 'block' : ''
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <div className="h-14 flex items-center justify-between px-4 border-b border-neutral-200">
            <div className="font-bold text-primary-700 text-base">
              نظام التخطيط
            </div>
            <button
              onClick={() => setOpen(false)}
              className="lg:hidden p-1.5 rounded-md hover:bg-neutral-100 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-3">
            <ul className="space-y-0.5 px-3">
              {navItems.map((item) => (
                <li key={item.path}>
                  <NavLink
                    to={item.path}
                    onClick={() => {
                      // Close sidebar on mobile after clicking
                      if (window.innerWidth < 1024) {
                        setOpen(false);
                      }
                    }}
                    className={({ isActive }) =>
                      `flex items-center gap-3 py-2 rounded-lg text-sm transition-all duration-200 cursor-pointer ${
                        item.level ? `pr-6 pl-3` : 'px-3'
                      } ${
                        isActive
                          ? 'bg-primary-50 text-primary-700 font-medium shadow-sm'
                          : 'text-neutral-600 hover:bg-neutral-50 hover:text-neutral-800'
                      }`
                    }
                  >
                    {item.level && (
                      <span className="text-xs text-neutral-400 font-medium w-4 text-center">
                        {item.level}
                      </span>
                    )}
                    <span className="shrink-0">{item.icon}</span>
                    <span className="truncate">{item.label}</span>
                  </NavLink>
                </li>
              ))}
            </ul>
          </nav>
          
          {/* Footer */}
          <div className="border-t border-neutral-200 p-3">
            <div className="text-xs text-neutral-500 text-center">
              إصدار 0.1.0
            </div>
          </div>
        </div>
      </motion.aside>
    </Fragment>
  );
};

export default Sidebar;