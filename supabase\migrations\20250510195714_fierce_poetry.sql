/*
  # Initial Schema Setup

  1. New Tables
    - `departments`: Organization structure
    - `roles`: User role definitions
    - `user_profiles`: Extended user information
  
  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    
  3. Initial Data
    - Create ADMIN role
*/

-- Create departments table first
CREATE TABLE IF NOT EXISTS departments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  parent_id uuid REFERENCES departments(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE departments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read departments"
  ON departments
  FOR SELECT
  TO authenticated
  USING (true);

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  description text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read roles"
  ON roles
  FOR SELECT
  TO authenticated
  USING (true);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  full_name text NOT NULL,
  role_id uuid REFERENCES roles ON DELETE RESTRICT,
  department_id uuid REFERENCES departments ON DELETE RESTRICT,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Insert initial admin role
INSERT INTO roles (id, name, description)
VALUES (
  'a81a6cd7-8125-4c27-8df5-7e2df0b8875a',
  'ADMIN',
  'System Administrator with full access'
) ON CONFLICT (name) DO NOTHING;

-- Insert initial department
INSERT INTO departments (id, name, description)
VALUES (
  'b92a7c56-1234-5678-9abc-def012345678',
  'Administration',
  'System Administration Department'
) ON CONFLICT (id) DO NOTHING;