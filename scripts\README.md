# إضافة المستخدمين الإداريين

هذا المجلد يحتوي على السكريبتات المطلوبة لإضافة المستخدمين الإداريين التالين بصلاحيات مدير:

## المستخدمون المطلوب إضافتهم:

1. **أحمد الدريهم** - مدير قسم التطوع
   - البريد: <EMAIL>
   - الهاتف: 966531685640
   - الدور: MANAGER

2. **سعد الدريهم** - المدير التنفيذي
   - البريد: <EMAIL>
   - الهاتف: 966555104096
   - الدور: ADMIN

3. **سعد القحيز** - مدير مشاريع
   - البريد: <EMAIL>
   - الهاتف: 966555108628
   - الدور: MANAGER

4. **عبدالعزيز المطرد** - مدير مشاريع
   - البريد: <EMAIL>
   - الهاتف: 966544303202
   - الدور: MANAGER

5. **عبدالله المحسن** - مدير قسم الموارد المالية
   - البريد: <EMAIL>
   - الهاتف: 966555106645
   - الدور: MANAGER

6. **معتصم العرفج** - مدير قسم البرامج
   - البريد: <EMAIL>
   - الهاتف: 9665608194
   - الدور: MANAGER

7. **ناصر اليحيى** - منسق البيئات
   - البريد: <EMAIL>
   - الهاتف: 966500977270
   - الدور: SUPERVISOR

## الملفات المتاحة:

### 1. `run-add-users.js` (الطريقة المفضلة)
سكريبت Node.js يمكن تشغيله مباشرة لإضافة جميع المستخدمين تلقائياً.

**كيفية التشغيل:**
```bash
# تأكد من وجود Node.js و npm
node scripts/run-add-users.js
```

**المميزات:**
- ✅ إنشاء الأقسام المفقودة تلقائياً
- ✅ إنشاء كلمات مرور عشوائية آمنة
- ✅ التحقق من وجود المستخدمين مسبقاً
- ✅ تقرير مفصل بالنتائج
- ✅ معالجة الأخطاء

### 2. `add-admin-users-supabase.sql`
سكريبت SQL يمكن تشغيله في Supabase SQL Editor.

**كيفية الاستخدام:**
1. افتح Supabase Dashboard
2. اذهب إلى SQL Editor
3. انسخ والصق محتوى الملف
4. شغل السكريبت

**ملاحظة:** يتطلب إنشاء المستخدمين في نظام المصادقة يدوياً أولاً.

### 3. `add-admin-users.ts`
سكريبت TypeScript للاستخدام في بيئة التطوير.

## متطلبات التشغيل:

### للسكريبت JavaScript:
- Node.js (الإصدار 14 أو أحدث)
- مكتبة @supabase/supabase-js
- مفتاح Supabase Service Role

### للسكريبت SQL:
- الوصول إلى Supabase Dashboard
- صلاحيات تشغيل SQL

## إعداد البيئة:

1. **تأكد من وجود مفتاح Supabase Service Role:**
   ```bash
   export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
   ```

2. **تثبيت المكتبات المطلوبة (إذا لم تكن مثبتة):**
   ```bash
   npm install @supabase/supabase-js
   ```

## ما يحدث عند التشغيل:

1. **إنشاء الأقسام المطلوبة:**
   - قسم التطوع
   - الإدارة التنفيذية
   - البرامج والمشاريع
   - الموارد المالية

2. **إنشاء المستخدمين:**
   - إنشاء حساب في نظام المصادقة
   - إنشاء ملف شخصي في قاعدة البيانات
   - ربط المستخدم بالدور والقسم المناسب

3. **إنشاء كلمات مرور عشوائية آمنة**

4. **تقرير مفصل بالنتائج**

## الأمان:

- ✅ كلمات مرور عشوائية قوية (12 حرف)
- ✅ تشفير كلمات المرور في قاعدة البيانات
- ✅ استخدام Service Role Key للصلاحيات الإدارية
- ✅ التحقق من صحة البيانات

## استكشاف الأخطاء:

### خطأ في الاتصال بقاعدة البيانات:
- تأكد من صحة URL و Service Role Key
- تأكد من الاتصال بالإنترنت

### خطأ في إنشاء المستخدم:
- تأكد من عدم وجود البريد الإلكتروني مسبقاً
- تأكد من صحة تنسيق البريد الإلكتروني

### خطأ في الصلاحيات:
- تأكد من استخدام Service Role Key وليس Anon Key
- تأكد من تفعيل RLS policies

## بعد التشغيل:

1. **احفظ كلمات المرور المُنشأة** في مكان آمن
2. **أرسل بيانات الدخول للمستخدمين** عبر قناة آمنة
3. **اطلب من المستخدمين تغيير كلمات المرور** عند أول تسجيل دخول
4. **تحقق من صحة الأدوار والصلاحيات** في النظام

## الدعم:

في حالة مواجهة أي مشاكل:
1. تحقق من logs السكريبت
2. تحقق من Supabase Dashboard للأخطاء
3. تأكد من صحة إعدادات قاعدة البيانات
