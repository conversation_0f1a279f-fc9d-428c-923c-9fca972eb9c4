import React from 'react';
import { Brain } from 'lucide-react';
import SmartImport from '../../components/smart-import/SmartImport';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { supabase } from '../../lib/supabase';

const SmartImportPage = () => {
  const notifications = useNotifications();

  const handleImport = async (data: any, importType: string) => {
    try {
      notifications.info('جاري المعالجة...', 'يتم تحليل البيانات والتحقق من الموجود مسبقاً');

      const results = {
        existing: {},
        new: {},
        total: {}
      };

      // معالجة كل نوع بيانات
      for (const [dataType, items] of Object.entries(data)) {
        if (!Array.isArray(items)) continue;

        const tableName = getTableName(dataType);
        const titleField = getTitleField(dataType);
        
        // التحقق من البيانات الموجودة
        const existingItems = await checkExistingItems(tableName, titleField, items);
        const newItems = items.filter(item => 
          !existingItems.some(existing => existing[titleField] === item[titleField])
        );

        results.existing[dataType] = existingItems.length;
        results.new[dataType] = newItems.length;
        results.total[dataType] = items.length;

        // إدراج البيانات الجديدة
        if (newItems.length > 0) {
          const cleanedItems = cleanDataForTable(dataType, newItems);
          const { error } = await supabase
            .from(tableName)
            .insert(cleanedItems);

          if (error) {
            console.error(`Error inserting ${dataType}:`, error);
            throw new Error(`فشل في إدراج ${getDataTypeName(dataType)}: ${error.message}`);
          }
        }
      }

      // عرض النتائج
      showImportResults(results, importType);

    } catch (error) {
      console.error('Error in smart import:', error);
      throw error;
    }
  };

  const getTableName = (dataType: string): string => {
    const mapping = {
      'strategic_objectives': 'strategic_objectives',
      'kpis': 'kpis',
      'projects': 'initiatives', // تم توحيد التسمية
      'activities': 'activities',
      'departments': 'departments'
    };
    return mapping[dataType] || dataType;
  };

  const getTitleField = (dataType: string): string => {
    const mapping = {
      'strategic_objectives': 'title',
      'kpis': 'name',
      'projects': 'title',
      'activities': 'title',
      'departments': 'name'
    };
    return mapping[dataType] || 'title';
  };

  const getDataTypeName = (dataType: string): string => {
    const mapping = {
      'strategic_objectives': 'الأهداف الاستراتيجية',
      'kpis': 'مؤشرات الأداء',
      'projects': 'المشاريع',
      'activities': 'الأنشطة',
      'departments': 'الأقسام'
    };
    return mapping[dataType] || dataType;
  };

  const checkExistingItems = async (tableName: string, titleField: string, items: any[]) => {
    try {
      const titles = items.map(item => item[titleField]).filter(Boolean);
      if (titles.length === 0) return [];

      const { data, error } = await supabase
        .from(tableName)
        .select(titleField)
        .in(titleField, titles);

      if (error) {
        console.error(`Error checking existing ${tableName}:`, error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error(`Error in checkExistingItems for ${tableName}:`, error);
      return [];
    }
  };

  const cleanDataForTable = (dataType: string, items: any[]) => {
    switch (dataType) {
      case 'strategic_objectives':
        return items.map(item => ({
          title: item.title || '',
          description: item.description || '',
          owner: item.owner || 'غير محدد',
          department: item.department || 'غير محدد',
          priority: normalizePriority(item.priority) || 'عالية',
          status: normalizeObjectiveStatus(item.status) || 'جديد',
          progress: Math.min(Math.max(parseInt(item.progress) || 0, 0), 100),
          start_date: item.start_date || null,
          end_date: item.end_date || null,
          budget: parseFloat(item.budget) || 0,
          expected_outcome: item.expected_outcome || '',
          success_metrics: item.success_metrics || '',
          manager: item.manager || ''
        }));

      case 'kpis':
        return items.map(item => ({
          name: item.name || '',
          category: item.category || 'عام',
          objective: item.objective || '',
          related_project: item.related_project || '',
          current_value: parseFloat(item.current_value) || 0,
          target_value: parseFloat(item.target_value) || 0,
          unit: item.unit || '',
          trend: normalizeKPITrend(item.trend) || 'stable',
          change_percentage: parseFloat(item.change_percentage) || 0,
          status: normalizeKPIStatus(item.status) || 'جيد',
          description: item.description || '',
          frequency: normalizeKPIFrequency(item.frequency) || 'شهري',
          data_source: item.data_source || '',
          formula: item.formula || '',
          responsible: item.responsible || 'غير محدد',
          last_updated: item.last_updated || null
        }));

      case 'projects':
        return items.map(item => ({
          title: item.title || '',
          description: item.description || '',
          objective: item.objective || '',
          related_kpi: item.related_kpi || '',
          department: item.department || 'غير محدد',
          manager: item.manager || 'غير محدد',
          priority: normalizePriority(item.priority) || 'عالية',
          status: normalizeProjectStatus(item.status) || 'جديدة',
          progress: Math.min(Math.max(parseInt(item.progress) || 0, 0), 100),
          start_date: item.start_date || null,
          end_date: item.end_date || null,
          budget: parseFloat(item.budget) || 0,
          expected_outcome: item.expected_outcome || '',
          success_metrics: item.success_metrics || ''
        }));

      case 'activities':
        return items.map(item => ({
          title: item.title || '',
          description: item.description || '',
          initiative: item.project || item.related_project || '',
          related_project: item.project || item.related_project || '',
          department: item.department || 'غير محدد',
          assignee: item.assignee || 'غير محدد',
          status: normalizeActivityStatus(item.status),
          priority: normalizePriority(item.priority),
          progress: Math.min(Math.max(parseInt(item.progress) || 0, 0), 100),
          start_date: item.start_date || null,
          end_date: item.end_date || null,
          budget: parseFloat(item.budget) || 0,
          participants: parseInt(item.participants) || 0,
          estimated_hours: parseInt(item.estimated_hours) || 0,
          actual_hours: parseInt(item.actual_hours) || 0
        }));

      case 'departments':
        return items.map(item => ({
          name: item.name || '',
          description: item.description || '',
          manager: item.manager || '',
          email: item.email || '',
          phone: item.phone || '',
          employee_count: parseInt(item.employee_count) || 0,
          budget: parseFloat(item.budget) || 0,
          status: item.status || 'نشط'
        }));

      default:
        return items;
    }
  };

  const normalizeStatus = (status: string): string => {
    if (!status) return 'جاري التنفيذ';
    if (status === 'قيد التنفيذ' || status === 'مخطط') return 'جاري التنفيذ';
    if (status === 'ملغي' || status === 'متوقف') return 'متعثر';
    return status;
  };

  const normalizeObjectiveStatus = (status: string): string => {
    if (!status) return 'جديد';

    // تطبيع الحالات للأهداف الاستراتيجية
    const statusMap = {
      'نشط': 'جاري',
      'قيد التنفيذ': 'جاري',
      'جاري التنفيذ': 'جاري',
      'مخطط': 'جديد',
      'جديد': 'جديد',
      'مكتمل': 'مكتمل',
      'منتهي': 'مكتمل',
      'متأخر': 'متأخر',
      'متعثر': 'متأخر',
      'معلق': 'معلق',
      'متوقف': 'معلق',
      'ملغي': 'معلق'
    };

    return statusMap[status] || 'جديد';
  };

  const normalizePriority = (priority: string): string => {
    if (!priority) return 'عالية';

    // تطبيع الأولويات
    const priorityMap = {
      'عالية': 'عالية',
      'عالي': 'عالية',
      'مرتفعة': 'عالية',
      'متوسطة': 'متوسطة',
      'متوسط': 'متوسطة',
      'منخفضة': 'منخفضة',
      'منخفض': 'منخفضة',
      'قليلة': 'منخفضة'
    };

    return priorityMap[priority] || 'عالية';
  };

  const normalizeKPIStatus = (status: string): string => {
    if (!status) return 'جيد';

    const statusMap = {
      'ممتاز': 'ممتاز',
      'ممتازة': 'ممتاز',
      'جيد': 'جيد',
      'جيدة': 'جيد',
      'متوسط': 'متوسط',
      'متوسطة': 'متوسط',
      'يحتاج تحسين': 'يحتاج تحسين',
      'ضعيف': 'يحتاج تحسين',
      'سيء': 'يحتاج تحسين'
    };

    return statusMap[status] || 'جيد';
  };

  const normalizeKPITrend = (trend: string): string => {
    if (!trend) return 'stable';

    const trendMap = {
      'صاعد': 'up',
      'متصاعد': 'up',
      'ارتفاع': 'up',
      'نازل': 'down',
      'متنازل': 'down',
      'انخفاض': 'down',
      'ثابت': 'stable',
      'مستقر': 'stable',
      'up': 'up',
      'down': 'down',
      'stable': 'stable'
    };

    return trendMap[trend] || 'stable';
  };

  const normalizeKPIFrequency = (frequency: string): string => {
    if (!frequency) return 'شهري';

    const frequencyMap = {
      'يومي': 'يومي',
      'يوميا': 'يومي',
      'أسبوعي': 'أسبوعي',
      'أسبوعيا': 'أسبوعي',
      'شهري': 'شهري',
      'شهريا': 'شهري',
      'ربع سنوي': 'ربع سنوي',
      'ربعي': 'ربع سنوي',
      'سنوي': 'سنوي',
      'سنويا': 'سنوي'
    };

    return frequencyMap[frequency] || 'شهري';
  };

  const normalizeProjectStatus = (status: string): string => {
    if (!status) return 'جديدة';

    const statusMap = {
      'جديدة': 'جديدة',
      'جديد': 'جديدة',
      'مخطط': 'التخطيط',
      'التخطيط': 'التخطيط',
      'تخطيط': 'التخطيط',
      'جاري التنفيذ': 'جاري التنفيذ',
      'جاري': 'جاري التنفيذ',
      'قيد التنفيذ': 'جاري التنفيذ',
      'مكتملة': 'مكتملة',
      'مكتمل': 'مكتملة',
      'منتهي': 'مكتملة',
      'متأخرة': 'متأخرة',
      'متأخر': 'متأخرة',
      'متعثر': 'متأخرة',
      'معلقة': 'معلقة',
      'معلق': 'معلقة',
      'متوقف': 'معلقة',
      'ملغي': 'معلقة'
    };

    return statusMap[status] || 'جديدة';
  };

  const normalizeActivityStatus = (status: string): string => {
    if (!status) return 'لم يبدأ';

    const statusMap = {
      'لم يبدأ': 'لم يبدأ',
      'لم يبدء': 'لم يبدأ',
      'جديد': 'لم يبدأ',
      'مخطط': 'لم يبدأ',
      'جاري التنفيذ': 'جاري التنفيذ',
      'جاري': 'جاري التنفيذ',
      'قيد التنفيذ': 'جاري التنفيذ',
      'مكتمل': 'مكتمل',
      'مكتملة': 'مكتمل',
      'منتهي': 'مكتمل',
      'متأخر': 'متأخر',
      'متأخرة': 'متأخر',
      'متعثر': 'متعثر',
      'متعثرة': 'متعثر',
      'معلق': 'متعثر',
      'متوقف': 'متعثر'
    };

    return statusMap[status] || 'لم يبدأ';
  };

  const showImportResults = (results: any, importType: string) => {
    const totalNew = Object.values(results.new).reduce((sum: number, count: any) => sum + count, 0);
    const totalExisting = Object.values(results.existing).reduce((sum: number, count: any) => sum + count, 0);

    if (totalNew > 0) {
      notifications.success(
        `تم الاستيراد بنجاح! 🎉`,
        `تم إضافة ${totalNew} عنصر جديد`
      );

      // تفاصيل كل نوع
      Object.entries(results.new).forEach(([dataType, count]: [string, any]) => {
        if (count > 0) {
          notifications.info(
            `${getDataTypeName(dataType)} 📋`,
            `تم إضافة ${count} عنصر جديد`
          );
        }
      });
    }

    if (totalExisting > 0) {
      notifications.warning(
        'عناصر موجودة مسبقاً ⚠️',
        `تم تجاهل ${totalExisting} عنصر موجود مسبقاً`
      );
    }

    if (totalNew === 0 && totalExisting === 0) {
      notifications.info('لا توجد بيانات', 'لم يتم العثور على بيانات صالحة للاستيراد');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Brain className="w-6 h-6 text-purple-600" />
        <div>
          <h1 className="text-2xl font-bold text-neutral-800">الاستيراد الذكي للبيانات</h1>
          <p className="text-sm text-neutral-600">استيراد البيانات بطريقة ذكية ومتدرجة باستخدام الذكاء الصناعي</p>
        </div>
      </div>

      <SmartImport onImport={handleImport} />
    </div>
  );
};

export default SmartImportPage;
