import React, { useState, useEffect, useRef } from 'react';
import { FileBar<PERSON>hart, Download, Filter, Calendar, BarChart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON>dingUp, Eye, Share2, FileText, Plus, Search, Edit, Trash2, Loader2, Save } from 'lucide-react';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { ReportService } from '../../lib/reportService';
import { DepartmentService } from '../../lib/departmentService';

// مكون اختيار القسم
const DepartmentSelect = ({ value, onChange }) => {
  const [departments, setDepartments] = useState(['جميع الإدارات']);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const deptData = await DepartmentService.getAllDepartments();
        const deptNames = ['جميع الإدارات', ...deptData.map(dept => dept.name)];
        setDepartments(deptNames);
      } catch (error) {
        console.error('Error fetching departments:', error);
      }
    };
    fetchDepartments();
  }, []);

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
    >
      {departments.map(dept => (
        <option key={dept} value={dept}>{dept}</option>
      ))}
    </select>
  );
};

// مكون النموذج المدمج
const InlineReportForm = ({ editData, onSave, onCancel, onDataChange }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    type: editData?.type || 'أداء',
    department: editData?.department || 'جميع الإدارات',
    period: editData?.period || 'شهري',
    description: editData?.description || '',
    format: editData?.format || 'PDF'
  });

  const [errors, setErrors] = useState({});
  
  useEffect(() => {
    if (editData) {
      setFormData({
        title: editData?.title || '',
        type: editData?.type || 'أداء',
        department: editData?.department || 'جميع الإدارات',
        period: editData?.period || 'شهري',
        description: editData?.description || '',
        format: editData?.format || 'PDF'
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان التقرير مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف التقرير مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        id: editData?.id || `report-${Date.now()}`,
        createdDate: editData?.createdDate || new Date().toISOString().split('T')[0],
        createdBy: editData?.createdBy || 'المستخدم الحالي',
        status: editData?.status || 'مكتمل',
        size: editData?.size || '2.1 MB',
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث التقرير' : 'تم إضافة التقرير', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ التقرير'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              عنوان التقرير *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان التقرير"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف التقرير *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للتقرير"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                نوع التقرير
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleChange('type', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="أداء">تقارير الأداء</option>
                <option value="مؤشرات">تقارير المؤشرات</option>
                <option value="أنشطة">تقارير الأنشطة</option>
                <option value="مالي">التقارير المالية</option>
                <option value="شامل">تقرير شامل</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الفترة الزمنية
              </label>
              <select
                value={formData.period}
                onChange={(e) => handleChange('period', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="أسبوعي">أسبوعي</option>
                <option value="شهري">شهري</option>
                <option value="ربع سنوي">ربع سنوي</option>
                <option value="سنوي">سنوي</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الإدارة المختصة
              </label>
              <DepartmentSelect
                value={formData.department}
                onChange={(value) => handleChange('department', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تنسيق التقرير
              </label>
              <select
                value={formData.format}
                onChange={(e) => handleChange('format', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="PDF">PDF</option>
                <option value="Excel">Excel</option>
                <option value="Word">Word</option>
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [loading, setLoading] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedReportType, setSelectedReportType] = useState('all');
  const [selectedItem, setSelectedItem] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('reportsListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  const notifications = useNotifications();

  // البيانات الحقيقية من قاعدة البيانات
  const [reports, setReports] = useState([]);
  const [departments, setDepartments] = useState(['جميع الإدارات']);
  const [reportStats, setReportStats] = useState({
    totalReports: 0,
    completedReports: 0,
    pendingReports: 0,
    totalSize: '0 KB',
    reportsByType: {},
    reportsByDepartment: {},
    recentActivity: 0
  });

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    fetchReports();
    fetchDepartments();
    fetchReportStats();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const data = await ReportService.getAllReports();

      // تحويل البيانات للتنسيق المطلوب
      const formattedReports = data.map(report => ({
        id: report.id,
        title: report.title,
        type: report.type,
        department: report.department || 'جميع الإدارات',
        period: report.period || 'شهري',
        createdDate: report.created_at.split('T')[0],
        createdBy: report.created_by || 'غير محدد',
        status: report.status || 'مكتمل',
        size: report.file_size || '0 KB',
        format: report.format || 'PDF',
        description: report.description || 'لا يوجد وصف'
      }));

      setReports(formattedReports);
    } catch (error) {
      console.error('Error fetching reports:', error);
      notifications.error('خطأ', 'فشل في تحميل التقارير');
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const deptData = await DepartmentService.getAllDepartments();
      const deptNames = ['جميع الإدارات', ...deptData.map(dept => dept.name)];
      setDepartments(deptNames);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const fetchReportStats = async () => {
    try {
      // حساب الإحصائيات من البيانات المحلية
      const stats = {
        totalReports: reports.length,
        completedReports: reports.filter(r => r.status === 'مكتمل').length,
        pendingReports: reports.filter(r => r.status === 'قيد الإعداد').length,
        totalSize: '0 KB',
        reportsByType: {},
        reportsByDepartment: {},
        recentActivity: 0
      };
      setReportStats(stats);
    } catch (error) {
      console.error('Error calculating report stats:', error);
    }
  };

  // تحديث الإحصائيات عند تغيير التقارير
  useEffect(() => {
    fetchReportStats();
  }, [reports]);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('reportsListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // وظائف التقارير
  const handleGenerateReport = async () => {
    setLoading(true);
    notifications.info('جاري الإعداد', 'يتم إعداد التقرير...');

    try {
      let reportData;
      let description = '';
      const reportType = selectedReportType === 'all' ? 'شامل' : selectedReportType;
      const department = selectedDepartment === 'all' ? 'جميع الإدارات' : selectedDepartment;

      // إنشاء التقرير بناءً على النوع المحدد
      switch (reportType) {
        case 'أداء':
          reportData = await ReportService.getPerformanceReport();
          description = `تقرير أداء شامل - معدل إنجاز الأهداف: ${reportData.summary.objectivesProgress.toFixed(1)}%، معدل إنجاز الأنشطة: ${reportData.summary.activitiesProgress.toFixed(1)}%`;
          break;

        case 'مؤشرات':
          reportData = await ReportService.getKPIReport();
          description = `تقرير مؤشرات الأداء - إجمالي المؤشرات: ${reportData.summary.total}، المحققة: ${reportData.summary.achieved}`;
          break;

        case 'مالي':
          reportData = await ReportService.getFinancialReport();
          description = `التقرير المالي - إجمالي المشاريع: ${reportData.summary.totalProjects}، إجمالي الميزانية: ${reportData.summary.totalBudget.toLocaleString('ar-SA')} ريال`;
          break;

        case 'أنشطة':
          reportData = await ReportService.getPerformanceReport();
          description = `تقرير الأنشطة - إجمالي الأنشطة: ${reportData.summary.totalActivities}، المكتملة: ${reportData.summary.completedActivities}`;
          break;

        case 'شامل':
          reportData = await ReportService.getComprehensiveReport();
          description = 'تقرير شامل يتضمن جميع جوانب الأداء والمؤشرات والبيانات المالية';
          break;

        default:
          reportData = await ReportService.getPerformanceReport();
          description = 'تقرير عام حول الأداء';
      }

      // حفظ التقرير في قاعدة البيانات
      const savedReport = await ReportService.createReport({
        title: `تقرير ${reportType} - ${department} - ${new Date().toLocaleDateString('ar-SA')}`,
        type: reportType,
        data: reportData
      });

      // إضافة التقرير للقائمة المحلية
      const newReport = {
        id: savedReport.id,
        title: savedReport.title,
        type: savedReport.type,
        department: department,
        period: selectedPeriod,
        createdDate: savedReport.created_at.split('T')[0],
        createdBy: 'المستخدم الحالي',
        status: 'مكتمل',
        size: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 9) + 1} MB`,
        format: 'PDF',
        description: description
      };

      setReports(prev => [newReport, ...prev]);
      notifications.success('تم الإنشاء', 'تم إنشاء التقرير بنجاح');
    } catch (error) {
      console.error('Error generating report:', error);
      notifications.error('خطأ', 'فشل في إنشاء التقرير');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveReport = async (reportData) => {
    try {
      if (editingItem) {
        setReports(prev =>
          prev.map(report =>
            report.id === editingItem.id
              ? { ...reportData, id: editingItem.id }
              : report
          )
        );
        setSelectedItem({ ...reportData, id: editingItem.id });
        notifications.success('تم التحديث', 'تم تحديث التقرير بنجاح');
      } else {
        const newId = `report-${Date.now()}`;
        const newReport = { ...reportData, id: newId };
        setReports(prev => [...prev, newReport]);
        setSelectedItem(newReport);
        notifications.success('تم الإضافة', 'تم إضافة التقرير بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      notifications.error('خطأ', 'فشل في حفظ التقرير');
      throw error;
    }
  };

  const handleDeleteReport = async (id) => {
    if (confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
      try {
        setReports(prev => prev.filter(report => report.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف التقرير بنجاح');
      } catch (error) {
        notifications.error('خطأ في الحذف', 'فشل في حذف التقرير');
      }
    }
  };

  const handleDownloadReport = (report) => {
    notifications.info('جاري التحميل', `يتم تحميل ${report.title}...`);
    setTimeout(() => {
      notifications.success('تم التحميل', 'تم تحميل التقرير بنجاح');
    }, 2000);
  };

  const handleViewReport = async (report) => {
    try {
      notifications.info('جاري التحميل', `يتم تحميل بيانات ${report.title}...`);

      // جلب البيانات الحقيقية للتقرير
      let reportData;
      switch (report.type) {
        case 'أداء':
          reportData = await ReportService.getPerformanceReport();
          break;
        case 'مؤشرات':
          reportData = await ReportService.getKPIReport();
          break;
        case 'مالي':
          reportData = await ReportService.getFinancialReport();
          break;
        case 'شامل':
          reportData = await ReportService.getComprehensiveReport();
          break;
        default:
          reportData = await ReportService.getPerformanceReport();
      }

      // عرض البيانات في نافذة جديدة أو modal
      console.log('Report Data:', reportData);
      notifications.success('تم التحميل', 'تم تحميل بيانات التقرير بنجاح');

      // يمكن إضافة modal هنا لعرض البيانات

    } catch (error) {
      console.error('Error viewing report:', error);
      notifications.error('خطأ', 'فشل في تحميل بيانات التقرير');
    }
  };

  const handleShareReport = (report) => {
    navigator.clipboard.writeText(`تقرير: ${report.title} - تم إنشاؤه في ${report.createdDate}`);
    notifications.success('تم النسخ', 'تم نسخ رابط التقرير إلى الحافظة');
  };

  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    if (isEditMode) {
      setEditingItem(item);
    }
  };

  // فلترة التقارير
  const filteredReports = reports.filter(report => {
    const departmentMatch = selectedDepartment === 'all' || report.department === selectedDepartment || report.department === 'جميع الإدارات';
    const typeMatch = selectedReportType === 'all' || report.type === selectedReportType;
    const searchMatch = !searchTerm || 
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.createdBy.toLowerCase().includes(searchTerm.toLowerCase());
    return departmentMatch && typeMatch && searchMatch;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-success-100 text-success-800';
      case 'قيد الإعداد':
        return 'bg-warning-100 text-warning-800';
      case 'مسودة':
        return 'bg-neutral-100 text-neutral-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.title}</h3>
            {isBeingEdited && (
              <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                قيد التحرير
              </span>
            )}
          </div>
          <span className={`badge text-xs ${getStatusColor(item.status)}`}>
            {item.status}
          </span>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">
          {item.description}
        </p>
        
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>النوع: {item.type}</span>
          <span>{new Date(item.createdDate).toLocaleDateString('ar-SA')}</span>
        </div>
      </div>
    );
  };

  // مكون لوحة المعاينة
  const PreviewPanel = () => {
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineReportForm 
            editData={editingItem}
            onSave={handleSaveReport}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <FileBarChart className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر تقرير لعرض المعاينة</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر تقرير من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-blue-500">
                <FileBarChart size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.title}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full badge ${getStatusColor(selectedItem.status)}`}>
                    {selectedItem.status}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">النوع:</span>
                    <span className="text-gray-900 dark:text-white">{selectedItem.type}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {new Date(selectedItem.createdDate).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={() => handleEdit(selectedItem)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                title="تعديل"
              >
                <Edit size={18} />
              </button>
              <button 
                onClick={() => handleDeleteReport(selectedItem.id)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                title="حذف"
              >
                <Trash2 size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">وصف التقرير</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* معلومات التقرير */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">معلومات التقرير</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الإدارة المختصة</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.department}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الفترة الزمنية</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.period}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">منشئ التقرير</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.createdBy}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">حجم الملف</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.size}</p>
                  </div>
                </div>
              </div>

              {/* إجراءات التقرير */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">إجراءات التقرير</h4>
                <div className="flex gap-3">
                  <button
                    onClick={() => handleViewReport(selectedItem)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    معاينة
                  </button>
                  <button
                    onClick={() => handleDownloadReport(selectedItem)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    تحميل
                  </button>
                  <button
                    onClick={() => handleShareReport(selectedItem)}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                    مشاركة
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <FileBarChart className="w-6 h-6 text-primary-600" />
        <div>
          <h1 className="text-2xl font-bold text-neutral-800">التقارير والتحليلات</h1>
          <p className="text-sm text-neutral-600">إدارة وتحليل التقارير والبيانات الإحصائية</p>
        </div>
      </div>

      {/* إحصائيات التقارير */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي التقارير</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{reportStats.totalReports}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">التقارير المكتملة</p>
              <p className="text-2xl font-bold text-green-600">{reportStats.completedReports}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <BarChart3 className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">قيد الإعداد</p>
              <p className="text-2xl font-bold text-orange-600">{reportStats.pendingReports}</p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">النشاط الأخير</p>
              <p className="text-2xl font-bold text-purple-600">{reportStats.recentActivity}</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* فلاتر التقارير */}
      <div className="card">
        <div className="flex items-center justify-between gap-4">
          <div className="flex gap-2 flex-1">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="input-field min-w-[120px]"
            >
              <option value="weekly">أسبوعي</option>
              <option value="monthly">شهري</option>
              <option value="quarterly">ربع سنوي</option>
              <option value="yearly">سنوي</option>
            </select>

            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="input-field min-w-[160px]"
            >
              <option value="all">جميع الإدارات</option>
              {departments.slice(1).map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>

            <select
              value={selectedReportType}
              onChange={(e) => setSelectedReportType(e.target.value)}
              className="input-field min-w-[140px]"
            >
              <option value="all">جميع الأنواع</option>
              <option value="أداء">تقارير الأداء</option>
              <option value="مؤشرات">تقارير المؤشرات</option>
              <option value="أنشطة">تقارير الأنشطة</option>
              <option value="مالي">التقارير المالية</option>
            </select>

            <button className="btn btn-outline flex items-center gap-2">
              <Filter className="w-4 h-4" />
              <span>تطبيق الفلاتر</span>
            </button>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleGenerateReport}
              disabled={loading}
              className="btn btn-outline flex items-center gap-2"
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <BarChart3 className="w-4 h-4" />
              )}
              <span>{loading ? 'جاري الإنشاء...' : 'إنشاء سريع'}</span>
            </button>
            
            <button
              onClick={handleAddNew}
              className="btn btn-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              <span>تقرير جديد</span>
            </button>
          </div>
        </div>
      </div>

      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
        {/* List Panel */}
        <div 
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
          style={{ width: `${listWidth}%` }}
        >
          {/* Search Header */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400" size={16} />
              <input 
                type="text" 
                placeholder="البحث في التقارير..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm"
              />
            </div>
          </div>
          
          {/* List Items */}
          <div className="flex-1 overflow-y-auto">
            <div className="space-y-1 p-2">
              {filteredReports.map(report => (
                <ListItem 
                  key={report.id} 
                  item={report} 
                  onClick={handleItemClick}
                  isSelected={selectedItem?.id === report.id}
                />
              ))}
            </div>
          </div>
        </div>
        
        {/* Resize Handle */}
        <div 
          className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
            isResizing 
              ? 'bg-blue-500' 
              : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
          }`}
          onMouseDown={() => setIsResizing(true)}
        >
          <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
        </div>

        {/* Preview Panel */}
        <div className="flex-1 overflow-hidden">
          <PreviewPanel />
        </div>
      </div>
    </div>
  );
};

export default Reports;