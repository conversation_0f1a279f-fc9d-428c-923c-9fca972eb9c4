import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

// تعريف أنواع الإشعارات
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (title: string, message?: string, type?: NotificationType, options?: Partial<Notification>) => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

// إنشاء السياق
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Hook لاستخدام نظام الإشعارات
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }

  // إرجاع الوظائف المطلوبة مباشرة
  return {
    success: (title: string, message?: string) =>
      context.addNotification(title, message, 'success'),

    error: (title: string, message?: string) =>
      context.addNotification(title, message, 'error'),

    warning: (title: string, message?: string) =>
      context.addNotification(title, message, 'warning'),

    info: (title: string, message?: string) =>
      context.addNotification(title, message, 'info'),

    // الوظائف الأصلية للمطورين المتقدمين
    addNotification: context.addNotification,
    removeNotification: context.removeNotification,
    clearAll: context.clearAll,
    notifications: context.notifications
  };
};

// مزود السياق
interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider = ({ children }: NotificationProviderProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // إضافة إشعار جديد
  const addNotification = (
    title: string, 
    message?: string, 
    type: NotificationType = 'info',
    options: Partial<Notification> = {}
  ) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const notification: Notification = {
      id,
      title,
      message,
      type,
      duration: 5000, // 5 ثوان افتراضي
      persistent: false,
      ...options
    };

    setNotifications(prev => [...prev, notification]);

    // إزالة الإشعار تلقائياً إذا لم يكن دائماً
    if (!notification.persistent && notification.duration) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration);
    }
  };

  // إزالة إشعار محدد
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // مسح جميع الإشعارات
  const clearAll = () => {
    setNotifications([]);
  };

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
};

// مكون حاوية الإشعارات
const NotificationContainer = () => {
  const { notifications, removeNotification } = useNotifications();

  if (notifications.length === 0) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-md w-full">
      <div className="space-y-2">
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onDismiss={() => removeNotification(notification.id)}
          />
        ))}
      </div>
    </div>
  );
};

// مكون الإشعار الفردي
interface NotificationItemProps {
  notification: Notification;
  onDismiss: () => void;
}

const NotificationItem = ({ notification, onDismiss }: NotificationItemProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // تأثير الظهور
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(onDismiss, 300); // انتظار انتهاء الحركة
  };

  // تحديد الأيقونة واللون حسب النوع
  const getNotificationConfig = () => {
    switch (notification.type) {
      case 'success':
        return {
          icon: <CheckCircle className="w-5 h-5" />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-500',
          textColor: 'text-green-800',
          iconColor: 'text-green-500'
        };
      case 'error':
        return {
          icon: <AlertCircle className="w-5 h-5" />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-500',
          textColor: 'text-red-800',
          iconColor: 'text-red-500'
        };
      case 'warning':
        return {
          icon: <AlertTriangle className="w-5 h-5" />,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-500',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-500'
        };
      case 'info':
      default:
        return {
          icon: <Info className="w-5 h-5" />,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-500',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-500'
        };
    }
  };

  const config = getNotificationConfig();

  return (
    <div 
      className={`
        ${config.bgColor} ${config.borderColor} ${config.textColor}
        border-r-4 p-4 rounded-lg shadow-lg flex items-start
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <div className={`flex-shrink-0 pt-0.5 ${config.iconColor}`}>
        {config.icon}
      </div>
      
      <div className="mr-3 flex-1">
        <p className="text-sm font-medium">{notification.title}</p>
        {notification.message && (
          <p className="mt-1 text-sm opacity-90">{notification.message}</p>
        )}
      </div>
      
      <button 
        onClick={handleDismiss}
        className="text-gray-400 hover:text-gray-500 transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};

// Hook مساعد للإشعارات السريعة
export const useQuickNotifications = () => {
  const { addNotification } = useNotifications();

  return {
    success: (title: string, message?: string) => 
      addNotification(title, message, 'success'),
    
    error: (title: string, message?: string) => 
      addNotification(title, message, 'error'),
    
    warning: (title: string, message?: string) => 
      addNotification(title, message, 'warning'),
    
    info: (title: string, message?: string) => 
      addNotification(title, message, 'info'),
    
    persistent: (title: string, message?: string, type: NotificationType = 'info') =>
      addNotification(title, message, type, { persistent: true }),
  };
};

export default NotificationProvider;
