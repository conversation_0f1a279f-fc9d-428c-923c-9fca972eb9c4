import { useState } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface TaskFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (task: any) => Promise<void>;
  editData?: any;
}

const TaskForm = ({ isOpen, onClose, onSave, editData }: TaskFormProps) => {
  const [loading, setLoading] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    status: editData?.status || 'جديدة',
    priority: editData?.priority || 'متوسطة',
    startDate: editData?.startDate || '',
    dueDate: editData?.dueDate || '',
    progress: editData?.progress || 0,
    assignedBy: editData?.assignedBy || '',
    category: editData?.category || '',
    estimatedHours: editData?.estimatedHours || '',
    actualHours: editData?.actualHours || 0,
    project: editData?.project || '',
    notes: editData?.notes || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان المهمة مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المهمة مطلوب';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'تاريخ الاستحقاق مطلوب';
    }

    if (formData.startDate && formData.dueDate && formData.startDate > formData.dueDate) {
      newErrors.dueDate = 'تاريخ الاستحقاق يجب أن يكون بعد تاريخ البداية';
    }

    if (!formData.assignedBy.trim()) {
      newErrors.assignedBy = 'مسند المهمة مطلوب';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'تصنيف المهمة مطلوب';
    }

    if (!formData.estimatedHours || formData.estimatedHours <= 0) {
      newErrors.estimatedHours = 'الساعات المقدرة مطلوبة ويجب أن تكون أكبر من صفر';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        estimatedHours: parseInt(formData.estimatedHours),
        actualHours: parseInt(formData.actualHours.toString()),
        progress: parseInt(formData.progress.toString()),
        id: editData?.id || `task-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث المهمة' : 'تم إضافة المهمة', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل المهمة' : 'إضافة مهمة جديدة'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* العنوان */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عنوان المهمة *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`input-field ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان المهمة"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف المهمة *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للمهمة"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* الحالة والأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                حالة المهمة
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="input-field"
              >
                <option value="جديدة">جديدة</option>
                <option value="جاري التنفيذ">جاري التنفيذ</option>
                <option value="مجدولة">مجدولة</option>
                <option value="متأخرة">متأخرة</option>
                <option value="مكتملة">مكتملة</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مستوى الأولوية
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="input-field"
              >
                <option value="عالية">عالية</option>
                <option value="متوسطة">متوسطة</option>
                <option value="منخفضة">منخفضة</option>
              </select>
            </div>
          </div>

          {/* التواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`input-field ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ الاستحقاق *
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => handleChange('dueDate', e.target.value)}
                className={`input-field ${errors.dueDate ? 'border-red-500' : ''}`}
              />
              {errors.dueDate && <p className="text-red-500 text-xs mt-1">{errors.dueDate}</p>}
            </div>
          </div>

          {/* مسند المهمة والتصنيف */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مسند المهمة *
              </label>
              <input
                type="text"
                value={formData.assignedBy}
                onChange={(e) => handleChange('assignedBy', e.target.value)}
                className={`input-field ${errors.assignedBy ? 'border-red-500' : ''}`}
                placeholder="اسم الشخص الذي أسند المهمة"
              />
              {errors.assignedBy && <p className="text-red-500 text-xs mt-1">{errors.assignedBy}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تصنيف المهمة *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                className={`input-field ${errors.category ? 'border-red-500' : ''}`}
              >
                <option value="">اختر التصنيف</option>
                <option value="تخطيط">تخطيط</option>
                <option value="تقارير">تقارير</option>
                <option value="قواعد البيانات">قواعد البيانات</option>
                <option value="اجتماعات">اجتماعات</option>
                <option value="مالية">مالية</option>
                <option value="إدارية">إدارية</option>
                <option value="تقنية">تقنية</option>
                <option value="تدريب">تدريب</option>
              </select>
              {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
            </div>
          </div>

          {/* المشروع والساعات */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم المشروع
              </label>
              <input
                type="text"
                value={formData.project}
                onChange={(e) => handleChange('project', e.target.value)}
                className="input-field"
                placeholder="اسم المشروع المرتبط بالمهمة"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الساعات المقدرة *
              </label>
              <input
                type="number"
                value={formData.estimatedHours}
                onChange={(e) => handleChange('estimatedHours', e.target.value)}
                className={`input-field ${errors.estimatedHours ? 'border-red-500' : ''}`}
                placeholder="عدد الساعات"
                min="1"
              />
              {errors.estimatedHours && <p className="text-red-500 text-xs mt-1">{errors.estimatedHours}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الساعات الفعلية
              </label>
              <input
                type="number"
                value={formData.actualHours}
                onChange={(e) => handleChange('actualHours', e.target.value)}
                className="input-field"
                placeholder="الساعات المنجزة فعلياً"
                min="0"
              />
            </div>
          </div>

          {/* نسبة الإنجاز */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نسبة الإنجاز (%)
            </label>
            <div className="space-y-2">
              <input
                type="range"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) => handleChange('progress', e.target.value)}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>0%</span>
                <span className="font-medium">{formData.progress}%</span>
                <span>100%</span>
              </div>
            </div>
          </div>

          {/* الملاحظات */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
              rows={3}
              className="input-field"
              placeholder="أي ملاحظات إضافية حول المهمة"
            />
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ المهمة'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskForm;