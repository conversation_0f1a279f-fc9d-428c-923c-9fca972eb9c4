import { useState } from 'react';
import { X, Save, Loader2, Plus, Trash2 } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface DepartmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (department: any) => Promise<void>;
  editData?: any;
}

const DepartmentForm = ({ isOpen, onClose, onSave, editData }: DepartmentFormProps) => {
  const [loading, setLoading] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    description: editData?.description || '',
    manager: editData?.manager || '',
    email: editData?.email || '',
    phone: editData?.phone || '',
    location: editData?.location || '',
    budget: editData?.budget || '',
    establishedDate: editData?.establishedDate || '',
    vision: editData?.vision || '',
    mission: editData?.mission || ''
  });

  const [responsibilities, setResponsibilities] = useState<string[]>(
    editData?.responsibilities || ['']
  );

  const [objectives, setObjectives] = useState<string[]>(
    editData?.objectives || ['']
  );

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم الإدارة مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف الإدارة مطلوب';
    }

    if (!formData.manager.trim()) {
      newErrors.manager = 'مدير الإدارة مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    }

    // التحقق من المسؤوليات
    const validResponsibilities = responsibilities.filter(r => r.trim());
    if (validResponsibilities.length === 0) {
      newErrors.responsibilities = 'يجب إضافة مسؤولية واحدة على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      const validResponsibilities = responsibilities.filter(r => r.trim());
      const validObjectives = objectives.filter(o => o.trim());

      await onSave({
        ...formData,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        responsibilities: validResponsibilities,
        objectives: validObjectives,
        id: editData?.id || `dept-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: editData?.status || 'نشط',
        employeeCount: editData?.employeeCount || 0
      });
      
      notifications.success(
        editData ? 'تم تحديث الإدارة' : 'تم إضافة الإدارة', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // إدارة المسؤوليات
  const addResponsibility = () => {
    setResponsibilities(prev => [...prev, '']);
  };

  const updateResponsibility = (index: number, value: string) => {
    setResponsibilities(prev => prev.map((r, i) => i === index ? value : r));
    if (errors.responsibilities) {
      setErrors(prev => ({ ...prev, responsibilities: '' }));
    }
  };

  const removeResponsibility = (index: number) => {
    if (responsibilities.length > 1) {
      setResponsibilities(prev => prev.filter((_, i) => i !== index));
    }
  };

  // إدارة الأهداف
  const addObjective = () => {
    setObjectives(prev => [...prev, '']);
  };

  const updateObjective = (index: number, value: string) => {
    setObjectives(prev => prev.map((o, i) => i === index ? value : o));
  };

  const removeObjective = (index: number) => {
    if (objectives.length > 1) {
      setObjectives(prev => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل الإدارة' : 'إضافة إدارة جديدة'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* اسم الإدارة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اسم الإدارة *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`input-field ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل اسم الإدارة"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف الإدارة *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للإدارة ودورها"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* مدير الإدارة ومعلومات الاتصال */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مدير الإدارة *
              </label>
              <input
                type="text"
                value={formData.manager}
                onChange={(e) => handleChange('manager', e.target.value)}
                className={`input-field ${errors.manager ? 'border-red-500' : ''}`}
                placeholder="اسم مدير الإدارة"
              />
              {errors.manager && <p className="text-red-500 text-xs mt-1">{errors.manager}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                className={`input-field ${errors.email ? 'border-red-500' : ''}`}
                placeholder="<EMAIL>"
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>
          </div>

          {/* الهاتف والموقع */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className={`input-field ${errors.phone ? 'border-red-500' : ''}`}
                placeholder="+966 50 123 4567"
              />
              {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الموقع
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleChange('location', e.target.value)}
                className="input-field"
                placeholder="مثل: الطابق الثاني، المبنى الرئيسي"
              />
            </div>
          </div>

          {/* الميزانية وتاريخ التأسيس */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الميزانية السنوية (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className="input-field"
                placeholder="0"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ التأسيس
              </label>
              <input
                type="date"
                value={formData.establishedDate}
                onChange={(e) => handleChange('establishedDate', e.target.value)}
                className="input-field"
              />
            </div>
          </div>

          {/* الرؤية والرسالة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الرؤية
              </label>
              <textarea
                value={formData.vision}
                onChange={(e) => handleChange('vision', e.target.value)}
                rows={2}
                className="input-field"
                placeholder="رؤية الإدارة المستقبلية"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الرسالة
              </label>
              <textarea
                value={formData.mission}
                onChange={(e) => handleChange('mission', e.target.value)}
                rows={2}
                className="input-field"
                placeholder="رسالة الإدارة وغرضها"
              />
            </div>
          </div>

          {/* المسؤوليات */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                المسؤوليات الرئيسية *
              </label>
              <button
                type="button"
                onClick={addResponsibility}
                className="text-primary-600 hover:text-primary-700 text-sm flex items-center gap-1"
              >
                <Plus className="w-3 h-3" />
                إضافة مسؤولية
              </button>
            </div>
            <div className="space-y-2">
              {responsibilities.map((responsibility, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={responsibility}
                    onChange={(e) => updateResponsibility(index, e.target.value)}
                    className="input-field flex-1"
                    placeholder={`المسؤولية ${index + 1}`}
                  />
                  {responsibilities.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeResponsibility(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
            {errors.responsibilities && <p className="text-red-500 text-xs mt-1">{errors.responsibilities}</p>}
          </div>

          {/* الأهداف */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                الأهداف الفرعية
              </label>
              <button
                type="button"
                onClick={addObjective}
                className="text-primary-600 hover:text-primary-700 text-sm flex items-center gap-1"
              >
                <Plus className="w-3 h-3" />
                إضافة هدف
              </button>
            </div>
            <div className="space-y-2">
              {objectives.map((objective, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={objective}
                    onChange={(e) => updateObjective(index, e.target.value)}
                    className="input-field flex-1"
                    placeholder={`الهدف ${index + 1}`}
                  />
                  {objectives.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeObjective(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ الإدارة'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DepartmentForm;
