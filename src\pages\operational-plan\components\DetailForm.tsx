import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save, X, Plus, Trash2 } from 'lucide-react';
import { useQuickNotifications } from '../../../components/ui/NotificationSystem';

interface DetailFormProps {
  type: 'objective' | 'kpi' | 'project' | 'activity';
  data?: any;
  onSave: (data: any) => Promise<void>;
  onCancel: () => void;
  objectives?: any[]; // للربط في KPIs
  projects?: any[]; // للربط في الأنشطة
  isDarkMode: boolean;
}

const DetailForm = ({ type, data, onSave, onCancel, objectives = [], projects = [], isDarkMode }: DetailFormProps) => {
  const notifications = useQuickNotifications();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Form data based on type
  const getInitialFormData = () => {
    switch (type) {
      case 'objective':
        return {
          title: data?.title || '',
          description: data?.description || '',
          owner: data?.owner || '',
          manager: data?.manager || '',
          priority: data?.priority || 'متوسطة',
          startDate: data?.startDate || '',
          endDate: data?.endDate || '',
          budget: data?.budget || '',
          department: data?.department || '',
          kpis: data?.kpis || [''],
          team: data?.team || [''],
          status: data?.status || 'جديد'
        };
      
      case 'kpi':
        return {
          name: data?.name || '',
          description: data?.description || '',
          goal: data?.goal || '',
          target: data?.target || '',
          current: data?.current || '',
          unit: data?.unit || '%',
          frequency: data?.frequency || 'شهري',
          responsible: data?.responsible || '',
          status: data?.status || 'جاري',
          dataSource: data?.dataSource || '',
          formula: data?.formula || ''
        };
      
      case 'project':
        return {
          title: data?.title || '',
          description: data?.description || '',
          goal: data?.goal || '',
          owner: data?.owner || '',
          manager: data?.manager || '',
          status: data?.status || 'التخطيط',
          priority: data?.priority || 'متوسطة',
          startDate: data?.startDate || '',
          endDate: data?.endDate || '',
          budget: data?.budget || '',
          spent: data?.spent || '',
          team: data?.team || [''],
          activities: data?.activities || [''],
          risks: data?.risks || [{ risk: '', probability: 'متوسطة', impact: 'متوسط' }],
          deliverables: data?.deliverables || [{ name: '', status: 'مجدول', date: '' }]
        };
      
      case 'activity':
        return {
          title: data?.title || '',
          project: data?.project || '',
          assignee: data?.assignee || '',
          status: data?.status || 'مجدول',
          priority: data?.priority || 'متوسطة',
          startDate: data?.startDate || '',
          endDate: data?.endDate || '',
          department: data?.department || '',
          description: data?.description || '',
          estimatedHours: data?.estimatedHours || '',
          actualHours: data?.actualHours || '',
          dependencies: data?.dependencies || ['']
        };
      
      default:
        return {};
    }
  };

  const [formData, setFormData] = useState(getInitialFormData());

  useEffect(() => {
    setFormData(getInitialFormData());
    setErrors({});
  }, [data, type]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Common validations
    if (type !== 'kpi' && !formData.title?.trim()) {
      newErrors.title = 'العنوان مطلوب';
    }
    if (type === 'kpi' && !formData.name?.trim()) {
      newErrors.name = 'اسم المؤشر مطلوب';
    }
    if (!formData.description?.trim()) {
      newErrors.description = 'الوصف مطلوب';
    }

    // Type-specific validations
    switch (type) {
      case 'objective':
        if (!formData.owner?.trim()) newErrors.owner = 'الإدارة المسؤولة مطلوبة';
        if (!formData.manager?.trim()) newErrors.manager = 'المدير مطلوب';
        break;
      
      case 'kpi':
        if (!formData.goal?.trim()) newErrors.goal = 'الهدف الاستراتيجي مطلوب';
        if (!formData.target?.trim()) newErrors.target = 'القيمة المستهدفة مطلوبة';
        if (!formData.current?.trim()) newErrors.current = 'القيمة الحالية مطلوبة';
        if (!formData.responsible?.trim()) newErrors.responsible = 'المسؤول مطلوب';
        break;
      
      case 'project':
        if (!formData.goal?.trim()) newErrors.goal = 'الهدف مطلوب';
        if (!formData.owner?.trim()) newErrors.owner = 'الإدارة المسؤولة مطلوبة';
        if (!formData.manager?.trim()) newErrors.manager = 'المدير مطلوب';
        break;
      
      case 'activity':
        if (!formData.project?.trim()) newErrors.project = 'المشروع مطلوب';
        if (!formData.assignee?.trim()) newErrors.assignee = 'المسؤول مطلوب';
        break;
    }

    // Date validations
    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      const dataToSave = {
        ...formData,
        progress: data?.progress || 0,
        id: data?.id || `${type}-${Date.now()}`,
        createdAt: data?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Clean up arrays
      if (formData.kpis) dataToSave.kpis = formData.kpis.filter((k: string) => k.trim());
      if (formData.team) dataToSave.team = formData.team.filter((m: string) => m.trim());
      if (formData.activities) dataToSave.activities = formData.activities.filter((a: string) => a.trim());
      if (formData.dependencies) dataToSave.dependencies = formData.dependencies.filter((d: string) => d.trim());

      await onSave(dataToSave);

      notifications.success(
        data ? 'تم التحديث' : 'تم الإضافة',
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  // Dynamic array management functions
  const addArrayItem = (field: string, defaultValue: any = '') => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: [...(prev[field] || []), defaultValue]
    }));
  };

  const updateArrayItem = (field: string, index: number, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: prev[field].map((item: any, i: number) => i === index ? value : item)
    }));
  };

  const removeArrayItem = (field: string, index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: prev[field].filter((_: any, i: number) => i !== index)
    }));
  };

  // Theme classes
  const inputClass = `w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-1 ${
    isDarkMode 
      ? 'bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-gray-600 focus:ring-blue-500' 
      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500'
  }`;

  const labelClass = `block text-xs font-medium mb-0.5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`;
  const errorClass = 'text-red-500 text-xs mt-0.5';
  const sectionClass = `p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-white'} rounded-lg border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`;

  const renderObjectiveForm = () => (
    <>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>العنوان *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className={`${inputClass} ${errors.title ? 'border-red-500' : ''}`}
          />
          {errors.title && <p className={errorClass}>{errors.title}</p>}
        </div>

        <div>
          <label className={labelClass}>الحالة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            className={inputClass}
          >
            <option value="جديد">جديد</option>
            <option value="جاري">جاري</option>
            <option value="مكتمل">مكتمل</option>
            <option value="معلق">معلق</option>
            <option value="متأخر">متأخر</option>
          </select>
        </div>
      </div>

      <div>
        <label className={labelClass}>الوصف *</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={2}
          className={`${inputClass} ${errors.description ? 'border-red-500' : ''}`}
        />
        {errors.description && <p className={errorClass}>{errors.description}</p>}
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>الإدارة المسؤولة *</label>
          <input
            type="text"
            value={formData.owner}
            onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
            className={`${inputClass} ${errors.owner ? 'border-red-500' : ''}`}
          />
          {errors.owner && <p className={errorClass}>{errors.owner}</p>}
        </div>

        <div>
          <label className={labelClass}>المدير المسؤول *</label>
          <input
            type="text"
            value={formData.manager}
            onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
            className={`${inputClass} ${errors.manager ? 'border-red-500' : ''}`}
          />
          {errors.manager && <p className={errorClass}>{errors.manager}</p>}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <label className={labelClass}>الأولوية</label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
            className={inputClass}
          >
            <option value="عالية">عالية</option>
            <option value="متوسطة">متوسطة</option>
            <option value="منخفضة">منخفضة</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>تاريخ البداية</label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>تاريخ النهاية</label>
          <input
            type="date"
            value={formData.endDate}
            onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
            className={`${inputClass} ${errors.endDate ? 'border-red-500' : ''}`}
          />
          {errors.endDate && <p className={errorClass}>{errors.endDate}</p>}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>الميزانية</label>
          <input
            type="text"
            value={formData.budget}
            onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
            placeholder="مثال: 500,000 ريال"
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>القسم</label>
          <input
            type="text"
            value={formData.department}
            onChange={(e) => setFormData({ ...formData, department: e.target.value })}
            className={inputClass}
          />
        </div>
      </div>

      {/* مؤشرات الأداء */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>مؤشرات الأداء المرتبطة</label>
          <button
            type="button"
            onClick={() => addArrayItem('kpis')}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.kpis?.map((kpi: string, index: number) => (
          <div key={index} className="flex gap-2 mb-1">
            <input
              type="text"
              value={kpi}
              onChange={(e) => updateArrayItem('kpis', index, e.target.value)}
              placeholder="اسم المؤشر"
              className={`flex-1 ${inputClass}`}
            />
            {formData.kpis.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem('kpis', index)}
                className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* فريق العمل */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>فريق العمل</label>
          <button
            type="button"
            onClick={() => addArrayItem('team')}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.team?.map((member: string, index: number) => (
          <div key={index} className="flex gap-2 mb-1">
            <input
              type="text"
              value={member}
              onChange={(e) => updateArrayItem('team', index, e.target.value)}
              placeholder="اسم العضو"
              className={`flex-1 ${inputClass}`}
            />
            {formData.team.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem('team', index)}
                className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        ))}
      </div>
    </>
  );

  const renderKPIForm = () => (
    <>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>اسم المؤشر *</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className={`${inputClass} ${errors.name ? 'border-red-500' : ''}`}
          />
          {errors.name && <p className={errorClass}>{errors.name}</p>}
        </div>

        <div>
          <label className={labelClass}>الهدف الاستراتيجي *</label>
          {objectives.length > 0 ? (
            <select
              value={formData.goal}
              onChange={(e) => setFormData({ ...formData, goal: e.target.value })}
              className={`${inputClass} ${errors.goal ? 'border-red-500' : ''}`}
            >
              <option value="">اختر الهدف</option>
              {objectives.map((obj) => (
                <option key={obj.id} value={obj.title}>
                  {obj.title}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              value={formData.goal}
              onChange={(e) => setFormData({ ...formData, goal: e.target.value })}
              className={`${inputClass} ${errors.goal ? 'border-red-500' : ''}`}
            />
          )}
          {errors.goal && <p className={errorClass}>{errors.goal}</p>}
        </div>
      </div>

      <div>
        <label className={labelClass}>الوصف *</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={2}
          className={`${inputClass} ${errors.description ? 'border-red-500' : ''}`}
        />
        {errors.description && <p className={errorClass}>{errors.description}</p>}
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <label className={labelClass}>القيمة المستهدفة *</label>
          <input
            type="text"
            value={formData.target}
            onChange={(e) => setFormData({ ...formData, target: e.target.value })}
            placeholder="مثال: 95%"
            className={`${inputClass} ${errors.target ? 'border-red-500' : ''}`}
          />
          {errors.target && <p className={errorClass}>{errors.target}</p>}
        </div>

        <div>
          <label className={labelClass}>القيمة الحالية *</label>
          <input
            type="text"
            value={formData.current}
            onChange={(e) => setFormData({ ...formData, current: e.target.value })}
            placeholder="مثال: 87%"
            className={`${inputClass} ${errors.current ? 'border-red-500' : ''}`}
          />
          {errors.current && <p className={errorClass}>{errors.current}</p>}
        </div>

        <div>
          <label className={labelClass}>وحدة القياس</label>
          <select
            value={formData.unit}
            onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
            className={inputClass}
          >
            <option value="%">نسبة مئوية (%)</option>
            <option value="عدد">عدد</option>
            <option value="ريال">ريال</option>
            <option value="يوم">يوم</option>
            <option value="ساعة">ساعة</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <label className={labelClass}>التكرار</label>
          <select
            value={formData.frequency}
            onChange={(e) => setFormData({ ...formData, frequency: e.target.value })}
            className={inputClass}
          >
            <option value="يومي">يومي</option>
            <option value="أسبوعي">أسبوعي</option>
            <option value="شهري">شهري</option>
            <option value="ربع سنوي">ربع سنوي</option>
            <option value="سنوي">سنوي</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>المسؤول *</label>
          <input
            type="text"
            value={formData.responsible}
            onChange={(e) => setFormData({ ...formData, responsible: e.target.value })}
            className={`${inputClass} ${errors.responsible ? 'border-red-500' : ''}`}
          />
          {errors.responsible && <p className={errorClass}>{errors.responsible}</p>}
        </div>

        <div>
          <label className={labelClass}>الحالة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            className={inputClass}
          >
            <option value="جاري">جاري</option>
            <option value="محقق">محقق</option>
            <option value="متأخر">متأخر</option>
            <option value="تحت المراقبة">تحت المراقبة</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>مصدر البيانات</label>
          <input
            type="text"
            value={formData.dataSource}
            onChange={(e) => setFormData({ ...formData, dataSource: e.target.value })}
            placeholder="مثال: النظام المالي"
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>طريقة الحساب</label>
          <input
            type="text"
            value={formData.formula}
            onChange={(e) => setFormData({ ...formData, formula: e.target.value })}
            placeholder="مثال: (القيمة الحالية / القيمة السابقة) × 100"
            className={inputClass}
          />
        </div>
      </div>
    </>
  );

  const renderProjectForm = () => (
    <>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>عنوان المشروع *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className={`${inputClass} ${errors.title ? 'border-red-500' : ''}`}
          />
          {errors.title && <p className={errorClass}>{errors.title}</p>}
        </div>

        <div>
          <label className={labelClass}>الهدف الاستراتيجي *</label>
          {objectives.length > 0 ? (
            <select
              value={formData.goal}
              onChange={(e) => setFormData({ ...formData, goal: e.target.value })}
              className={`${inputClass} ${errors.goal ? 'border-red-500' : ''}`}
            >
              <option value="">اختر الهدف</option>
              {objectives.map((obj) => (
                <option key={obj.id} value={obj.title}>
                  {obj.title}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              value={formData.goal}
              onChange={(e) => setFormData({ ...formData, goal: e.target.value })}
              className={`${inputClass} ${errors.goal ? 'border-red-500' : ''}`}
            />
          )}
          {errors.goal && <p className={errorClass}>{errors.goal}</p>}
        </div>
      </div>

      <div>
        <label className={labelClass}>الوصف *</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={2}
          className={`${inputClass} ${errors.description ? 'border-red-500' : ''}`}
        />
        {errors.description && <p className={errorClass}>{errors.description}</p>}
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>الإدارة المسؤولة *</label>
          <input
            type="text"
            value={formData.owner}
            onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
            className={`${inputClass} ${errors.owner ? 'border-red-500' : ''}`}
          />
          {errors.owner && <p className={errorClass}>{errors.owner}</p>}
        </div>

        <div>
          <label className={labelClass}>مدير المشروع *</label>
          <input
            type="text"
            value={formData.manager}
            onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
            className={`${inputClass} ${errors.manager ? 'border-red-500' : ''}`}
          />
          {errors.manager && <p className={errorClass}>{errors.manager}</p>}
        </div>
      </div>

      <div className="grid grid-cols-4 gap-3">
        <div>
          <label className={labelClass}>الحالة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            className={inputClass}
          >
            <option value="التخطيط">التخطيط</option>
            <option value="جاري">جاري</option>
            <option value="مكتمل">مكتمل</option>
            <option value="معلق">معلق</option>
            <option value="ملغي">ملغي</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>الأولوية</label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
            className={inputClass}
          >
            <option value="عالية">عالية</option>
            <option value="متوسطة">متوسطة</option>
            <option value="منخفضة">منخفضة</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>تاريخ البداية</label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>تاريخ النهاية</label>
          <input
            type="date"
            value={formData.endDate}
            onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
            className={`${inputClass} ${errors.endDate ? 'border-red-500' : ''}`}
          />
          {errors.endDate && <p className={errorClass}>{errors.endDate}</p>}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>الميزانية المخصصة</label>
          <input
            type="text"
            value={formData.budget}
            onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
            placeholder="مثال: 600,000 ريال"
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>المبلغ المصروف</label>
          <input
            type="text"
            value={formData.spent}
            onChange={(e) => setFormData({ ...formData, spent: e.target.value })}
            placeholder="مثال: 420,000 ريال"
            className={inputClass}
          />
        </div>
      </div>

      {/* فريق العمل */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>فريق العمل</label>
          <button
            type="button"
            onClick={() => addArrayItem('team')}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.team?.map((member: string, index: number) => (
          <div key={index} className="flex gap-2 mb-1">
            <input
              type="text"
              value={member}
              onChange={(e) => updateArrayItem('team', index, e.target.value)}
              placeholder="اسم العضو"
              className={`flex-1 ${inputClass}`}
            />
            {formData.team.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem('team', index)}
                className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* الأنشطة */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>الأنشطة الرئيسية</label>
          <button
            type="button"
            onClick={() => addArrayItem('activities')}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.activities?.map((activity: string, index: number) => (
          <div key={index} className="flex gap-2 mb-1">
            <input
              type="text"
              value={activity}
              onChange={(e) => updateArrayItem('activities', index, e.target.value)}
              placeholder="وصف النشاط"
              className={`flex-1 ${inputClass}`}
            />
            {formData.activities.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem('activities', index)}
                className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* المخاطر */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>المخاطر</label>
          <button
            type="button"
            onClick={() => addArrayItem('risks', { risk: '', probability: 'متوسطة', impact: 'متوسط' })}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.risks?.map((riskItem: any, index: number) => (
          <div key={index} className={`p-2 mb-1 rounded-md ${isDarkMode ? 'bg-gray-900/50' : 'bg-gray-50'}`}>
            <div className="grid grid-cols-3 gap-2">
              <input
                type="text"
                value={riskItem.risk}
                onChange={(e) => updateArrayItem('risks', index, { ...riskItem, risk: e.target.value })}
                placeholder="وصف المخاطرة"
                className={`col-span-1 md:col-span-3 ${inputClass}`}
              />
              <select
                value={riskItem.probability}
                onChange={(e) => updateArrayItem('risks', index, { ...riskItem, probability: e.target.value })}
                className={inputClass}
              >
                <option value="منخفضة">احتمالية منخفضة</option>
                <option value="متوسطة">احتمالية متوسطة</option>
                <option value="عالية">احتمالية عالية</option>
              </select>
              <select
                value={riskItem.impact}
                onChange={(e) => updateArrayItem('risks', index, { ...riskItem, impact: e.target.value })}
                className={inputClass}
              >
                <option value="منخفض">تأثير منخفض</option>
                <option value="متوسط">تأثير متوسط</option>
                <option value="عالي">تأثير عالي</option>
              </select>
              {formData.risks.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeArrayItem('risks', index)}
                  className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </>
  );

  const renderActivityForm = () => (
    <>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>عنوان النشاط *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className={`${inputClass} ${errors.title ? 'border-red-500' : ''}`}
          />
          {errors.title && <p className={errorClass}>{errors.title}</p>}
        </div>

        <div>
          <label className={labelClass}>المشروع *</label>
          {projects.length > 0 ? (
            <select
              value={formData.project}
              onChange={(e) => setFormData({ ...formData, project: e.target.value })}
              className={`${inputClass} ${errors.project ? 'border-red-500' : ''}`}
            >
              <option value="">اختر المشروع</option>
              {projects.map((proj) => (
                <option key={proj.id} value={proj.title}>
                  {proj.title}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              value={formData.project}
              onChange={(e) => setFormData({ ...formData, project: e.target.value })}
              className={`${inputClass} ${errors.project ? 'border-red-500' : ''}`}
            />
          )}
          {errors.project && <p className={errorClass}>{errors.project}</p>}
        </div>
      </div>

      <div>
        <label className={labelClass}>الوصف *</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={2}
          className={`${inputClass} ${errors.description ? 'border-red-500' : ''}`}
        />
        {errors.description && <p className={errorClass}>{errors.description}</p>}
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>المسؤول عن التنفيذ *</label>
          <input
            type="text"
            value={formData.assignee}
            onChange={(e) => setFormData({ ...formData, assignee: e.target.value })}
            className={`${inputClass} ${errors.assignee ? 'border-red-500' : ''}`}
          />
          {errors.assignee && <p className={errorClass}>{errors.assignee}</p>}
        </div>

        <div>
          <label className={labelClass}>القسم</label>
          <input
            type="text"
            value={formData.department}
            onChange={(e) => setFormData({ ...formData, department: e.target.value })}
            className={inputClass}
          />
        </div>
      </div>

      <div className="grid grid-cols-4 gap-3">
        <div>
          <label className={labelClass}>الحالة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            className={inputClass}
          >
            <option value="مجدول">مجدول</option>
            <option value="جاري">جاري</option>
            <option value="مكتمل">مكتمل</option>
            <option value="متأخر">متأخر</option>
            <option value="معلق">معلق</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>الأولوية</label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
            className={inputClass}
          >
            <option value="عالية">عالية</option>
            <option value="متوسطة">متوسطة</option>
            <option value="منخفضة">منخفضة</option>
          </select>
        </div>

        <div>
          <label className={labelClass}>تاريخ البداية</label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>تاريخ النهاية</label>
          <input
            type="date"
            value={formData.endDate}
            onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
            className={`${inputClass} ${errors.endDate ? 'border-red-500' : ''}`}
          />
          {errors.endDate && <p className={errorClass}>{errors.endDate}</p>}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className={labelClass}>الساعات المقدرة</label>
          <input
            type="number"
            value={formData.estimatedHours}
            onChange={(e) => setFormData({ ...formData, estimatedHours: e.target.value })}
            placeholder="عدد الساعات"
            className={inputClass}
          />
        </div>

        <div>
          <label className={labelClass}>الساعات الفعلية</label>
          <input
            type="number"
            value={formData.actualHours}
            onChange={(e) => setFormData({ ...formData, actualHours: e.target.value })}
            placeholder="عدد الساعات"
            className={inputClass}
          />
        </div>
      </div>

      {/* الاعتماديات */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label className={labelClass}>الاعتماديات</label>
          <button
            type="button"
            onClick={() => addArrayItem('dependencies')}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
          >
            <Plus size={14} /> إضافة
          </button>
        </div>
        {formData.dependencies?.map((dep: string, index: number) => (
          <div key={index} className="flex gap-2 mb-1">
            <input
              type="text"
              value={dep}
              onChange={(e) => updateArrayItem('dependencies', index, e.target.value)}
              placeholder="النشاط المعتمد عليه"
              className={`flex-1 ${inputClass}`}
            />
            {formData.dependencies.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem('dependencies', index)}
                className={`p-1.5 rounded-md ${isDarkMode ? 'text-red-400 hover:bg-red-900/20' : 'text-red-600 hover:bg-red-50'}`}
              >
                <Trash2 size={14} />
              </button>
            )}
          </div>
        ))}
      </div>
    </>
  );

  return (
    <form onSubmit={handleSubmit} className="h-full flex flex-col">
      {/* Header */}
      <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={onCancel}
              className={`p-1.5 rounded-md ${isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
            >
              <ArrowLeft size={16} className="rotate-180" />
            </button>
            <h2 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {data ? 'تعديل' : 'إضافة'} {
                type === 'objective' ? 'هدف استراتيجي' :
                type === 'kpi' ? 'مؤشر أداء' :
                type === 'project' ? 'مشروع' :
                'نشاط'
              }
            </h2>
          </div>
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={onCancel}
              className={`px-3 py-1.5 text-sm rounded-md ${
                isDarkMode 
                  ? 'text-gray-300 bg-gray-800 hover:bg-gray-700' 
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
              }`}
              disabled={loading}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2 disabled:opacity-50"
              disabled={loading}
            >
              <Save size={14} />
              {loading ? 'جاري الحفظ...' : data ? 'تحديث' : 'حفظ'}
            </button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className={sectionClass}>
          <div className="space-y-4">
            {type === 'objective' && renderObjectiveForm()}
            {type === 'kpi' && renderKPIForm()}
            {type === 'project' && renderProjectForm()}
            {type === 'activity' && renderActivityForm()}
          </div>
        </div>
      </div>
    </form>
  );
};

export default DetailForm;