import { useState, useEffect } from 'react';

interface KpiGaugeProps {
  currentValue: number;
  targetValue: number;
  title?: string;
  unit?: string;
  direction?: 'INCREASING' | 'DECREASING';
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  animated?: boolean;
}

const KpiGauge = ({ 
  currentValue, 
  targetValue, 
  title, 
  unit = '', 
  direction = 'INCREASING',
  size = 'md',
  showLabels = true,
  animated = true
}: KpiGaugeProps) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  // حساب نسبة الأداء
  let performanceRatio;
  if (direction === 'INCREASING') {
    performanceRatio = targetValue === 0 ? 1 : currentValue / targetValue;
  } else {
    // للمؤشرات التنازلية (كلما قل كان أفضل)
    performanceRatio = targetValue === 0 ? 1 : Math.min(2 - (currentValue / targetValue), 1);
  }

  // تحديد اللون بناءً على الأداء
  const getColor = (ratio: number) => {
    if (ratio >= 1) return '#34A853'; // أخضر (تجاوز المستهدف)
    if (ratio >= 0.75) return '#4285F4'; // أزرق (على المسار الصحيح)
    if (ratio >= 0.5) return '#FBBC05'; // أصفر (في خطر)
    return '#EA4335'; // أحمر (دون المستهدف)
  };

  const color = getColor(performanceRatio);
  const percentage = Math.min(Math.max(performanceRatio * 100, 0), 100);

  // تحديد الأحجام
  const sizeConfig = {
    sm: { 
      width: 80, 
      height: 40, 
      strokeWidth: 6, 
      fontSize: 'text-xs',
      titleSize: 'text-xs'
    },
    md: { 
      width: 100, 
      height: 50, 
      strokeWidth: 8, 
      fontSize: 'text-sm',
      titleSize: 'text-sm'
    },
    lg: { 
      width: 120, 
      height: 60, 
      strokeWidth: 10, 
      fontSize: 'text-base',
      titleSize: 'text-base'
    }
  };

  const config = sizeConfig[size];
  const radius = (config.width - config.strokeWidth) / 2;
  const circumference = Math.PI * radius; // نصف دائرة
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (animatedProgress / 100) * circumference;

  // تحريك التقدم
  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setAnimatedProgress(percentage);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setAnimatedProgress(percentage);
    }
  }, [percentage, animated]);

  // تحديد حالة الأداء
  const getPerformanceStatus = () => {
    if (performanceRatio >= 1) return { text: 'متجاوز للمستهدف', color: 'text-green-600' };
    if (performanceRatio >= 0.75) return { text: 'على المسار الصحيح', color: 'text-blue-600' };
    if (performanceRatio >= 0.5) return { text: 'في خطر', color: 'text-yellow-600' };
    return { text: 'دون المستهدف', color: 'text-red-600' };
  };

  const status = getPerformanceStatus();

  return (
    <div className="flex flex-col items-center">
      {title && showLabels && (
        <h4 className={`font-medium mb-2 text-center ${config.titleSize}`}>{title}</h4>
      )}
      
      <div className="relative" style={{ width: config.width, height: config.height }}>
        <svg 
          width={config.width} 
          height={config.height} 
          viewBox={`0 0 ${config.width} ${config.height}`}
          className="transform -rotate-90"
        >
          {/* الخلفية */}
          <path
            d={`M ${config.strokeWidth / 2} ${config.height - config.strokeWidth / 2} 
                A ${radius} ${radius} 0 0 1 ${config.width - config.strokeWidth / 2} ${config.height - config.strokeWidth / 2}`}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth={config.strokeWidth}
            strokeLinecap="round"
          />
          
          {/* شريط التقدم */}
          <path
            d={`M ${config.strokeWidth / 2} ${config.height - config.strokeWidth / 2} 
                A ${radius} ${radius} 0 0 1 ${config.width - config.strokeWidth / 2} ${config.height - config.strokeWidth / 2}`}
            fill="none"
            stroke={color}
            strokeWidth={config.strokeWidth}
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className={animated ? 'transition-all duration-1000 ease-out' : ''}
          />
        </svg>
        
        {/* النص المركزي */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className={`font-bold ${config.fontSize}`} style={{ color }}>
            {Math.round(animatedProgress)}%
          </div>
          {showLabels && (
            <div className="text-xs text-gray-500 text-center">
              {currentValue.toLocaleString('ar-SA')} {unit}
            </div>
          )}
        </div>
      </div>
      
      {showLabels && (
        <div className="mt-2 text-center space-y-1">
          <div className="text-xs text-gray-600">
            المستهدف: {targetValue.toLocaleString('ar-SA')} {unit}
          </div>
          <div className={`text-xs font-medium ${status.color}`}>
            {status.text}
          </div>
        </div>
      )}
    </div>
  );
};

// مكون مبسط لعرض مؤشر الأداء في البطاقات
export const KpiMiniGauge = ({ 
  currentValue, 
  targetValue, 
  unit = '', 
  direction = 'INCREASING' 
}: Pick<KpiGaugeProps, 'currentValue' | 'targetValue' | 'unit' | 'direction'>) => {
  return (
    <KpiGauge
      currentValue={currentValue}
      targetValue={targetValue}
      unit={unit}
      direction={direction}
      size="sm"
      showLabels={false}
      animated={false}
    />
  );
};

export default KpiGauge;
