import { supabase } from './supabase';

export interface Department {
  id: string;
  name: string;
  description?: string;
  manager?: string;
  email?: string;
  phone?: string;
  employee_count: number;
  budget: string;
  status: string;
  parent_id?: string;
  created_at: string;
  updated_at: string;
}

export class DepartmentService {
  // جلب جميع الأقسام
  static async getAllDepartments(): Promise<Department[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  // إنشاء قسم جديد
  static async createDepartment(departmentData: {
    name: string;
    description?: string;
    manager?: string;
    email?: string;
    phone?: string;
    parent_id?: string;
  }): Promise<Department> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .insert({
          name: departmentData.name,
          description: departmentData.description,
          manager: departmentData.manager,
          email: departmentData.email,
          phone: departmentData.phone,
          parent_id: departmentData.parent_id,
          employee_count: 0,
          budget: '0',
          status: 'نشط'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating department:', error);
      throw error;
    }
  }

  // تحديث قسم
  static async updateDepartment(departmentId: string, updates: Partial<Department>): Promise<Department> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', departmentId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating department:', error);
      throw error;
    }
  }

  // حذف قسم
  static async deleteDepartment(departmentId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('departments')
        .delete()
        .eq('id', departmentId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting department:', error);
      throw error;
    }
  }

  // جلب قسم واحد
  static async getDepartmentById(departmentId: string): Promise<Department | null> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .eq('id', departmentId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching department:', error);
      return null;
    }
  }

  // جلب الأقسام الفرعية
  static async getSubDepartments(parentId: string): Promise<Department[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .eq('parent_id', parentId)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching sub-departments:', error);
      throw error;
    }
  }

  // جلب أسماء الإدارات فقط (للقوائم المنسدلة)
  static async getDepartmentNames(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('name')
        .eq('status', 'نشط')
        .order('name');

      if (error) throw error;
      return data?.map(dept => dept.name) || [];
    } catch (error) {
      console.error('Error fetching department names:', error);
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'قسم البحث والابتكار',
        'قسم التسويق والمبيعات',
        'قسم التطوير والهندسة2',
        'قسم العمليات وخدمة العملاء1',
        'قسم المالية والإدارة'
      ];
    }
  }

  // إحصائيات الأقسام
  static async getDepartmentStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    totalEmployees: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('status, employee_count');

      if (error) throw error;

      const stats = {
        total: data?.length || 0,
        active: data?.filter(d => d.status === 'نشط').length || 0,
        inactive: data?.filter(d => d.status !== 'نشط').length || 0,
        totalEmployees: data?.reduce((sum, d) => sum + (d.employee_count || 0), 0) || 0
      };

      return stats;
    } catch (error) {
      console.error('Error fetching department stats:', error);
      return { total: 0, active: 0, inactive: 0, totalEmployees: 0 };
    }
  }
}
