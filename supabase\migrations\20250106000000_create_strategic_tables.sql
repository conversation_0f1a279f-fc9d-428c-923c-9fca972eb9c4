/*
  # Strategic Planning Tables

  1. Create tables for strategic planning system
    - strategic_objectives: Strategic objectives 
    - kpis: Key Performance Indicators
    - initiatives: Projects/Initiatives
    - activities: Operational activities
    - objective_kpis: Many-to-many relationship between objectives and KPIs
    
  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Create strategic_objectives table
CREATE TABLE IF NOT EXISTS strategic_objectives (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  owner text NOT NULL,
  department text NOT NULL,
  priority text DEFAULT 'متوسطة' CHECK (priority IN ('عالية', 'متوسطة', 'منخفضة')),
  status text DEFAULT 'جديد' CHECK (status IN ('جديد', 'جاري', 'مكتمل', 'متأخر', 'معلق')),
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  start_date date,
  end_date date,
  budget numeric DEFAULT 0,
  expected_outcome text,
  success_metrics text,
  manager text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create KPIs table
CREATE TABLE IF NOT EXISTS kpis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  category text NOT NULL,
  objective text,
  related_project text,
  current_value numeric DEFAULT 0,
  target_value numeric NOT NULL,
  unit text NOT NULL,
  trend text DEFAULT 'up' CHECK (trend IN ('up', 'down', 'stable')),
  change_percentage numeric DEFAULT 0,
  status text DEFAULT 'متوسط' CHECK (status IN ('ممتاز', 'جيد', 'متوسط', 'يحتاج تحسين')),
  description text,
  frequency text DEFAULT 'شهري' CHECK (frequency IN ('يومي', 'أسبوعي', 'شهري', 'ربع سنوي', 'سنوي')),
  data_source text,
  formula text,
  responsible text NOT NULL,
  last_updated date DEFAULT CURRENT_DATE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create initiatives/projects table
CREATE TABLE IF NOT EXISTS initiatives (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  objective text,
  related_kpi text,
  department text NOT NULL,
  manager text NOT NULL,
  priority text DEFAULT 'متوسطة' CHECK (priority IN ('عالية', 'متوسطة', 'منخفضة')),
  status text DEFAULT 'جديدة' CHECK (status IN ('جديدة', 'جاري التنفيذ', 'مكتملة', 'متأخرة', 'التخطيط', 'معلقة')),
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  start_date date,
  end_date date,
  budget numeric DEFAULT 0,
  participants integer DEFAULT 0,
  expected_outcome text,
  success_metrics text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create activities table
CREATE TABLE IF NOT EXISTS activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  initiative text,
  related_project text,
  department text NOT NULL,
  assignee text NOT NULL,
  status text DEFAULT 'لم يبدأ' CHECK (status IN ('لم يبدأ', 'جاري التنفيذ', 'مكتمل', 'متأخر', 'متعثر')),
  priority text DEFAULT 'متوسطة' CHECK (priority IN ('عالية', 'متوسطة', 'منخفضة')),
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  start_date date,
  end_date date,
  budget numeric DEFAULT 0,
  participants integer DEFAULT 0,
  estimated_hours integer DEFAULT 0,
  actual_hours integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create many-to-many relationship table for objectives and KPIs
CREATE TABLE IF NOT EXISTS objective_kpis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  objective_id uuid REFERENCES strategic_objectives(id) ON DELETE CASCADE,
  kpi_name text NOT NULL,
  created_at timestamptz DEFAULT now(),
  UNIQUE(objective_id, kpi_name)
);

-- Enable Row Level Security
ALTER TABLE strategic_objectives ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE initiatives ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE objective_kpis ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Authenticated users can read strategic_objectives"
  ON strategic_objectives
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert strategic_objectives"
  ON strategic_objectives
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update strategic_objectives"
  ON strategic_objectives
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete strategic_objectives"
  ON strategic_objectives
  FOR DELETE
  TO authenticated
  USING (true);

-- KPIs policies
CREATE POLICY "Authenticated users can read kpis"
  ON kpis
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert kpis"
  ON kpis
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update kpis"
  ON kpis
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete kpis"
  ON kpis
  FOR DELETE
  TO authenticated
  USING (true);

-- Initiatives policies
CREATE POLICY "Authenticated users can read initiatives"
  ON initiatives
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert initiatives"
  ON initiatives
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update initiatives"
  ON initiatives
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete initiatives"
  ON initiatives
  FOR DELETE
  TO authenticated
  USING (true);

-- Activities policies
CREATE POLICY "Authenticated users can read activities"
  ON activities
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert activities"
  ON activities
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update activities"
  ON activities
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete activities"
  ON activities
  FOR DELETE
  TO authenticated
  USING (true);

-- Objective KPIs policies
CREATE POLICY "Authenticated users can read objective_kpis"
  ON objective_kpis
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert objective_kpis"
  ON objective_kpis
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update objective_kpis"
  ON objective_kpis
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete objective_kpis"
  ON objective_kpis
  FOR DELETE
  TO authenticated
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_strategic_objectives_department ON strategic_objectives(department);
CREATE INDEX IF NOT EXISTS idx_strategic_objectives_status ON strategic_objectives(status);
CREATE INDEX IF NOT EXISTS idx_kpis_category ON kpis(category);
CREATE INDEX IF NOT EXISTS idx_kpis_status ON kpis(status);
CREATE INDEX IF NOT EXISTS idx_initiatives_department ON initiatives(department);
CREATE INDEX IF NOT EXISTS idx_initiatives_status ON initiatives(status);
CREATE INDEX IF NOT EXISTS idx_activities_department ON activities(department);
CREATE INDEX IF NOT EXISTS idx_activities_status ON activities(status);
CREATE INDEX IF NOT EXISTS idx_objective_kpis_objective_id ON objective_kpis(objective_id);