import React, { useState } from 'react';
import { Upload, Download, Trash2, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useNotifications } from '../ui/NotificationSystem';

interface ImportProgress {
  strategic_objectives: number;
  kpis: number;
  initiatives: number;
  activities: number;
  objective_kpis: number;
}

const DataImporter = () => {
  const [loading, setLoading] = useState(false);
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    strategic_objectives: 0,
    kpis: 0,
    initiatives: 0,
    activities: 0,
    objective_kpis: 0
  });
  const [importComplete, setImportComplete] = useState(false);
  const notifications = useNotifications();

  // بيانات الأهداف الاستراتيجية
  const strategicObjectives = [
    {
      title: 'تطوير مهارات الشباب التقنية والرقمية',
      description: 'تعزيز قدرات الشباب في مجال التكنولوجيا والذكاء الاصطناعي لمواكبة متطلبات المستقبل',
      owner: 'إدارة التطوير والابتكار',
      department: 'إدارة التطوير والابتكار',
      priority: 'عالية',
      status: 'جاري',
      progress: 75,
      start_date: '2025-01-01',
      end_date: '2025-12-31',
      budget: 850000,
      expected_outcome: 'تدريب 200 شاب على المهارات الرقمية وتأهيلهم لسوق العمل التقني',
      success_metrics: 'عدد المتدربين المتخرجين، نسبة النجاح في الاختبارات، معدل التوظيف',
      manager: 'أحمد محمد السالم'
    },
    {
      title: 'تمكين رواد الأعمال الشباب',
      description: 'دعم الشباب في إطلاق مشاريعهم الناشئة وتوفير البيئة المناسبة للإبداع والابتكار',
      owner: 'إدارة ريادة الأعمال',
      department: 'إدارة ريادة الأعمال',
      priority: 'عالية',
      status: 'جاري',
      progress: 60,
      start_date: '2025-02-01',
      end_date: '2025-11-30',
      budget: 1200000,
      expected_outcome: 'إطلاق 50 مشروع ناشئ وتوفير 300 فرصة عمل جديدة',
      success_metrics: 'عدد المشاريع المدعومة، معدل نجاح المشاريع، حجم الاستثمارات المجذوبة',
      manager: 'فاطمة أحمد الزهراني'
    },
    {
      title: 'تعزيز المشاركة المجتمعية للشباب',
      description: 'تشجيع الشباب على المشاركة الفاعلة في خدمة المجتمع والأعمال التطوعية',
      owner: 'إدارة البرامج الشبابية',
      department: 'إدارة البرامج الشبابية',
      priority: 'متوسطة',
      status: 'جاري',
      progress: 45,
      start_date: '2025-01-15',
      end_date: '2025-10-15',
      budget: 400000,
      expected_outcome: 'مشاركة 500 متطوع في 50 فعالية تطوعية مجتمعية',
      success_metrics: 'عدد المتطوعين النشطين، ساعات العمل التطوعي، عدد المستفيدين',
      manager: 'محمد علي القحطاني'
    },
    {
      title: 'تعزيز الهوية الوطنية والقيم المجتمعية',
      description: 'ترسيخ القيم الوطنية والثقافية في نفوس الشباب والحفاظ على التراث',
      owner: 'الإدارة الثقافية',
      department: 'الإدارة الثقافية',
      priority: 'عالية',
      status: 'جديد',
      progress: 25,
      start_date: '2025-03-01',
      end_date: '2025-12-31',
      budget: 600000,
      expected_outcome: 'تنظيم 20 فعالية ثقافية وتراثية وتدريب 100 سفير ثقافي',
      success_metrics: 'عدد الفعاليات الثقافية، نسبة مشاركة الشباب، مستوى الوعي الثقافي',
      manager: 'سارة خالد العتيبي'
    }
  ];

  // بيانات مؤشرات الأداء
  const kpis = [
    {
      name: 'نسبة رضا المستفيدين',
      category: 'جودة الخدمة',
      objective: 'تطوير مهارات الشباب التقنية والرقمية',
      related_project: 'مشروع تطوير المهارات الرقمية للشباب',
      current_value: 85,
      target_value: 90,
      unit: '%',
      trend: 'up',
      change_percentage: 5,
      status: 'جيد',
      description: 'مؤشر لقياس مستوى رضا المستفيدين عن الخدمات المقدمة',
      frequency: 'شهري',
      data_source: 'نظام إدارة العلاقات',
      formula: '(عدد العملاء الراضين / إجمالي العملاء المستطلعين) × 100',
      responsible: 'قسم خدمة العملاء',
      last_updated: '2025-06-04'
    },
    {
      name: 'معدل إنجاز المشاريع',
      category: 'الأداء التشغيلي',
      objective: 'تعزيز المشاركة المجتمعية للشباب',
      related_project: 'مشروع التطوع المجتمعي الشبابي',
      current_value: 72,
      target_value: 80,
      unit: '%',
      trend: 'up',
      change_percentage: 8,
      status: 'متوسط',
      description: 'نسبة المشاريع المنجزة في الوقت المحدد',
      frequency: 'شهري',
      data_source: 'نظام إدارة المشاريع',
      formula: '(عدد المشاريع المكتملة / إجمالي المشاريع) × 100',
      responsible: 'إدارة المشاريع',
      last_updated: '2025-06-04'
    },
    {
      name: 'عدد المستفيدين الجدد',
      category: 'النمو',
      objective: 'تمكين رواد الأعمال الشباب',
      related_project: 'مشروع دعم رواد الأعمال الشباب',
      current_value: 1250,
      target_value: 1500,
      unit: 'مستفيد',
      trend: 'down',
      change_percentage: -3,
      status: 'يحتاج تحسين',
      description: 'عدد المستفيدين الجدد المسجلين شهرياً',
      frequency: 'شهري',
      data_source: 'نظام إدارة المستفيدين',
      formula: 'عدد التسجيلات الجديدة خلال الشهر',
      responsible: 'قسم التسجيل',
      last_updated: '2025-06-04'
    },
    {
      name: 'معدل الحضور في البرامج',
      category: 'المشاركة',
      objective: 'تعزيز الهوية الوطنية والقيم المجتمعية',
      related_project: 'مشروع تعزيز الهوية الوطنية والتراث',
      current_value: 78,
      target_value: 85,
      unit: '%',
      trend: 'up',
      change_percentage: 12,
      status: 'جيد',
      description: 'نسبة حضور المشاركين في البرامج والفعاليات',
      frequency: 'أسبوعي',
      data_source: 'نظام الحضور',
      formula: '(عدد الحاضرين / عدد المسجلين) × 100',
      responsible: 'منسقو البرامج',
      last_updated: '2025-06-04'
    }
  ];

  // بيانات المشاريع
  const initiatives = [
    {
      title: 'مشروع تطوير المهارات الرقمية للشباب',
      description: 'برنامج شامل لتدريب الشباب على أحدث التقنيات والمهارات الرقمية مع التركيز على البرمجة والذكاء الاصطناعي',
      objective: 'تطوير مهارات الشباب التقنية والرقمية',
      related_kpi: 'نسبة التوظيف',
      department: 'إدارة التطوير والابتكار',
      manager: 'أحمد محمد السالم',
      status: 'جاري التنفيذ',
      priority: 'عالية',
      progress: 75,
      start_date: '2025-01-01',
      end_date: '2025-12-31',
      budget: 850000,
      participants: 200,
      expected_outcome: 'تدريب 200 شاب على المهارات الرقمية وتأهيلهم لسوق العمل التقني',
      success_metrics: 'عدد المتدربين المتخرجين، نسبة النجاح في الاختبارات، معدل التوظيف'
    },
    {
      title: 'مشروع دعم رواد الأعمال الشباب',
      description: 'توفير الدعم المالي والاستشاري والتدريبي للشباب الراغبين في بدء مشاريعهم الناشئة',
      objective: 'تمكين رواد الأعمال الشباب',
      related_kpi: 'عدد المستفيدين الجدد',
      department: 'إدارة ريادة الأعمال',
      manager: 'فاطمة أحمد الزهراني',
      status: 'جاري التنفيذ',
      priority: 'عالية',
      progress: 60,
      start_date: '2025-02-01',
      end_date: '2025-11-30',
      budget: 1200000,
      participants: 100,
      expected_outcome: 'إطلاق 50 مشروع ناشئ وتوفير 300 فرصة عمل جديدة',
      success_metrics: 'عدد المشاريع المدعومة، معدل نجاح المشاريع، حجم الاستثمارات المجذوبة'
    },
    {
      title: 'مشروع التطوع المجتمعي الشبابي',
      description: 'تنظيم برامج تطوعية متنوعة لتعزيز مشاركة الشباب في خدمة المجتمع وتنمية روح المسؤولية الاجتماعية',
      objective: 'تعزيز المشاركة المجتمعية للشباب',
      related_kpi: 'معدل إنجاز المشاريع',
      department: 'إدارة البرامج الشبابية',
      manager: 'محمد علي القحطاني',
      status: 'متأخرة',
      priority: 'متوسطة',
      progress: 40,
      start_date: '2025-01-15',
      end_date: '2025-10-15',
      budget: 400000,
      participants: 500,
      expected_outcome: 'مشاركة 500 متطوع في 50 فعالية تطوعية مجتمعية',
      success_metrics: 'عدد المتطوعين النشطين، ساعات العمل التطوعي، عدد المستفيدين'
    },
    {
      title: 'مشروع تعزيز الهوية الوطنية والتراث',
      description: 'برنامج ثقافي شامل لترسيخ القيم الوطنية والحفاظ على التراث الثقافي في نفوس الشباب',
      objective: 'تعزيز الهوية الوطنية والقيم المجتمعية',
      related_kpi: 'معدل الحضور في البرامج',
      department: 'الإدارة الثقافية',
      manager: 'سارة خالد العتيبي',
      status: 'التخطيط',
      priority: 'عالية',
      progress: 25,
      start_date: '2025-03-01',
      end_date: '2025-12-31',
      budget: 600000,
      participants: 300,
      expected_outcome: 'تنظيم 20 فعالية ثقافية وتراثية وتدريب 100 سفير ثقافي',
      success_metrics: 'عدد الفعاليات الثقافية، نسبة مشاركة الشباب، مستوى الوعي الثقافي'
    }
  ];

  // بيانات الأنشطة
  const activities = [
    {
      title: 'ورشة تدريبية في البرمجة',
      description: 'ورشة تدريبية مكثفة لتعليم أساسيات البرمجة للشباب',
      initiative: 'برنامج تطوير المهارات الرقمية للشباب',
      related_project: 'مشروع تطوير المهارات الرقمية للشباب',
      department: 'إدارة التطوير والابتكار',
      assignee: 'أحمد محمد السالم',
      status: 'مكتمل',
      priority: 'عالية',
      start_date: '2025-05-01',
      end_date: '2025-05-15',
      progress: 100,
      participants: 25,
      budget: 15000,
      estimated_hours: 40,
      actual_hours: 38
    },
    {
      title: 'حملة توعوية عن ريادة الأعمال',
      description: 'حملة إعلامية وتوعوية لتشجيع الشباب على ريادة الأعمال',
      initiative: 'مبادرة دعم رواد الأعمال الشباب',
      related_project: 'مشروع دعم رواد الأعمال الشباب',
      department: 'إدارة ريادة الأعمال',
      assignee: 'فاطمة أحمد الزهراني',
      status: 'جاري التنفيذ',
      priority: 'متوسطة',
      start_date: '2025-06-01',
      end_date: '2025-06-30',
      progress: 65,
      participants: 150,
      budget: 25000,
      estimated_hours: 60,
      actual_hours: 40
    },
    {
      title: 'فعالية تطوعية لتنظيف الحدائق',
      description: 'نشاط تطوعي لتنظيف وتجميل الحدائق العامة',
      initiative: 'برنامج التطوع المجتمعي',
      related_project: 'مشروع التطوع المجتمعي الشبابي',
      department: 'إدارة البرامج الشبابية',
      assignee: 'محمد علي القحطاني',
      status: 'متأخر',
      priority: 'منخفضة',
      start_date: '2025-05-20',
      end_date: '2025-06-05',
      progress: 30,
      participants: 50,
      budget: 8000,
      estimated_hours: 16,
      actual_hours: 8
    },
    {
      title: 'معرض التراث الشعبي',
      description: 'تنظيم معرض لعرض التراث والثقافة الشعبية',
      initiative: 'مهرجان الثقافة والتراث',
      related_project: 'مشروع تعزيز الهوية الوطنية والتراث',
      department: 'الإدارة الثقافية',
      assignee: 'سارة خالد العتيبي',
      status: 'لم يبدأ',
      priority: 'عالية',
      start_date: '2025-07-01',
      end_date: '2025-07-10',
      progress: 0,
      participants: 200,
      budget: 40000,
      estimated_hours: 80,
      actual_hours: 0
    },
    {
      title: 'تطوير مركز الشباب الرقمي',
      description: 'تحديث وتطوير المعدات التقنية في مركز الشباب',
      initiative: 'تطوير البنية التحتية للمرافق الشبابية',
      related_project: null,
      department: 'إدارة المرافق',
      assignee: 'أحمد عبدالرحمن الدوسري',
      status: 'جاري التنفيذ',
      priority: 'عالية',
      start_date: '2025-06-15',
      end_date: '2025-08-15',
      progress: 45,
      participants: 10,
      budget: 120000,
      estimated_hours: 200,
      actual_hours: 90
    }
  ];

  const clearAllData = async () => {
    if (!confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }

    setLoading(true);
    try {
      // حذف البيانات بالترتيب الصحيح (العلاقات أولاً)
      await supabase.from('objective_kpis').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('activities').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('initiatives').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('kpis').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('strategic_objectives').delete().neq('id', '00000000-0000-0000-0000-000000000000');

      setImportProgress({
        strategic_objectives: 0,
        kpis: 0,
        initiatives: 0,
        activities: 0,
        objective_kpis: 0
      });
      setImportComplete(false);
      notifications.success('تم الحذف', 'تم حذف جميع البيانات بنجاح');
    } catch (error) {
      console.error('Error clearing data:', error);
      notifications.error('خطأ', 'فشل في حذف البيانات');
    } finally {
      setLoading(false);
    }
  };

  const importData = async () => {
    setLoading(true);
    setImportComplete(false);
    let currentProgress = { ...importProgress };

    try {
      // 1. استيراد الأهداف الاستراتيجية
      for (const objective of strategicObjectives) {
        const { error } = await supabase
          .from('strategic_objectives')
          .insert(objective);
        
        if (error) throw error;
        currentProgress.strategic_objectives++;
        setImportProgress({ ...currentProgress });
      }

      // 2. استيراد مؤشرات الأداء
      for (const kpi of kpis) {
        const { error } = await supabase
          .from('kpis')
          .insert(kpi);
        
        if (error) throw error;
        currentProgress.kpis++;
        setImportProgress({ ...currentProgress });
      }

      // 3. استيراد المشاريع
      for (const initiative of initiatives) {
        const { error } = await supabase
          .from('initiatives')
          .insert(initiative);
        
        if (error) throw error;
        currentProgress.initiatives++;
        setImportProgress({ ...currentProgress });
      }

      // 4. استيراد الأنشطة
      for (const activity of activities) {
        const { error } = await supabase
          .from('activities')
          .insert(activity);
        
        if (error) throw error;
        currentProgress.activities++;
        setImportProgress({ ...currentProgress });
      }

      // 5. إنشاء علاقات الأهداف ومؤشرات الأداء
      const { data: objectivesData } = await supabase
        .from('strategic_objectives')
        .select('id, title');

      if (objectivesData) {
        const relationshipsToInsert = [];
        
        // ربط كل هدف بمؤشرات الأداء المناسبة
        for (const obj of objectivesData) {
          const relatedKpis = kpis.filter(kpi => kpi.objective === obj.title);
          for (const kpi of relatedKpis) {
            relationshipsToInsert.push({
              objective_id: obj.id,
              kpi_name: kpi.name
            });
          }
        }

        if (relationshipsToInsert.length > 0) {
          const { error } = await supabase
            .from('objective_kpis')
            .insert(relationshipsToInsert);
          
          if (error) throw error;
          currentProgress.objective_kpis = relationshipsToInsert.length;
          setImportProgress({ ...currentProgress });
        }
      }

      setImportComplete(true);
      notifications.success('تم الاستيراد', 'تم استيراد جميع البيانات بنجاح');
    } catch (error) {
      console.error('Error importing data:', error);
      notifications.error('خطأ في الاستيراد', 'فشل في استيراد البيانات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          استيراد البيانات التجريبية
        </h2>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          استيراد البيانات التجريبية لقاعدة البيانات لاختبار النظام
        </p>
      </div>

      {/* إحصائيات الاستيراد */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {importProgress.strategic_objectives}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">الأهداف الاستراتيجية</div>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {importProgress.kpis}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">مؤشرات الأداء</div>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">
            {importProgress.initiatives}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">المشاريع</div>
        </div>
        <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">
            {importProgress.activities}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">الأنشطة</div>
        </div>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">
            {importProgress.objective_kpis}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">العلاقات</div>
        </div>
      </div>

      {/* رسالة حالة الاستيراد */}
      {importComplete && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-green-700 dark:text-green-400 font-medium">
              تم استيراد جميع البيانات بنجاح
            </span>
          </div>
        </div>
      )}

      {/* أزرار العمليات */}
      <div className="flex gap-4">
        <button
          onClick={importData}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Upload className="w-5 h-5" />
          )}
          {loading ? 'جاري الاستيراد...' : 'استيراد البيانات'}
        </button>

        <button
          onClick={clearAllData}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg font-medium transition-colors"
        >
          <Trash2 className="w-5 h-5" />
          حذف جميع البيانات
        </button>
      </div>

      {/* تفاصيل البيانات */}
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          تفاصيل البيانات المستوردة
        </h3>
        
        <div className="space-y-4">
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              الأهداف الاستراتيجية ({strategicObjectives.length})
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {strategicObjectives.map((obj, index) => (
                <li key={index}>• {obj.title}</li>
              ))}
            </ul>
          </div>

          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              مؤشرات الأداء ({kpis.length})
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {kpis.map((kpi, index) => (
                <li key={index}>• {kpi.name} ({kpi.category})</li>
              ))}
            </ul>
          </div>

          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              المشاريع ({initiatives.length})
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {initiatives.map((init, index) => (
                <li key={index}>• {init.title}</li>
              ))}
            </ul>
          </div>

          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              الأنشطة ({activities.length})
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {activities.map((act, index) => (
                <li key={index}>• {act.title}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataImporter;