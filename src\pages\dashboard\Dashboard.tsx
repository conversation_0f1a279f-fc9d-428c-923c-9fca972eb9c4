import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Plus, Calendar, Target, TrendingUp, Users, BarChart3, FileText, CheckCircle, Clock, AlertCircle, ChevronDown, MoreHorizontal, Edit, Trash2, Shield, UserCheck, Eye, Settings, Award, Sun, Moon, ArrowLeft, X, DollarSign, TrendingDown, Activity, Zap, Star } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../components/ui/NotificationSystem';

// بيانات لوحة التحكم
const dashboardStats = [
  {
    id: 1,
    title: 'إجمالي الأهداف',
    value: '12',
    change: '+2',
    changeType: 'increase',
    icon: Target,
    color: 'blue',
    description: 'أهداف استراتيجية نشطة'
  },
  {
    id: 2,
    title: 'المشاريع النشطة',
    value: '37',
    change: '+5',
    changeType: 'increase',
    icon: Zap,
    color: 'green',
    description: 'مشاريع قيد التنفيذ'
  },
  {
    id: 3,
    title: 'مؤشرات الأداء',
    value: '48',
    change: '+3',
    changeType: 'increase',
    icon: BarChart3,
    color: 'purple',
    description: 'مؤشرات أداء رئيسية'
  },
  {
    id: 4,
    title: 'معدل الإنجاز',
    value: '75%',
    change: '+8%',
    changeType: 'increase',
    icon: CheckCircle,
    color: 'yellow',
    description: 'متوسط إنجاز المشاريع'
  },
  {
    id: 5,
    title: 'الميزانية المستخدمة',
    value: '2.5M',
    change: '+12%',
    changeType: 'increase',
    icon: DollarSign,
    color: 'red',
    description: 'من إجمالي الميزانية'
  },
  {
    id: 6,
    title: 'الفرق النشطة',
    value: '8',
    change: '0',
    changeType: 'stable',
    icon: Users,
    color: 'indigo',
    description: 'فرق عمل متخصصة'
  }
];

// المهام الحديثة
const recentActivities = [
  {
    id: 1,
    title: 'تم إنجاز مؤشر معدل النمو السنوي',
    type: 'completion',
    time: 'منذ ساعتين',
    user: 'أحمد محمد',
    department: 'الإدارة المالية',
    status: 'مكتمل'
  },
  {
    id: 2,
    title: 'تحديث في مشروع التحول الرقمي',
    type: 'update',
    time: 'منذ 4 ساعات',
    user: 'سارة أحمد',
    department: 'تقنية المعلومات',
    status: 'جاري'
  },
  {
    id: 3,
    title: 'اجتماع مراجعة الربع الثاني',
    type: 'meeting',
    time: 'منذ يوم',
    user: 'محمد العتيبي',
    department: 'الإدارة العليا',
    status: 'مجدول'
  },
  {
    id: 4,
    title: 'تأخير في هدف تحسين الأداء',
    type: 'alert',
    time: 'منذ يومين',
    user: 'فاطمة الزهراني',
    department: 'إدارة الأداء',
    status: 'متأخر'
  },
  {
    id: 5,
    title: 'إطلاق مبادرة جديدة للابتكار',
    type: 'launch',
    time: 'منذ 3 أيام',
    user: 'خالد السعيد',
    department: 'الابتكار',
    status: 'جديد'
  }
];

// التنبيهات والمخاطر
const alerts = [
  {
    id: 1,
    title: 'تأخير محتمل في المشروع الرقمي',
    severity: 'high',
    type: 'risk',
    description: 'المشروع قد يتأخر بسبب نقص الموارد',
    department: 'تقنية المعلومات',
    dueDate: '2025-06-15'
  },
  {
    id: 2,
    title: 'مؤشر رضا العملاء دون المستهدف',
    severity: 'medium',
    type: 'performance',
    description: 'انخفاض بنسبة 5% عن المستهدف',
    department: 'خدمة العملاء',
    dueDate: '2025-06-20'
  },
  {
    id: 3,
    title: 'تجاوز الميزانية في قسم التسويق',
    severity: 'high',
    type: 'budget',
    description: 'تجاوز بنسبة 15% من الميزانية المخصصة',
    department: 'التسويق',
    dueDate: '2025-06-10'
  }
];

const Dashboard = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [activeView, setActiveView] = useState('نظرة عامة');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('dashboardListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { user } = useAuth();
  const notifications = useNotifications();

  // تطبيق الوضع الليلي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('dashboardListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  const getActivityIcon = (type) => {
    switch(type) {
      case 'completion': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'update': return <Activity className="w-4 h-4 text-blue-500" />;
      case 'meeting': return <Calendar className="w-4 h-4 text-purple-500" />;
      case 'alert': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'launch': return <Star className="w-4 h-4 text-yellow-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getAlertColor = (severity) => {
    switch(severity) {
      case 'high': return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'medium': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low': return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      default: return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getStatColor = (color) => {
    const colors = {
      blue: 'from-blue-500 to-blue-600',
      green: 'from-green-500 to-green-600',
      purple: 'from-purple-500 to-purple-600',
      yellow: 'from-yellow-500 to-yellow-600',
      red: 'from-red-500 to-red-600',
      indigo: 'from-indigo-500 to-indigo-600'
    };
    return colors[color] || colors.blue;
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <BarChart3 className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر عنصر لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر عنصر من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className={`p-2 rounded-lg bg-gradient-to-r ${getStatColor(selectedItem.color)} text-white`}>
                {React.createElement(selectedItem.icon, { size: 24 })}
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">{selectedItem.title}</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{selectedItem.description}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-6">
            {/* القيمة الحالية */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">القيمة الحالية</h4>
              <div className="flex items-center gap-2">
                <span className="text-3xl font-bold text-gray-900 dark:text-white">{selectedItem.value}</span>
                <div className={`flex items-center gap-1 text-sm ${
                  selectedItem.changeType === 'increase' ? 'text-green-500' : 
                  selectedItem.changeType === 'decrease' ? 'text-red-500' : 'text-gray-500'
                }`}>
                  {selectedItem.changeType === 'increase' && <TrendingUp className="w-4 h-4" />}
                  {selectedItem.changeType === 'decrease' && <TrendingDown className="w-4 h-4" />}
                  <span>{selectedItem.change}</span>
                </div>
              </div>
            </div>

            {/* تفاصيل إضافية */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="font-medium mb-3 text-gray-900 dark:text-white">معلومات تفصيلية</h4>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">آخر تحديث: </span>
                  <span className="text-sm text-gray-900 dark:text-white">اليوم، 2:30 م</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">التكرار: </span>
                  <span className="text-sm text-gray-900 dark:text-white">يومي</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">المسؤول: </span>
                  <span className="text-sm text-gray-900 dark:text-white">فريق التخطيط</span>
                </div>
              </div>
            </div>

            {/* رسم بياني (محاكاة) */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="font-medium mb-3 text-gray-900 dark:text-white">الاتجاه الشهري</h4>
              <div className="h-32 flex items-end gap-2">
                {[65, 70, 68, 75, 80, 85, 78].map((height, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div 
                      className={`w-full bg-gradient-to-t ${getStatColor(selectedItem.color)} rounded-t`}
                      style={{ height: `${height}%` }}
                    />
                    <span className="text-xs mt-1 text-gray-600 dark:text-gray-400">
                      {['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي'][index]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">لوحة التحكم</h1>
        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">نظرة شاملة على أداء المؤسسة</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-x-auto">
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'نظرة عامة' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('نظرة عامة')}
        >
          نظرة عامة
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'الأنشطة' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('الأنشطة')}
        >
          الأنشطة الحديثة
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'التنبيهات' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('التنبيهات')}
        >
          التنبيهات والمخاطر
        </button>
      </div>

      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
        {/* List Panel */}
        <div 
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
          style={{ width: `${listWidth}%` }}
        >
          {activeView === 'نظرة عامة' && (
            <>
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium mb-3 text-gray-900 dark:text-white">المؤشرات الرئيسية</h3>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="space-y-2 p-2">
                {dashboardStats.map(stat => (
                  <div
                    key={stat.id}
                    className={`p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                      selectedItem?.id === stat.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedItem(stat)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${getStatColor(stat.color)} text-white`}>
                        {React.createElement(stat.icon, { size: 20 })}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm text-gray-900 dark:text-white">{stat.title}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-lg font-bold text-gray-900 dark:text-white">{stat.value}</span>
                          <span className={`text-xs ${
                            stat.changeType === 'increase' ? 'text-green-500' : 
                            stat.changeType === 'decrease' ? 'text-red-500' : 'text-gray-500'
                          }`}>
                            {stat.change}
                          </span>
                        </div>
                        <p className="text-xs mt-1 text-gray-600 dark:text-gray-400">{stat.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </>
          )}

          {activeView === 'الأنشطة' && (
            <>
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium mb-3 text-gray-900 dark:text-white">الأنشطة الحديثة</h3>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="space-y-1">
                {recentActivities.map(activity => (
                  <div
                    key={activity.id}
                    className="p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/30 cursor-pointer"
                    onClick={() => setSelectedItem(activity)}
                  >
                    <div className="flex items-start gap-3">
                      {getActivityIcon(activity.type)}
                      <div className="flex-1">
                        <h4 className="font-medium text-sm text-gray-900 dark:text-white">{activity.title}</h4>
                        <p className="text-xs mt-1 text-gray-600 dark:text-gray-400">{activity.department} • {activity.time}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className={`text-xs px-2 py-1 rounded ${
                            activity.status === 'مكتمل' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            activity.status === 'جاري' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                            activity.status === 'متأخر' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {activity.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </>
          )}

          {activeView === 'التنبيهات' && (
            <>
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium mb-3 text-gray-900 dark:text-white">التنبيهات والمخاطر</h3>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="space-y-2 p-2">
                {alerts.map(alert => (
                  <div
                    key={alert.id}
                    className={`p-4 rounded-lg border-l-4 ${getAlertColor(alert.severity)} cursor-pointer`}
                    onClick={() => setSelectedItem(alert)}
                  >
                    <h4 className="font-medium text-sm mb-1 text-gray-900 dark:text-white">{alert.title}</h4>
                    <p className="text-xs mb-2 text-gray-600 dark:text-gray-400">{alert.description}</p>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600 dark:text-gray-400">{alert.department}</span>
                      <span className="text-gray-600 dark:text-gray-400">{alert.dueDate}</span>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* Resize Handle */}
        <div 
          className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
            isResizing 
              ? 'bg-blue-500' 
              : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
          }`}
          onMouseDown={() => setIsResizing(true)}
        >
          <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
        </div>

        {/* Detail Panel */}
        <div className="flex-1 overflow-hidden">
          <DetailPanel />
        </div>
      </div>

      {/* Role Switcher */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default Dashboard;