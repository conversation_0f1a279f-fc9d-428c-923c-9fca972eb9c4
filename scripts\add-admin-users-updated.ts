import { UserService } from '../src/lib/userService';

// بيانات المستخدمين الإداريين
const adminUsers = [
  {
    name: 'أحمد الدريهم',
    position: 'مدير قسم التطوع',
    department: 'قسم التطوع',
    email: '<EMAIL>',
    phone: '966531685640',
    role: 'مدير',
    password: 'AdminPass123!'
  },
  {
    name: 'سعد الدريهم',
    position: 'المدير التنفيذي',
    department: 'الإدارة التنفيذية',
    email: '<EMAIL>',
    phone: '966555104096',
    role: 'مدير عام',
    password: 'AdminPass123!'
  },
  {
    name: 'سعد القحيز',
    position: 'مدير مشاريع',
    department: 'البرامج والمشاريع',
    email: 'bi.sh<PERSON><PERSON><EMAIL>',
    phone: '966555108628',
    role: 'مدير',
    password: 'AdminPass123!'
  },
  {
    name: 'عبدالعزيز المطرد',
    position: 'مدير مشاريع',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '966544303202',
    role: 'مدير',
    password: 'AdminPass123!'
  },
  {
    name: 'عبدالله المحسن',
    position: 'مدير قسم الموارد المالية',
    department: 'الموارد المالية',
    email: '<EMAIL>',
    phone: '966555106645',
    role: 'مدير',
    password: 'AdminPass123!'
  },
  {
    name: 'معتصم العرفج',
    position: 'مدير قسم البرامج',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '9665608194',
    role: 'مدير',
    password: 'AdminPass123!'
  },
  {
    name: 'ناصر اليحيى',
    position: 'منسق البيئات',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '966500977270',
    role: 'مشرف',
    password: 'AdminPass123!'
  }
];

// دالة لإنشاء كلمة مرور عشوائية
function generatePassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// الدالة الرئيسية لإضافة المستخدمين
export async function addAdminUsers() {
  console.log('🚀 بدء عملية إضافة المستخدمين الإداريين...');
  
  const results = [];
  
  for (const userData of adminUsers) {
    console.log(`\n📝 معالجة المستخدم: ${userData.name}`);
    
    try {
      // التحقق من وجود المستخدم مسبقاً
      const existingUsers = await UserService.getAllUsers();
      const existingUser = existingUsers.find(u => 
        u.email === userData.email || u.full_name === userData.name
      );

      if (existingUser) {
        console.log(`⚠️  المستخدم ${userData.name} موجود مسبقاً`);
        results.push({
          name: userData.name,
          status: 'موجود مسبقاً',
          email: userData.email
        });
        continue;
      }

      // إنشاء كلمة مرور عشوائية
      const password = generatePassword();

      // إنشاء المستخدم مع حساب مصادقة
      const newUser = await UserService.createUser({
        email: userData.email,
        password: password,
        full_name: userData.name,
        role: userData.role,
        department: userData.department,
        position: userData.position,
        phone: userData.phone,
        createAuthAccount: true // إنشاء حساب مصادقة
      });

      console.log(`✅ تم إنشاء المستخدم ${userData.name} بنجاح`);
      results.push({
        name: userData.name,
        status: 'تم الإنشاء بنجاح',
        email: userData.email,
        password: password,
        role: userData.role,
        department: userData.department,
        position: userData.position,
        phone: userData.phone,
        userId: newUser.id
      });

    } catch (error) {
      console.error(`❌ خطأ في معالجة المستخدم ${userData.name}:`, error);
      results.push({
        name: userData.name,
        status: 'فشل',
        email: userData.email,
        error: error.message
      });
    }
  }

  // طباعة النتائج النهائية
  console.log('\n📊 ملخص النتائج:');
  console.log('==================');
  
  results.forEach(result => {
    console.log(`\n👤 ${result.name}`);
    console.log(`   📧 البريد: ${result.email}`);
    console.log(`   📊 الحالة: ${result.status}`);
    if (result.password) {
      console.log(`   🔑 كلمة المرور: ${result.password}`);
      console.log(`   👔 الدور: ${result.role}`);
      console.log(`   🏢 القسم: ${result.department}`);
      console.log(`   📱 الهاتف: ${result.phone}`);
    }
    if (result.error) {
      console.log(`   ❌ الخطأ: ${result.error}`);
    }
  });

  const successful = results.filter(r => r.status === 'تم الإنشاء بنجاح').length;
  const existing = results.filter(r => r.status === 'موجود مسبقاً').length;
  const failed = results.filter(r => r.status === 'فشل').length;

  console.log(`\n📈 الإحصائيات:`);
  console.log(`   ✅ تم الإنشاء: ${successful}`);
  console.log(`   ⚠️  موجود مسبقاً: ${existing}`);
  console.log(`   ❌ فشل: ${failed}`);
  console.log(`   📊 المجموع: ${results.length}`);

  return results;
}

// دالة لاختبار النظام الجديد
export async function testAuthSystem() {
  console.log('🧪 اختبار النظام الجديد...');
  
  try {
    // اختبار جلب المستخدمين
    const users = await UserService.getAllUsers();
    console.log(`✅ تم جلب ${users.length} مستخدم`);
    
    // اختبار جلب ملف المستخدم الحالي
    const currentProfile = await UserService.getCurrentUserProfile();
    if (currentProfile) {
      console.log(`✅ تم جلب ملف المستخدم الحالي: ${currentProfile.full_name}`);
    } else {
      console.log('⚠️  لا يوجد مستخدم مسجل دخول حالياً');
    }
    
    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار النظام:', error);
    return false;
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  addAdminUsers()
    .then(() => {
      console.log('\n🎉 انتهت عملية إضافة المستخدمين الإداريين');
      return testAuthSystem();
    })
    .then(() => {
      console.log('\n✅ تم اختبار النظام بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ خطأ في تشغيل السكريپت:', error);
      process.exit(1);
    });
}
