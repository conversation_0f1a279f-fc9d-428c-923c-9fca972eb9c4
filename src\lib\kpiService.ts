import { supabase } from './supabase';

export interface KPI {
  id: string;
  name: string;
  category: string;
  objective?: string;
  related_project?: string;
  current_value: number;
  target_value: number;
  unit: string;
  trend: string;
  change_percentage: number;
  status: string;
  description?: string;
  frequency: string;
  data_source?: string;
  formula?: string;
  responsible: string;
  last_updated: string;
  created_at: string;
  updated_at: string;
}

export class KPIService {
  // جلب جميع مؤشرات الأداء
  static async getAllKPIs(): Promise<KPI[]> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching KPIs:', error);
      throw error;
    }
  }

  // إنشاء مؤشر أداء جديد
  static async createKPI(kpiData: {
    name: string;
    category: string;
    objective?: string;
    target_value: number;
    unit: string;
    responsible: string;
    frequency?: string;
    description?: string;
  }): Promise<KPI> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .insert({
          name: kpiData.name,
          category: kpiData.category,
          objective: kpiData.objective,
          target_value: kpiData.target_value,
          current_value: 0,
          unit: kpiData.unit,
          trend: 'مستقر',
          change_percentage: 0,
          status: 'نشط',
          description: kpiData.description,
          frequency: kpiData.frequency || 'شهري',
          responsible: kpiData.responsible,
          last_updated: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating KPI:', error);
      throw error;
    }
  }

  // تحديث مؤشر أداء
  static async updateKPI(kpiId: string, updates: Partial<KPI>): Promise<KPI> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
          last_updated: new Date().toISOString()
        })
        .eq('id', kpiId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating KPI:', error);
      throw error;
    }
  }

  // حذف مؤشر أداء
  static async deleteKPI(kpiId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('kpis')
        .delete()
        .eq('id', kpiId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting KPI:', error);
      throw error;
    }
  }

  // تحديث قيمة مؤشر الأداء
  static async updateKPIValue(kpiId: string, newValue: number): Promise<KPI> {
    try {
      // جلب القيمة الحالية أولاً
      const { data: currentKPI, error: fetchError } = await supabase
        .from('kpis')
        .select('current_value, target_value')
        .eq('id', kpiId)
        .single();

      if (fetchError) throw fetchError;

      // حساب النسبة المئوية للتغيير
      const oldValue = currentKPI.current_value || 0;
      const changePercentage = oldValue > 0 ? ((newValue - oldValue) / oldValue) * 100 : 0;
      
      // تحديد الاتجاه
      let trend = 'مستقر';
      if (changePercentage > 5) trend = 'صاعد';
      else if (changePercentage < -5) trend = 'هابط';

      // تحديد الحالة بناءً على التقدم
      const progress = (newValue / currentKPI.target_value) * 100;
      let status = 'نشط';
      if (progress >= 100) status = 'مكتمل';
      else if (progress >= 75) status = 'متقدم';
      else if (progress < 25) status = 'متأخر';

      const { data, error } = await supabase
        .from('kpis')
        .update({
          current_value: newValue,
          change_percentage: Math.round(changePercentage * 100) / 100,
          trend,
          status,
          last_updated: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', kpiId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating KPI value:', error);
      throw error;
    }
  }

  // جلب إحصائيات مؤشرات الأداء
  static async getKPIStats(): Promise<{
    total: number;
    completed: number;
    onTrack: number;
    delayed: number;
    averageProgress: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('current_value, target_value, status');

      if (error) throw error;

      const total = data?.length || 0;
      const completed = data?.filter(kpi => kpi.status === 'مكتمل').length || 0;
      const onTrack = data?.filter(kpi => kpi.status === 'متقدم' || kpi.status === 'نشط').length || 0;
      const delayed = data?.filter(kpi => kpi.status === 'متأخر').length || 0;

      // حساب متوسط التقدم
      const totalProgress = data?.reduce((sum, kpi) => {
        const progress = kpi.target_value > 0 ? (kpi.current_value / kpi.target_value) * 100 : 0;
        return sum + Math.min(progress, 100);
      }, 0) || 0;

      const averageProgress = total > 0 ? totalProgress / total : 0;

      return {
        total,
        completed,
        onTrack,
        delayed,
        averageProgress: Math.round(averageProgress * 100) / 100
      };
    } catch (error) {
      console.error('Error fetching KPI stats:', error);
      return { total: 0, completed: 0, onTrack: 0, delayed: 0, averageProgress: 0 };
    }
  }

  // جلب مؤشرات الأداء حسب الفئة
  static async getKPIsByCategory(): Promise<{ [category: string]: KPI[] }> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('*')
        .order('category', { ascending: true });

      if (error) throw error;

      // تجميع حسب الفئة
      const groupedKPIs = (data || []).reduce((acc, kpi) => {
        const category = kpi.category || 'غير محدد';
        if (!acc[category]) acc[category] = [];
        acc[category].push(kpi);
        return acc;
      }, {} as { [category: string]: KPI[] });

      return groupedKPIs;
    } catch (error) {
      console.error('Error fetching KPIs by category:', error);
      return {};
    }
  }

  // جلب مؤشر أداء واحد
  static async getKPIById(kpiId: string): Promise<KPI | null> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('*')
        .eq('id', kpiId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching KPI:', error);
      return null;
    }
  }
}
