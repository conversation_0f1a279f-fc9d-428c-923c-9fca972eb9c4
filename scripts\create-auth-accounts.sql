-- سكريپت لإنشاء حسابات المصادقة للمستخدمين الإداريين
-- يجب تشغيل هذا في Supabase SQL Editor أو Dashboard

-- ✅ تم إضافة المستخدمين في جدول users بنجاح:

/*
المستخدمون المضافون:

1. أحمد الدريهم (4caaed9e-c586-4b2d-9165-7afc0811bd88)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: قسم التطوع

2. سعد الدريهم (3f548c0d-5797-417a-8fa7-0678593ee6a9)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: الإدارة التنفيذية

3. سعد القحيز (7dc7cd96-baea-40a9-a8d5-f94948d0c45f)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: البرامج والمشاريع

4. عبدالعزيز المطرد (cd539c15-195b-43d0-8473-43fb2b1a0b41)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: البرامج والمشاريع

5. عبدالله المحسن (f8b7a9fa-c162-40ad-8626-d8649cae9856)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: الموارد المالية

6. معتصم العرفج (a3f0bc25-805e-455e-a899-a20d45fdc80f)
   - البريد: <EMAIL>
   - الدور: مدير
   - القسم: البرامج والمشاريع

7. ناصر اليحيى (51af1160-afbe-4edc-9c2f-c644d33c48ef)
   - البريد: <EMAIL>
   - الدور: مشرف
   - القسم: البرامج والمشاريع
*/

-- 🔧 الخطوة التالية: إنشاء حسابات المصادقة

-- يجب إنشاء حسابات المصادقة في Supabase Dashboard:
-- 1. اذهب إلى Authentication > Users
-- 2. اضغط "Add User"
-- 3. أدخل البيانات التالية لكل مستخدم:

/*
=== بيانات المصادقة المطلوبة ===

1. أحمد الدريهم:
   Email: <EMAIL>
   Password: TempPass123!
   
2. سعد الدريهم:
   Email: <EMAIL>
   Password: TempPass123!
   
3. سعد القحيز:
   Email: <EMAIL>
   Password: TempPass123!
   
4. عبدالعزيز المطرد:
   Email: <EMAIL>
   Password: TempPass123!
   
5. عبدالله المحسن:
   Email: <EMAIL>
   Password: TempPass123!
   
6. معتصم العرفج:
   Email: <EMAIL>
   Password: TempPass123!
   
7. ناصر اليحيى:
   Email: <EMAIL>
   Password: TempPass123!
*/

-- 🔗 بعد إنشاء حسابات المصادقة، قم بتشغيل الاستعلامات التالية لربط الحسابات:

-- دالة لربط حساب المصادقة بالمستخدم في جدول users
-- (استبدل AUTH_USER_ID و USER_EMAIL بالقيم الفعلية)

/*
-- مثال لربط أحمد الدريهم:
UPDATE users 
SET auth_id = 'AUTH_USER_ID_FROM_DASHBOARD'
WHERE email = '<EMAIL>';

-- مثال لربط سعد الدريهم:
UPDATE users 
SET auth_id = 'AUTH_USER_ID_FROM_DASHBOARD'
WHERE email = '<EMAIL>';

-- وهكذا لباقي المستخدمين...
*/

-- 📋 للتحقق من المستخدمين المضافين:
SELECT 
  id,
  name,
  email,
  role,
  department,
  position,
  status,
  has_system_account,
  auth_id,
  CASE 
    WHEN auth_id IS NOT NULL THEN 'مرتبط بحساب مصادقة'
    ELSE 'غير مرتبط بحساب مصادقة'
  END as auth_status
FROM users 
WHERE name IN (
  'أحمد الدريهم',
  'سعد الدريهم', 
  'سعد القحيز',
  'عبدالعزيز المطرد',
  'عبدالله المحسن',
  'معتصم العرفج',
  'ناصر اليحيى'
)
ORDER BY name;

-- 🔍 للتحقق من حسابات المصادقة:
-- (تشغيل هذا بعد إنشاء الحسابات في Dashboard)
SELECT 
  au.id as auth_id,
  au.email,
  au.created_at as auth_created_at,
  u.id as user_id,
  u.name,
  u.role,
  u.department
FROM auth.users au
LEFT JOIN users u ON au.id = u.auth_id
WHERE au.email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ORDER BY au.email;

-- 📝 ملاحظات مهمة:
-- 1. كلمة المرور المؤقتة: TempPass123!
-- 2. يجب على المستخدمين تغيير كلمة المرور عند أول تسجيل دخول
-- 3. تأكد من تفعيل Email Confirmation في إعدادات المصادقة
-- 4. يمكن تخصيص كلمات مرور مختلفة لكل مستخدم حسب الحاجة

-- 🎯 الهدف النهائي:
-- جميع المستخدمين سيتمكنون من تسجيل الدخول باستخدام بريدهم الإلكتروني وكلمة المرور
-- وسيتم جلب بياناتهم الكاملة من جدول users عبر النظام المحدث
