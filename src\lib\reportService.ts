import { supabase } from './supabase';

export interface ReportData {
  id: string;
  title: string;
  type: string;
  department?: string;
  period?: string;
  description?: string;
  format?: string;
  status?: string;
  created_by?: string;
  file_size?: string;
  file_url?: string;
  data?: any;
  created_at: string;
  updated_at: string;
}

export class ReportService {
  // جلب جميع التقارير
  static async getAllReports(): Promise<ReportData[]> {
    try {
      const { data, error } = await supabase
        .from('reports')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching reports:', error);
      throw error;
    }
  }

  // إنشاء تقرير جديد
  static async createReport(reportData: {
    title: string;
    type: string;
    data: any;
  }): Promise<ReportData> {
    try {
      const { data, error } = await supabase
        .from('reports')
        .insert({
          title: reportData.title,
          type: reportData.type,
          data: reportData.data
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }

  // جلب تقرير الأداء
  static async getPerformanceReport(): Promise<any> {
    try {
      // جلب بيانات الأهداف الاستراتيجية
      const { data: objectives, error: objError } = await supabase
        .from('strategic_objectives')
        .select('*');

      if (objError) throw objError;

      // جلب بيانات مؤشرات الأداء
      const { data: kpis, error: kpiError } = await supabase
        .from('kpis')
        .select('*');

      if (kpiError) throw kpiError;

      // جلب بيانات الأنشطة
      const { data: activities, error: actError } = await supabase
        .from('activities')
        .select('*');

      if (actError) throw actError;

      // حساب الإحصائيات
      const totalObjectives = objectives?.length || 0;
      const completedObjectives = objectives?.filter(obj => obj.status === 'مكتمل').length || 0;
      const totalKpis = kpis?.length || 0;
      const totalActivities = activities?.length || 0;
      const completedActivities = activities?.filter(act => act.status === 'مكتمل').length || 0;

      return {
        summary: {
          totalObjectives,
          completedObjectives,
          objectivesProgress: totalObjectives > 0 ? (completedObjectives / totalObjectives) * 100 : 0,
          totalKpis,
          totalActivities,
          completedActivities,
          activitiesProgress: totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0
        },
        objectives: objectives || [],
        kpis: kpis || [],
        activities: activities || []
      };
    } catch (error) {
      console.error('Error generating performance report:', error);
      throw error;
    }
  }

  // جلب تقرير المؤشرات
  static async getKPIReport(): Promise<any> {
    try {
      const { data: kpis, error } = await supabase
        .from('kpis')
        .select(`
          *,
          strategic_objectives(title, status)
        `);

      if (error) throw error;

      // تجميع البيانات حسب الحالة
      const kpisByStatus = {
        achieved: kpis?.filter(kpi => (kpi.current_value || 0) >= (kpi.target_value || 0)).length || 0,
        inProgress: kpis?.filter(kpi => (kpi.current_value || 0) < (kpi.target_value || 0) && (kpi.current_value || 0) > 0).length || 0,
        notStarted: kpis?.filter(kpi => (kpi.current_value || 0) === 0).length || 0
      };

      return {
        summary: {
          total: kpis?.length || 0,
          ...kpisByStatus
        },
        kpis: kpis || []
      };
    } catch (error) {
      console.error('Error generating KPI report:', error);
      throw error;
    }
  }

  // جلب التقرير المالي
  static async getFinancialReport(): Promise<any> {
    try {
      // جلب بيانات المشاريع مع الميزانيات
      const { data: projects, error } = await supabase
        .from('projects')
        .select('*');

      if (error) throw error;

      // حساب الإحصائيات المالية
      const totalBudget = projects?.reduce((sum, project) => sum + (parseFloat(project.budget) || 0), 0) || 0;
      const projectsByStatus = {
        planned: projects?.filter(p => p.status === 'مخطط').length || 0,
        inProgress: projects?.filter(p => p.status === 'قيد التنفيذ').length || 0,
        completed: projects?.filter(p => p.status === 'مكتمل').length || 0
      };

      return {
        summary: {
          totalProjects: projects?.length || 0,
          totalBudget,
          averageBudget: projects?.length ? totalBudget / projects.length : 0,
          ...projectsByStatus
        },
        projects: projects || []
      };
    } catch (error) {
      console.error('Error generating financial report:', error);
      throw error;
    }
  }

  // جلب التقرير الشامل
  static async getComprehensiveReport(): Promise<any> {
    try {
      const [performanceReport, kpiReport, financialReport] = await Promise.all([
        this.getPerformanceReport(),
        this.getKPIReport(),
        this.getFinancialReport()
      ]);

      return {
        performance: performanceReport,
        kpis: kpiReport,
        financial: financialReport,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating comprehensive report:', error);
      throw error;
    }
  }

  // جلب إحصائيات الأقسام
  static async getDepartmentReport(): Promise<any> {
    try {
      const { data: departments, error } = await supabase
        .from('departments')
        .select('*');

      if (error) throw error;

      const stats = {
        total: departments?.length || 0,
        active: departments?.filter(d => d.status === 'نشط').length || 0,
        totalEmployees: departments?.reduce((sum, d) => sum + (d.employee_count || 0), 0) || 0
      };

      return {
        summary: stats,
        departments: departments || []
      };
    } catch (error) {
      console.error('Error generating department report:', error);
      throw error;
    }
  }

  // جلب إحصائيات المستخدمين
  static async getUserReport(): Promise<any> {
    try {
      const { data: users, error } = await supabase
        .from('users')
        .select('*');

      if (error) throw error;

      const usersByRole = users?.reduce((acc, user) => {
        const role = user.role || 'غير محدد';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
      }, {}) || {};

      const usersByStatus = {
        active: users?.filter(u => u.status === 'نشط').length || 0,
        inactive: users?.filter(u => u.status !== 'نشط').length || 0
      };

      return {
        summary: {
          total: users?.length || 0,
          ...usersByStatus,
          byRole: usersByRole
        },
        users: users || []
      };
    } catch (error) {
      console.error('Error generating user report:', error);
      throw error;
    }
  }
}
