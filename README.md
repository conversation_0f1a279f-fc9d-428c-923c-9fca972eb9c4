# نظام التخطيط الاستراتيجي

نظام شامل لإدارة التخطيط الاستراتيجي للمؤسسات مبني بـ React و TypeScript و Supabase.

## المميزات

- 🎯 إدارة الأهداف الاستراتيجية
- 📊 مؤشرات الأداء الرئيسية (KPIs)
- 🏢 إدارة الأقسام والموظفين
- 📈 تقارير وتحليلات تفاعلية
- 🔐 نظام مصادقة آمن
- 📱 تصميم متجاوب

## التقنيات المستخدمة

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Charts**: ApexCharts
- **Animation**: Framer Motion
- **Forms**: React Hook Form
- **State Management**: Zustand
- **Routing**: React Router DOM

## إعداد المشروع

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. متغيرات البيئة

تم إعداد ملف `.env` مع الإعدادات التالية:

```env
VITE_SUPABASE_URL=https://ncsqltgvfioneovskwxg.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8F02C4CQz79JxtW1L_ZqEPOZJEaDwQRLLfVdwxZ7NZg
```

### 3. تشغيل المشروع محلياً

```bash
npm run dev
```

### 4. بناء المشروع للإنتاج

```bash
npm run build
```

## 🔧 الإصلاحات الجديدة

### ما تم إصلاحه:
- ✅ إضافة أنواع البيانات المفقودة (`user_profiles`, `roles`)
- ✅ إنشاء خدمة إدارة المستخدمين (`UserService`)
- ✅ نظام إنشاء المدير الافتراضي تلقائياً
- ✅ صفحة إعداد النظام (`/setup`)
- ✅ تحديث نظام المصادقة مع ملفات المستخدمين
- ✅ توحيد بيانات الدخول

### المشاكل المتبقية:
- ⚠️ خطأ في استيراد أيقونة `Lightbulb` في `Activities.tsx`
- ⚠️ تحذيرات React Router للإصدار المستقبلي

## 🚀 إعداد النظام للمرة الأولى

### الخطوة 1: إعداد النظام
1. بعد تشغيل المشروع، انتقل إلى: `http://localhost:5173/setup`
2. اضغط على "إعداد النظام" لإنشاء المدير الافتراضي
3. انتظر حتى اكتمال الإعداد

### الخطوة 2: تسجيل الدخول
بعد إكمال الإعداد، استخدم البيانات التالية:

- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Strategic@123`

⚠️ **مهم**: يُنصح بتغيير كلمة المرور بعد تسجيل الدخول الأول

## النشر على Netlify

تم إعداد المشروع للنشر على Netlify:

1. **ملف `netlify.toml`**: يحتوي على إعدادات البناء والتوجيه
2. **ملف `public/_redirects`**: لضمان عمل التوجيه بشكل صحيح
3. **متغيرات البيئة**: مُعدة في ملف التكوين

### خطوات النشر:

1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Netlify
3. سيتم النشر تلقائياً

## قاعدة البيانات

تم إنشاء مشروع Supabase جديد خصيصاً لهذا النظام:

- **اسم المشروع**: strategic-planning-system
- **المعرف**: jqtguuuzezhorpyydhwz
- **المنطقة**: ap-southeast-2

## الهيكل

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
├── contexts/           # سياقات React (Auth)
├── lib/               # إعدادات المكتبات (Supabase)
├── pages/             # صفحات التطبيق
└── main.tsx           # نقطة الدخول الرئيسية
```

## المساهمة

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اعمل Commit للتغييرات
4. ادفع إلى الفرع
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
