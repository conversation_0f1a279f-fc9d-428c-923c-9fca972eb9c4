import React, { useState, useEffect, useRef } from 'react';
import { Users as UsersIcon, Plus, Search, Filter, Shield, Mail, Phone, Edit, Trash2, Eye, EyeOff, Save, Loader2, User, Settings } from 'lucide-react';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { UserService, type ExtendedUserProfile } from '../../lib/userService';
import type { Database } from '../../lib/database.types';

type Role = Database['public']['Tables']['roles']['Row'];
type Department = Database['public']['Tables']['departments']['Row'];

// مكون النموذج المدمج للمستخدمين
const InlineUserForm = ({ editData, onSave, onCancel, onDataChange, availableDepartments = [] }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    email: editData?.email || '',
    phone: editData?.phone || '',
    role: editData?.role || 'موظف',
    department: editData?.department || '',
    position: editData?.position || '',
    status: editData?.status || 'نشط',
    permissions: editData?.permissions || ['قراءة'],
    // حقول إضافية للمديرين
    experience: editData?.experience || '',
    qualification: editData?.qualification || '',
    responsibilities: editData?.responsibilities || '',
    joinDate: editData?.join_date || editData?.joinDate || ''
  });

  const [errors, setErrors] = useState({});
  
  useEffect(() => {
    if (editData) {
      setFormData({
        name: editData?.name || '',
        email: editData?.email || '',
        phone: editData?.phone || '',
        role: editData?.role || 'موظف',
        department: editData?.department || '',
        position: editData?.position || '',
        status: editData?.status || 'نشط',
        permissions: editData?.permissions || ['قراءة'],
        // حقول إضافية للمديرين
        experience: editData?.experience || '',
        qualification: editData?.qualification || '',
        responsibilities: editData?.responsibilities || '',
        joinDate: editData?.join_date || editData?.joinDate || ''
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المستخدم مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'صيغة البريد الإلكتروني غير صحيحة';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'الإدارة مطلوبة';
    }

    if (!formData.position.trim()) {
      newErrors.position = 'المنصب مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        id: editData?.id || `user-${Date.now()}`,
        lastLogin: editData?.lastLogin || new Date().toISOString(),
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث المستخدم' : 'تم إضافة المستخدم', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (onDataChange) {
      onDataChange(true);
    }
  };

  const handlePermissionChange = (permission, checked) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        permissions: [...prev.permissions, permission]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(p => p !== permission)
      }));
    }
    if (onDataChange) {
      onDataChange(true);
    }
  };

  const availablePermissions = [
    'قراءة',
    'كتابة',
    'حذف',
    'إدارة المستخدمين',
    'إدارة الصلاحيات',
    'عرض التقارير',
    'إنشاء التقارير',
    'إدارة النظام'
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ المستخدم'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              اسم المستخدم *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل اسم المستخدم"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.email ? 'border-red-500' : ''}`}
                placeholder="<EMAIL>"
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                رقم الهاتف
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                placeholder="+966 50 123 4567"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الدور الوظيفي
              </label>
              <select
                value={formData.role}
                onChange={(e) => handleChange('role', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="مدير النظام">مدير النظام</option>
                <option value="مدير إدارة">مدير إدارة</option>
                <option value="مشرف">مشرف</option>
                <option value="موظف">موظف</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                حالة المستخدم
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="نشط">نشط</option>
                <option value="غير نشط">غير نشط</option>
                <option value="محظور">محظور</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الإدارة *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الإدارة</option>
                {availableDepartments.map((dept, index) => (
                  <option key={index} value={dept}>{dept}</option>
                ))}
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                المنصب *
              </label>
              <input
                type="text"
                value={formData.position}
                onChange={(e) => handleChange('position', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.position ? 'border-red-500' : ''}`}
                placeholder="المنصب الوظيفي"
              />
              {errors.position && <p className="text-red-500 text-xs mt-1">{errors.position}</p>}
            </div>
          </div>

          {/* حقول إضافية للمديرين */}
          {(formData.role?.includes('مدير') || formData.role === 'مدير') && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    سنوات الخبرة
                  </label>
                  <input
                    type="text"
                    value={formData.experience}
                    onChange={(e) => handleChange('experience', e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                    placeholder="مثال: 5 سنوات"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    المؤهل العلمي
                  </label>
                  <input
                    type="text"
                    value={formData.qualification}
                    onChange={(e) => handleChange('qualification', e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                    placeholder="مثال: ماجستير إدارة أعمال"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  تاريخ الانضمام
                </label>
                <input
                  type="date"
                  value={formData.joinDate}
                  onChange={(e) => handleChange('joinDate', e.target.value)}
                  className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  المسؤوليات الرئيسية
                </label>
                <textarea
                  value={formData.responsibilities}
                  onChange={(e) => handleChange('responsibilities', e.target.value)}
                  rows={3}
                  className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                  placeholder="اكتب المسؤوليات الرئيسية للمدير..."
                />
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              الصلاحيات
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availablePermissions.map(permission => (
                <label key={permission} className="flex items-center space-x-2 space-x-reverse cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.permissions.includes(permission)}
                    onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{permission}</span>
                </label>
              ))}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

const Users = () => {
  // حالة البيانات الحقيقية من قاعدة البيانات
  const [users, setUsers] = useState<ExtendedUserProfile[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [dataLoading, setDataLoading] = useState(true);

  // حالة واجهة المستخدم
  const [selectedItem, setSelectedItem] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('الجميع');
  const [isResizing, setIsResizing] = useState(false);
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('usersListWidth');
      return saved ? parseFloat(saved) : 40;
    }
    return 40;
  });

  const containerRef = useRef(null);
  const notifications = useNotifications();

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setDataLoading(true);
      const [usersData, departmentNames] = await Promise.all([
        UserService.getAllUsers(),
        UserService.getDepartmentNames()
      ]);

      setUsers(usersData);
      setAvailableDepartments(departmentNames);
    } catch (error) {
      console.error('Error loading data:', error);
      notifications.error('خطأ', 'فشل في تحميل البيانات');
    } finally {
      setDataLoading(false);
    }
  };

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('usersListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // دالة حفظ المستخدم
  const handleSaveUser = async (userData) => {
    try {
      if (editingItem) {
        // تحديث مستخدم موجود
        const updatedUser = await UserService.updateUser(editingItem.id, {
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          role: userData.role,
          department: userData.department,
          position: userData.position,
          permissions: userData.permissions,
          // حقول إضافية للمديرين
          experience: userData.experience,
          qualification: userData.qualification,
          responsibilities: userData.responsibilities,
          join_date: userData.joinDate
        });

        setUsers(prev =>
          prev.map(user =>
            user.id === editingItem.id ? updatedUser : user
          )
        );
        setSelectedItem(updatedUser);
        notifications.success('تم التحديث', 'تم تحديث بيانات المستخدم بنجاح');
      } else {
        // إنشاء مستخدم جديد
        const newUser = await UserService.createUser({
          email: userData.email,
          password: 'temp123456', // كلمة مرور مؤقتة
          full_name: userData.name,
          role: userData.role,
          department: userData.department,
          position: userData.position,
          // حقول إضافية للمديرين
          experience: userData.experience,
          qualification: userData.qualification,
          responsibilities: userData.responsibilities,
          join_date: userData.joinDate
        });

        setUsers(prev => [...prev, newUser]);
        setSelectedItem(newUser);
        notifications.success('تم الإضافة', 'تم إضافة المستخدم بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving user:', error);
      notifications.error('خطأ', 'فشل في حفظ المستخدم');
      throw error;
    }
  };

  const handleDeleteUser = async (userId) => {
    const user = users.find(u => u.id === userId);
    if (window.confirm(`هل أنت متأكد من حذف المستخدم ${user?.name}؟`)) {
      try {
        await UserService.deleteUser(userId);
        setUsers(prev => prev.filter(user => user.id !== userId));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف المستخدم بنجاح');
      } catch (error) {
        console.error('Error deleting user:', error);
        notifications.error('خطأ', 'فشل في حذف المستخدم');
      }
    }
  };

  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    if (isEditMode) {
      setEditingItem(item);
    }
  };

  // فلترة المستخدمين
  const filteredUsers = users.filter(user => {
    const userRole = typeof user.role === 'string' ? user.role : user.role?.name || '';
    const userDepartment = typeof user.department === 'string' ? user.department : user.department?.name || '';

    const matchesSearch = !searchTerm ||
                         (user.name || user.full_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         userDepartment.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = !roleFilter || userRole === roleFilter;

    // فلترة حسب التبويب النشط
    let matchesTab = true;
    switch (activeTab) {
      case 'المديرين':
        matchesTab = userRole?.includes('مدير') || userRole === 'مدير';
        break;
      case 'المنسقين':
        matchesTab = userRole?.includes('منسق') || user.position?.includes('منسق');
        break;
      case 'الأخصائيين':
        matchesTab = userRole?.includes('أخصائي') || user.position?.includes('أخصائي');
        break;
      case 'المعطلين':
        matchesTab = user.status !== 'نشط';
        break;
      case 'الجميع':
      default:
        matchesTab = true;
        break;
    }

    return matchesSearch && matchesRole && matchesTab;
  });
  
  const getRoleColor = (role) => {
    switch (role) {
      case 'مدير النظام':
        return 'bg-error-100 text-error-800';
      case 'مدير إدارة':
        return 'bg-warning-100 text-warning-800';
      case 'مشرف':
        return 'bg-primary-100 text-primary-800';
      case 'موظف':
        return 'bg-success-100 text-success-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط':
        return 'bg-success-100 text-success-800';
      case 'غير نشط':
        return 'bg-neutral-100 text-neutral-800';
      case 'محظور':
        return 'bg-error-100 text-error-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.name}</h3>
            {isBeingEdited && (
              <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                قيد التحرير
              </span>
            )}
          </div>
          <div className="flex gap-1">
            <span className={`badge text-xs ${getRoleColor(typeof item.role === 'string' ? item.role : item.role?.name || '')}`}>
              {typeof item.role === 'string' ? item.role : item.role?.name || ''}
            </span>
            <span className={`badge text-xs ${getStatusColor(item.status)}`}>
              {item.status}
            </span>
          </div>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-xs mb-2">
          {item.position} - {item.department}
        </p>
        
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{item.email}</span>
          <span>{item.permissions.length} صلاحية</span>
        </div>
      </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineUserForm
            editData={editingItem}
            onSave={handleSaveUser}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
            availableDepartments={availableDepartments}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <User className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر مستخدم لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر مستخدم من القائمة لعرض المعلومات التفصيلية والصلاحيات</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-blue-500">
                <User size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.name}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full badge ${getRoleColor(typeof selectedItem.role === 'string' ? selectedItem.role : selectedItem.role?.name || '')}`}>
                    {typeof selectedItem.role === 'string' ? selectedItem.role : selectedItem.role?.name || ''}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full badge ${getStatusColor(selectedItem.status)}`}>
                    {selectedItem.status}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {selectedItem.position} - {selectedItem.department}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={() => handleEdit(selectedItem)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                title="تعديل"
              >
                <Edit size={18} />
              </button>
              <button 
                onClick={() => handleDeleteUser(selectedItem.id)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                title="حذف"
              >
                <Trash2 size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* معلومات المستخدم */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">المعلومات الشخصية</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">البريد الإلكتروني</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.email}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">رقم الهاتف</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.phone}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الإدارة</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.department}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">المنصب</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.position}</p>
                  </div>

                  {/* معلومات إضافية للمديرين */}
                  {(selectedItem.role?.includes('مدير') || selectedItem.role === 'مدير') && (
                    <>
                      {selectedItem.experience && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400 text-xs block">سنوات الخبرة</span>
                          <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.experience}</p>
                        </div>
                      )}
                      {selectedItem.qualification && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400 text-xs block">المؤهل العلمي</span>
                          <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.qualification}</p>
                        </div>
                      )}
                      {selectedItem.join_date && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400 text-xs block">تاريخ الانضمام</span>
                          <p className="text-gray-900 dark:text-white text-sm font-medium">
                            {new Date(selectedItem.join_date).toLocaleDateString('ar-SA')}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* المسؤوليات للمديرين */}
              {(selectedItem.role?.includes('مدير') || selectedItem.role === 'مدير') && selectedItem.responsibilities && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-2">المسؤوليات الرئيسية</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {selectedItem.responsibilities}
                  </p>
                </div>
              )}

              {/* الصلاحيات */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    الصلاحيات والأذونات
                  </div>
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {selectedItem.permissions.map((permission, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-900 dark:text-white">{permission}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* معلومات النشاط */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">معلومات النشاط</h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Eye className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400 text-sm">آخر دخول:</span>
                    <span className="text-gray-900 dark:text-white text-sm">
                      {new Date(selectedItem.lastLogin).toLocaleDateString('ar-SA')} - {new Date(selectedItem.lastLogin).toLocaleTimeString('ar-SA')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Settings className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400 text-sm">تاريخ الإنشاء:</span>
                    <span className="text-gray-900 dark:text-white text-sm">
                      {new Date(selectedItem.createdAt).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400 text-sm">مستوى الوصول:</span>
                    <span className={`badge text-xs ${getRoleColor(typeof selectedItem.role === 'string' ? selectedItem.role : selectedItem.role?.name || '')}`}>
                      {typeof selectedItem.role === 'string' ? selectedItem.role : selectedItem.role?.name || ''}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  if (dataLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-neutral-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <div className="flex items-center gap-2">
          <UsersIcon className="w-5 h-5 text-primary-600" />
          <h1 className="text-2xl font-bold text-neutral-800">مستخدمو النظام</h1>
        </div>
        <p className="text-neutral-600 text-sm mt-1">إدارة مستخدمي النظام وصلاحياتهم</p>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex overflow-x-auto">
          {['الجميع', 'المديرين', 'المنسقين', 'الأخصائيين', 'المعطلين'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-6 py-3 border-b-2 whitespace-nowrap transition-colors ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              {tab}
              <span className="ml-2 text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">
                {tab === 'الجميع' ? users.length :
                 tab === 'المديرين' ? users.filter(u => {
                   const userRole = typeof u.role === 'string' ? u.role : u.role?.name || '';
                   return userRole?.includes('مدير') || userRole === 'مدير';
                 }).length :
                 tab === 'المنسقين' ? users.filter(u => {
                   const userRole = typeof u.role === 'string' ? u.role : u.role?.name || '';
                   return userRole?.includes('منسق') || u.position?.includes('منسق');
                 }).length :
                 tab === 'الأخصائيين' ? users.filter(u => {
                   const userRole = typeof u.role === 'string' ? u.role : u.role?.name || '';
                   return userRole?.includes('أخصائي') || u.position?.includes('أخصائي');
                 }).length :
                 tab === 'المعطلين' ? users.filter(u => u.status !== 'نشط').length : 0}
              </span>
            </button>
          ))}
        </div>
      </div>
      
      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-200px)]" ref={containerRef}>
        {/* List Panel */}
        <div 
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
          style={{ width: `${listWidth}%` }}
        >
          {/* Search Header */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 w-5 h-5" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pr-10 pl-4 py-2 focus:outline-none text-sm"
                  placeholder="بحث عن مستخدم..."
                />
              </div>
              
              <select 
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[120px]"
              >
                <option value="">جميع الأدوار</option>
                <option value="مدير النظام">مدير النظام</option>
                <option value="مدير إدارة">مدير إدارة</option>
                <option value="مشرف">مشرف</option>
                <option value="موظف">موظف</option>
              </select>
              
              <button 
                onClick={handleAddNew}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
              >
                <Plus className="w-4 h-4" />
                <span>جديد</span>
              </button>
            </div>
          </div>
          
          {/* List Items */}
          <div className="flex-1 overflow-y-auto">
            <div className="space-y-1 p-2">
              {filteredUsers.map(user => (
                <ListItem 
                  key={user.id} 
                  item={user} 
                  onClick={handleItemClick}
                  isSelected={selectedItem?.id === user.id}
                />
              ))}
            </div>
          </div>
        </div>
        
        {/* Resize Handle */}
        <div 
          className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
            isResizing 
              ? 'bg-blue-500' 
              : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
          }`}
          onMouseDown={() => setIsResizing(true)}
        >
          <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
        </div>

        {/* Detail Panel */}
        <div className="flex-1 overflow-hidden">
          <DetailPanel />
        </div>
      </div>
    </div>
  );
};

export default Users;