import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Plus, Calendar, Target, TrendingUp, Users, BarChart3, FileText, CheckCircle, Clock, AlertCircle, ChevronDown, MoreHorizontal, Edit, Trash2, Shield, UserCheck, Eye, Settings, Award, Sun, Moon, ArrowLeft, X, Lightbulb, Star, Zap, Save, Loader2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../lib/database.types';

type Initiative = Database['public']['Tables']['initiatives']['Row'];
type InitiativeInsert = Database['public']['Tables']['initiatives']['Insert'];

// مكون النموذج المدمج
const InlineInitiativeForm = ({ editData, onSave, onCancel, onDataChange }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    objective: editData?.objective || '',
    relatedKPI: editData?.relatedKPI || '',
    department: editData?.department || '',
    manager: editData?.manager || '',
    priority: editData?.priority || 'متوسطة',
    startDate: editData?.startDate || '',
    endDate: editData?.endDate || '',
    budget: editData?.budget || '',
    participants: editData?.participants || '',
    expectedOutcome: editData?.expectedOutcome || '',
    successMetrics: editData?.successMetrics || '',
    status: editData?.status || 'جديدة',
    progress: editData?.progress || 0
  });

  const [errors, setErrors] = useState({});
  
  // تحديث البيانات عند تغيير editData
  useEffect(() => {
    if (editData) {
      setFormData({
        title: editData?.title || '',
        description: editData?.description || '',
        objective: editData?.objective || '',
        relatedKPI: editData?.relatedKPI || '',
        department: editData?.department || '',
        manager: editData?.manager || '',
        priority: editData?.priority || 'متوسطة',
        startDate: editData?.startDate || '',
        endDate: editData?.endDate || '',
        budget: editData?.budget || '',
        participants: editData?.participants || '',
        expectedOutcome: editData?.expectedOutcome || '',
        successMetrics: editData?.successMetrics || '',
        status: editData?.status || 'جديدة',
        progress: editData?.progress || 0
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان المشروع مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب';
    }

    if (!formData.relatedKPI.trim()) {
      newErrors.relatedKPI = 'مؤشر الأداء المرتبط مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'الإدارة المسؤولة مطلوبة';
    }

    if (!formData.manager.trim()) {
      newErrors.manager = 'مدير المشروع مطلوب';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية مطلوب';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        participants: formData.participants ? parseInt(formData.participants) : 0,
        id: editData?.id || `initiative-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث المشروع' : 'تم إضافة المشروع', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    // إشعار المكون الأب بوجود تغييرات غير محفوظة
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* أزرار التحكم في الأعلى */}
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ المشروع'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      {/* محتوى النموذج */}
      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* العنوان */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              عنوان المشروع *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان المشروع"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف المشروع *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للمشروع"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* السطر الأول: مؤشر الأداء المرتبط والإدارة ومدير المشروع والأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                مؤشر الأداء المرتبط *
              </label>
              <select
                value={formData.relatedKPI || ''}
                onChange={(e) => handleChange('relatedKPI', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.relatedKPI ? 'border-red-500' : ''}`}
              >
                <option value="">اختر مؤشر الأداء</option>
                <option value="نسبة رضا المستفيدين">نسبة رضا المستفيدين</option>
                <option value="معدل إنجاز المشاريع">معدل إنجاز المشاريع</option>
                <option value="عدد المستفيدين الجدد">عدد المستفيدين الجدد</option>
                <option value="معدل الحضور في البرامج">معدل الحضور في البرامج</option>
                <option value="نسبة التوظيف">نسبة التوظيف</option>
                <option value="معدل النمو السنوي">معدل النمو السنوي</option>
              </select>
              {errors.relatedKPI && <p className="text-red-500 text-xs mt-1">{errors.relatedKPI}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الإدارة المسؤولة *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الإدارة</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                مدير المشروع *
              </label>
              <input
                type="text"
                value={formData.manager}
                onChange={(e) => handleChange('manager', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.manager ? 'border-red-500' : ''}`}
                placeholder="اسم مدير المشروع"
              />
              {errors.manager && <p className="text-red-500 text-xs mt-1">{errors.manager}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                مستوى الأولوية
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="عالية">عالية</option>
                <option value="متوسطة">متوسطة</option>
                <option value="منخفضة">منخفضة</option>
              </select>
            </div>
          </div>

          {/* السطر الثاني: التواريخ والميزانية وعدد المستفيدين */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.endDate ? 'border-red-500' : ''}`}
              />
              {errors.endDate && <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الميزانية المقدرة (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                عدد المستفيدين المتوقع
              </label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => handleChange('participants', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          {/* النتائج المتوقعة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              النتائج المتوقعة
            </label>
            <textarea
              value={formData.expectedOutcome}
              onChange={(e) => handleChange('expectedOutcome', e.target.value)}
              rows={2}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="النتائج والمخرجات المتوقعة من المشروع"
            />
          </div>

          {/* مؤشرات النجاح */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              مؤشرات النجاح
            </label>
            <textarea
              value={formData.successMetrics}
              onChange={(e) => handleChange('successMetrics', e.target.value)}
              rows={2}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="المؤشرات التي ستقيس نجاح المشروع"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

// بيانات المشاريع
const initialProjects = [
  {
    id: 1,
    title: 'مشروع تطوير المهارات الرقمية للشباب',
    description: 'برنامج شامل لتدريب الشباب على أحدث التقنيات والمهارات الرقمية مع التركيز على البرمجة والذكاء الاصطناعي',
    objective: 'تطوير مهارات الشباب التقنية والرقمية',
    relatedKPI: 'نسبة التوظيف',
    department: 'إدارة التطوير والابتكار',
    manager: 'أحمد محمد السالم',
    status: 'جاري التنفيذ',
    priority: 'عالية',
    progress: 75,
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    budget: 850000,
    participants: 200,
    expectedOutcome: 'تدريب 200 شاب على المهارات الرقمية وتأهيلهم لسوق العمل التقني',
    successMetrics: 'عدد المتدربين المتخرجين، نسبة النجاح في الاختبارات، معدل التوظيف'
  },
  {
    id: 2,
    title: 'مشروع دعم رواد الأعمال الشباب',
    description: 'توفير الدعم المالي والاستشاري والتدريبي للشباب الراغبين في بدء مشاريعهم الناشئة',
    objective: 'تمكين رواد الأعمال الشباب',
    relatedKPI: 'عدد المستفيدين الجدد',
    department: 'إدارة ريادة الأعمال',
    manager: 'فاطمة أحمد الزهراني',
    status: 'جاري التنفيذ',
    priority: 'عالية',
    progress: 60,
    startDate: '2025-02-01',
    endDate: '2025-11-30',
    budget: 1200000,
    participants: 100,
    expectedOutcome: 'إطلاق 50 مشروع ناشئ وتوفير 300 فرصة عمل جديدة',
    successMetrics: 'عدد المشاريع المدعومة، معدل نجاح المشاريع، حجم الاستثمارات المجذوبة'
  },
  {
    id: 3,
    title: 'مشروع التطوع المجتمعي الشبابي',
    description: 'تنظيم برامج تطوعية متنوعة لتعزيز مشاركة الشباب في خدمة المجتمع وتنمية روح المسؤولية الاجتماعية',
    objective: 'تعزيز المشاركة المجتمعية للشباب',
    relatedKPI: 'معدل إنجاز المشاريع',
    department: 'إدارة البرامج الشبابية',
    manager: 'محمد علي القحطاني',
    status: 'متأخرة',
    priority: 'متوسطة',
    progress: 40,
    startDate: '2025-01-15',
    endDate: '2025-10-15',
    budget: 400000,
    participants: 500,
    expectedOutcome: 'مشاركة 500 متطوع في 50 فعالية تطوعية مجتمعية',
    successMetrics: 'عدد المتطوعين النشطين، ساعات العمل التطوعي، عدد المستفيدين'
  },
  {
    id: 4,
    title: 'مشروع تعزيز الهوية الوطنية والتراث',
    description: 'برنامج ثقافي شامل لترسيخ القيم الوطنية والحفاظ على التراث الثقافي في نفوس الشباب',
    objective: 'تعزيز الهوية الوطنية والقيم المجتمعية',
    relatedKPI: 'معدل الحضور في البرامج',
    department: 'الإدارة الثقافية',
    manager: 'سارة خالد العتيبي',
    status: 'التخطيط',
    priority: 'عالية',
    progress: 25,
    startDate: '2025-03-01',
    endDate: '2025-12-31',
    budget: 600000,
    participants: 300,
    expectedOutcome: 'تنظيم 20 فعالية ثقافية وتراثية وتدريب 100 سفير ثقافي',
    successMetrics: 'عدد الفعاليات الثقافية، نسبة مشاركة الشباب، مستوى الوعي الثقافي'
  }
];

const Initiatives = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [activeView, setActiveView] = useState('الكل');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [kpiFilter, setKpiFilter] = useState('');
  const [objectiveFilter, setObjectiveFilter] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('initiativesListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  
  const { user } = useAuth();
  const notifications = useNotifications();
  
  // State for data management
  const [initiatives, setInitiatives] = useState<Initiative[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableKPIs, setAvailableKPIs] = useState<string[]>([]);
  const [availableObjectives, setAvailableObjectives] = useState<string[]>([]);

  // تحميل البيانات من قاعدة البيانات
  useEffect(() => {
    fetchInitiatives();
    fetchKPIs();
    fetchObjectives();
  }, []);

  const fetchInitiatives = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('initiatives')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setInitiatives(data || []);
    } catch (error) {
      console.error('Error fetching initiatives:', error);
      notifications.error('خطأ في تحميل البيانات', 'فشل في تحميل المشاريع');
    } finally {
      setLoading(false);
    }
  };

  const fetchKPIs = async () => {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('name')
        .order('name', { ascending: true });

      if (error) throw error;
      setAvailableKPIs(data?.map(kpi => kpi.name) || []);
    } catch (error) {
      console.error('Error fetching KPIs:', error);
    }
  };

  const fetchObjectives = async () => {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('title')
        .order('title', { ascending: true });

      if (error) throw error;
      setAvailableObjectives(data?.map(obj => obj.title) || []);
    } catch (error) {
      console.error('Error fetching objectives:', error);
    }
  };

  // تطبيق الوضع الليلي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('initiativesListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتملة': return 'bg-green-500';
      case 'جاري التنفيذ': return 'bg-blue-500';
      case 'التخطيط': return 'bg-yellow-500';
      case 'مجدولة': return 'bg-purple-500';
      case 'متأخرة': return 'bg-red-500';
      case 'معلقة': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'عالية': return 'text-red-400 bg-red-500/20';
      case 'متوسطة': return 'text-yellow-400 bg-yellow-500/20';
      case 'منخفضة': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  // وظائف إدارة المشاريع
  const handleSaveInitiative = async (initiativeData: any) => {
    try {
      if (editingItem) {
        // تحديث مشروع موجود
        const { data, error } = await supabase
          .from('initiatives')
          .update({
            title: initiativeData.title,
            description: initiativeData.description,
            objective: initiativeData.objective,
            related_kpi: initiativeData.relatedKPI,
            department: initiativeData.department,
            manager: initiativeData.manager,
            priority: initiativeData.priority,
            start_date: initiativeData.startDate,
            end_date: initiativeData.endDate,
            budget: initiativeData.budget,
            participants: initiativeData.participants,
            expected_outcome: initiativeData.expectedOutcome,
            success_metrics: initiativeData.successMetrics,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingItem.id)
          .select()
          .single();

        if (error) throw error;

        setInitiatives(prev =>
          prev.map(initiative =>
            initiative.id === editingItem.id ? data : initiative
          )
        );
        setSelectedItem(data);
        notifications.success('تم التحديث', 'تم تحديث المشروع بنجاح');
      } else {
        // إنشاء مشروع جديد
        const { data, error } = await supabase
          .from('initiatives')
          .insert({
            title: initiativeData.title,
            description: initiativeData.description,
            objective: initiativeData.objective,
            related_kpi: initiativeData.relatedKPI,
            department: initiativeData.department,
            manager: initiativeData.manager,
            priority: initiativeData.priority,
            start_date: initiativeData.startDate,
            end_date: initiativeData.endDate,
            budget: initiativeData.budget,
            participants: initiativeData.participants,
            expected_outcome: initiativeData.expectedOutcome,
            success_metrics: initiativeData.successMetrics
          })
          .select()
          .single();

        if (error) throw error;

        setInitiatives(prev => [data, ...prev]);
        setSelectedItem(data);
        notifications.success('تم الإضافة', 'تم إضافة المشروع بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving initiative:', error);
      notifications.error('خطأ', 'فشل في حفظ المشروع');
      throw error;
    }
  };
  
  const handleDeleteInitiative = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
      try {
        const { error } = await supabase
          .from('initiatives')
          .delete()
          .eq('id', id);

        if (error) throw error;

        setInitiatives(prev => prev.filter(initiative => initiative.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف المشروع بنجاح');
      } catch (error) {
        console.error('Error deleting initiative:', error);
        notifications.error('خطأ في الحذف', 'فشل في حذف المشروع');
      }
    }
  };
  
  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    // إذا كنا في وضع التعديل، حدث العنصر المحرر إلى العنصر الجديد
    if (isEditMode) {
      setEditingItem(item);
    }
    // لا نغلق وضع التعديل - نبقيه مفتوحاً
  };

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.title}</h3>
            {isBeingEdited && (
              <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                قيد التحرير
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(item.priority)}`}>
              {item.priority}
            </span>
            <span className={`text-xs px-2 py-1 rounded ${getStatusColor(item.status)} text-white`}>
              {item.status}
            </span>
          </div>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">
          {item.description}
        </p>
        
        <div className="mb-2">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-gray-600 dark:text-gray-400">التقدم</span>
            <span className="text-gray-900 dark:text-white">{item.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div 
              className="bg-blue-500 h-1 rounded-full transition-all"
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
        </div>
        
        <div className="text-gray-600 dark:text-gray-400 text-xs">
          الإدارة: {item.department}
        </div>
      </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    // إذا كنا في وضع الإضافة أو التعديل، اعرض النموذج مباشرة
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineInitiativeForm 
            editData={editingItem}
            onSave={handleSaveInitiative}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Lightbulb className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر مشروع لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر مشروع من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-purple-500">
                <Lightbulb size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.title}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedItem.status)} text-white`}>
                    {selectedItem.status}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">الأولوية:</span>
                    <span className={`px-2 py-0.5 rounded text-xs ${getPriorityColor(selectedItem.priority)}`}>
                      {selectedItem.priority}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {selectedItem.end_date ? new Date(selectedItem.end_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">{selectedItem.department}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasPermission('تعديل') && (
                <>
                  <button 
                    onClick={() => handleEdit(selectedItem)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                    title="تعديل"
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    onClick={() => handleDeleteInitiative(selectedItem.id)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                    title="حذف"
                  >
                    <Trash2 size={18} />
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* التقدم العام */}
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-gray-600 dark:text-gray-400 text-sm">التقدم العام</span>
                <span className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.progress}%</span>
              </div>
              <div className="flex items-center gap-1">
                {selectedItem.progress < 30 && <AlertCircle className="w-4 h-4 text-red-500" />}
                {selectedItem.progress >= 30 && selectedItem.progress < 70 && <Clock className="w-4 h-4 text-yellow-500" />}
                {selectedItem.progress >= 70 && <CheckCircle className="w-4 h-4 text-green-500" />}
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all ${
                  selectedItem.progress < 30 ? 'bg-red-500' :
                  selectedItem.progress < 70 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${selectedItem.progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Content - عرض المعلومات مباشرة */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">الوصف</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* الإحصائيات الرئيسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">الإحصائيات الرئيسية</h4>
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {selectedItem.progress}%
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">نسبة الإنجاز</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">التقدم</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {selectedItem.participants || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">المستفيدين</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">عدد</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {selectedItem.budget ? (selectedItem.budget / 1000000).toFixed(1) : 0}M
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الميزانية</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">ريال</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {selectedItem.end_date ? Math.ceil((new Date(selectedItem.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الأيام المتبقية</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">يوم</div>
                  </div>
                </div>
              </div>

              {/* المعلومات الأساسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">المعلومات الأساسية</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الهدف الاستراتيجي</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.objective}</p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">مدير المشروع</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.manager}</p>
                    </div>
                  </div>
                  
                  {selectedItem.related_kpi && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">مؤشر الأداء المرتبط</span>
                      <div className="flex items-center gap-2 mt-1">
                        <TrendingUp className="w-4 h-4 text-green-500" />
                        <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.related_kpi}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الإدارة المسؤولة</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.department}</p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الحالة الزمنية</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">
                        {selectedItem.end_date && Math.ceil((new Date(selectedItem.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) > 0
                          ? `${Math.ceil((new Date(selectedItem.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم متبقي`
                          : 'متأخر عن الموعد'
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الجدول الزمني</span>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-gray-900 dark:text-white text-sm">
                        {selectedItem.start_date ? new Date(selectedItem.start_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-400 rotate-90" />
                      <span className="text-gray-900 dark:text-white text-sm">
                        {selectedItem.end_date ? new Date(selectedItem.end_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* النتائج المتوقعة */}
              {selectedItem.expected_outcome && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-2">النتائج المتوقعة</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {selectedItem.expected_outcome}
                  </p>
                </div>
              )}

              {/* مؤشرات النجاح */}
              {selectedItem.success_metrics && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-2">مؤشرات النجاح</h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {selectedItem.success_metrics}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // فلترة المشاريع
  const filteredInitiatives = initiatives.filter(initiative => {
    // فلترة حسب العرض
    let viewFilter = false;
    switch(activeView) {
      case 'الكل': viewFilter = true; break;
      case 'جاري التنفيذ': viewFilter = initiative.status === 'جاري التنفيذ'; break;
      case 'مكتملة': viewFilter = initiative.status === 'مكتملة'; break;
      case 'متأخرة': viewFilter = initiative.status === 'متأخرة' || (initiative.status === 'جاري التنفيذ' && initiative.end_date && new Date(initiative.end_date) < new Date()); break;
      default: viewFilter = true;
    }
    
    // فلترة حسب البحث
    const searchFilter = !searchTerm || 
      initiative.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      initiative.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      initiative.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      initiative.manager.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلترة حسب الحالة
    const statusMatch = !statusFilter || initiative.status === statusFilter;
    
    // فلترة حسب الأولوية  
    const priorityMatch = !priorityFilter || initiative.priority === priorityFilter;
    
    // فلترة حسب الإدارة
    const departmentMatch = !departmentFilter || initiative.department === departmentFilter;
    
    // فلترة حسب مؤشر الأداء المرتبط
    const kpiMatch = !kpiFilter || initiative.related_kpi === kpiFilter;

    // فلترة حسب الهدف المرتبط
    const objectiveMatch = !objectiveFilter || initiative.objective === objectiveFilter;

    return viewFilter && searchFilter && statusMatch && priorityMatch && departmentMatch && kpiMatch && objectiveMatch;
  });

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">المشاريع</h1>
        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">إدارة ومتابعة المشاريع والبرامج الاستراتيجية</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex overflow-x-auto">
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'الكل' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('الكل')}
          >
            جميع المشاريع
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'جاري التنفيذ' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('جاري التنفيذ')}
          >
            قيد التنفيذ
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'مكتملة' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('مكتملة')}
          >
            مكتملة
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'متأخرة' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('متأخرة')}
          >
            متأخرة
          </button>
        </div>
        
        {/* Add Button */}
        <div className="px-4">
          {hasPermission('إضافة') && (
            <button 
              onClick={handleAddNew}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
            >
              <Plus size={16} />
              جديد
            </button>
          )}
        </div>
      </div>

      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
          {/* List Panel */}
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
            style={{ width: `${listWidth}%` }}
          >
            {/* Search Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              {/* البحث والفلاتر متجاوبة مع العرض */}
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400" size={16} />
                  <input 
                    type="text" 
                    placeholder="البحث..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm"
                  />
                </div>
                
                {/* الفلاتر - تتجاوب مع عرض القائمة */}
                <div className={`grid gap-2 ${listWidth > 40 ? 'grid-cols-3' : listWidth > 30 ? 'grid-cols-2' : 'grid-cols-1'}`}>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الحالة</option>
                    <option value="جاري التنفيذ">جاري التنفيذ</option>
                    <option value="مكتملة">مكتملة</option>
                    <option value="متأخرة">متأخرة</option>
                    <option value="التخطيط">التخطيط</option>
                    <option value="معلقة">معلقة</option>
                  </select>
                  
                  <select
                    value={priorityFilter}
                    onChange={(e) => setPriorityFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الأولوية</option>
                    <option value="عالية">عالية</option>
                    <option value="متوسطة">متوسطة</option>
                    <option value="منخفضة">منخفضة</option>
                  </select>
                  
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الإدارة</option>
                    <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                    <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                    <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                    <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                  </select>
                  
                  <select
                    value={kpiFilter}
                    onChange={(e) => setKpiFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">المؤشر المرتبط</option>
                    {availableKPIs.map((kpi, index) => (
                      <option key={index} value={kpi}>{kpi}</option>
                    ))}
                  </select>

                  <select
                    value={objectiveFilter}
                    onChange={(e) => setObjectiveFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الهدف المرتبط</option>
                    {availableObjectives.map((objective, index) => (
                      <option key={index} value={objective}>{objective}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            
            {/* List Items */}
            <div className="flex-1 overflow-y-auto">
              <div className="space-y-1 p-2">
                {filteredInitiatives.map(initiative => (
                  <ListItem 
                    key={initiative.id} 
                    item={initiative} 
                    onClick={handleItemClick}
                    isSelected={selectedItem?.id === initiative.id}
                  />
                ))}
              </div>
            </div>
          </div>
          
          {/* Resize Handle */}
          <div 
            className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
              isResizing 
                ? 'bg-blue-500' 
                : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
            }`}
            onMouseDown={() => setIsResizing(true)}
          >
            <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
          </div>

          {/* Detail Panel */}
          <div className="flex-1 overflow-hidden">
            <DetailPanel />
          </div>
        </div>

      {/* Role Switcher */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default Initiatives;