import { supabase } from '../src/lib/supabase';

// بيانات المستخدمين المطلوب إضافتهم
const adminUsers = [
  {
    name: 'أحمد الدريهم',
    position: 'مدير قسم التطوع',
    department: 'قسم التطوع',
    email: '<EMAIL>',
    phone: '966531685640',
    role: 'MANAGER'
  },
  {
    name: 'سعد الدريهم',
    position: 'المدير التنفيذي',
    department: 'الإدارة التنفيذية',
    email: '<EMAIL>',
    phone: '966555104096',
    role: 'ADMIN'
  },
  {
    name: 'سعد القحيز',
    position: 'مدير مشاريع',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '966555108628',
    role: 'MANAGER'
  },
  {
    name: 'عبدالعزيز المطرد',
    position: 'مدير مشاريع',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '966544303202',
    role: 'MANAGER'
  },
  {
    name: 'عبدالله المحسن',
    position: 'مدير قسم الموارد المالية',
    department: 'الموارد المالية',
    email: '<EMAIL>',
    phone: '966555106645',
    role: 'MANAGER'
  },
  {
    name: 'معتصم العرفج',
    position: 'مدير قسم البرامج',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '9665608194',
    role: 'MANAGER'
  },
  {
    name: 'ناصر اليحيى',
    position: 'منسق البيئات',
    department: 'البرامج والمشاريع',
    email: '<EMAIL>',
    phone: '966500977270',
    role: 'SUPERVISOR'
  }
];

// دالة للحصول على معرف الدور
async function getRoleId(roleName: string): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from('roles')
      .select('id')
      .eq('name', roleName)
      .single();

    if (error) {
      console.error(`خطأ في جلب الدور ${roleName}:`, error);
      return null;
    }

    return data?.id || null;
  } catch (error) {
    console.error(`خطأ في جلب الدور ${roleName}:`, error);
    return null;
  }
}

// دالة للحصول على معرف القسم أو إنشاؤه
async function getDepartmentId(departmentName: string): Promise<string | null> {
  try {
    // البحث عن القسم الموجود
    const { data: existingDept, error: searchError } = await supabase
      .from('departments')
      .select('id')
      .eq('name', departmentName)
      .single();

    if (!searchError && existingDept) {
      return existingDept.id;
    }

    // إنشاء القسم إذا لم يكن موجوداً
    console.log(`إنشاء قسم جديد: ${departmentName}`);
    const { data: newDept, error: createError } = await supabase
      .from('departments')
      .insert({
        name: departmentName,
        description: `قسم ${departmentName}`,
        status: 'نشط',
        employee_count: 0,
        budget: '0'
      })
      .select('id')
      .single();

    if (createError) {
      console.error(`خطأ في إنشاء القسم ${departmentName}:`, createError);
      return null;
    }

    return newDept?.id || null;
  } catch (error) {
    console.error(`خطأ في معالجة القسم ${departmentName}:`, error);
    return null;
  }
}

// دالة لإنشاء مستخدم في نظام المصادقة
async function createAuthUser(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true
    });

    if (error) {
      console.error(`خطأ في إنشاء مستخدم المصادقة للبريد ${email}:`, error);
      return null;
    }

    return data.user;
  } catch (error) {
    console.error(`خطأ في إنشاء مستخدم المصادقة للبريد ${email}:`, error);
    return null;
  }
}

// دالة لإنشاء ملف المستخدم
async function createUserProfile(userId: string, userData: any, roleId: string, departmentId: string) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        full_name: userData.name,
        role_id: roleId,
        department_id: departmentId,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      console.error(`خطأ في إنشاء ملف المستخدم ${userData.name}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`خطأ في إنشاء ملف المستخدم ${userData.name}:`, error);
    return null;
  }
}

// دالة لإنشاء كلمة مرور عشوائية
function generatePassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// الدالة الرئيسية لإضافة المستخدمين
async function addAdminUsers() {
  console.log('🚀 بدء عملية إضافة المستخدمين الإداريين...');
  
  const results = [];
  
  for (const userData of adminUsers) {
    console.log(`\n📝 معالجة المستخدم: ${userData.name}`);
    
    try {
      // التحقق من وجود المستخدم مسبقاً
      const { data: existingUser } = await supabase
        .from('user_profiles')
        .select('id, full_name')
        .eq('full_name', userData.name)
        .single();

      if (existingUser) {
        console.log(`⚠️  المستخدم ${userData.name} موجود مسبقاً`);
        results.push({
          name: userData.name,
          status: 'موجود مسبقاً',
          email: userData.email
        });
        continue;
      }

      // الحصول على معرف الدور
      const roleId = await getRoleId(userData.role);
      if (!roleId) {
        console.log(`❌ فشل في الحصول على معرف الدور ${userData.role} للمستخدم ${userData.name}`);
        results.push({
          name: userData.name,
          status: 'فشل - دور غير موجود',
          email: userData.email
        });
        continue;
      }

      // الحصول على معرف القسم أو إنشاؤه
      const departmentId = await getDepartmentId(userData.department);
      if (!departmentId) {
        console.log(`❌ فشل في الحصول على معرف القسم ${userData.department} للمستخدم ${userData.name}`);
        results.push({
          name: userData.name,
          status: 'فشل - قسم غير متاح',
          email: userData.email
        });
        continue;
      }

      // إنشاء كلمة مرور عشوائية
      const password = generatePassword();

      // إنشاء مستخدم في نظام المصادقة
      const authUser = await createAuthUser(userData.email, password);
      if (!authUser) {
        console.log(`❌ فشل في إنشاء مستخدم المصادقة للمستخدم ${userData.name}`);
        results.push({
          name: userData.name,
          status: 'فشل - خطأ في المصادقة',
          email: userData.email
        });
        continue;
      }

      // إنشاء ملف المستخدم
      const userProfile = await createUserProfile(authUser.id, userData, roleId, departmentId);
      if (!userProfile) {
        console.log(`❌ فشل في إنشاء ملف المستخدم ${userData.name}`);
        results.push({
          name: userData.name,
          status: 'فشل - خطأ في الملف الشخصي',
          email: userData.email
        });
        continue;
      }

      console.log(`✅ تم إنشاء المستخدم ${userData.name} بنجاح`);
      results.push({
        name: userData.name,
        status: 'تم الإنشاء بنجاح',
        email: userData.email,
        password: password,
        role: userData.role,
        department: userData.department
      });

    } catch (error) {
      console.error(`❌ خطأ عام في معالجة المستخدم ${userData.name}:`, error);
      results.push({
        name: userData.name,
        status: 'فشل - خطأ عام',
        email: userData.email,
        error: error.message
      });
    }
  }

  // طباعة النتائج النهائية
  console.log('\n📊 ملخص النتائج:');
  console.log('==================');
  
  results.forEach(result => {
    console.log(`\n👤 ${result.name}`);
    console.log(`   📧 البريد: ${result.email}`);
    console.log(`   📊 الحالة: ${result.status}`);
    if (result.password) {
      console.log(`   🔑 كلمة المرور: ${result.password}`);
      console.log(`   👔 الدور: ${result.role}`);
      console.log(`   🏢 القسم: ${result.department}`);
    }
    if (result.error) {
      console.log(`   ❌ الخطأ: ${result.error}`);
    }
  });

  const successful = results.filter(r => r.status === 'تم الإنشاء بنجاح').length;
  const existing = results.filter(r => r.status === 'موجود مسبقاً').length;
  const failed = results.filter(r => r.status.includes('فشل')).length;

  console.log(`\n📈 الإحصائيات:`);
  console.log(`   ✅ تم الإنشاء: ${successful}`);
  console.log(`   ⚠️  موجود مسبقاً: ${existing}`);
  console.log(`   ❌ فشل: ${failed}`);
  console.log(`   📊 المجموع: ${results.length}`);

  return results;
}

// تشغيل السكريبت
if (require.main === module) {
  addAdminUsers()
    .then(() => {
      console.log('\n🎉 انتهت عملية إضافة المستخدمين الإداريين');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ خطأ في تشغيل السكريبت:', error);
      process.exit(1);
    });
}

export { addAdminUsers };
