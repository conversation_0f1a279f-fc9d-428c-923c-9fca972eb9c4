# 🗄️ دليل إعداد قاعدة البيانات

## 🚨 **المشكلة المكتشفة:**
قاعدة البيانات الجديدة فارغة ولا تحتوي على:
- ❌ جدول `roles` 
- ❌ جدول `departments`
- ❌ جدول `user_profiles`
- ❌ العلاقات بين الجداول
- ❌ سياسات الأمان (RLS)

## 🛠️ **الحل السريع:**

### **الخطوة 1: إعداد قاعدة البيانات**

1. **اذهب إلى Supabase Dashboard:**
   ```
   https://supabase.com/dashboard/project/ncsqltgvfioneovskwxg
   ```

2. **افتح SQL Editor:**
   - اضغط على "SQL Editor" في القائمة الجانبية
   - اضغط "New query"

3. **انسخ والصق المحتوى:**
   - افتح ملف: `supabase/setup_database.sql`
   - ان<PERSON>خ المحتوى كاملاً
   - الصق في SQL Editor
   - اضغط "Run" أو Ctrl+Enter

### **الخطوة 2: التحقق من النجاح**

بعد تشغيل SQL، تحقق من إنشاء الجداول:

```sql
-- تحقق من الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- تحقق من البيانات الأساسية
SELECT * FROM public.roles;
SELECT * FROM public.departments;
```

### **الخطوة 3: اختبار النظام**

1. **اذهب إلى موقعك:**
   ```
   https://your-site.netlify.app/setup
   ```

2. **اضغط "إعداد النظام"**
   - سيتم إنشاء المدير الافتراضي
   - سيتم ربطه بالأدوار والأقسام

3. **سجل دخول:**
   - البريد: `<EMAIL>`
   - كلمة المرور: `Strategic@123`

## 📋 **الجداول التي سيتم إنشاؤها:**

### **الجداول الأساسية:**
- ✅ `roles` - أدوار المستخدمين
- ✅ `departments` - الأقسام والإدارات  
- ✅ `user_profiles` - ملفات المستخدمين

### **جداول النظام:**
- ✅ `strategic_objectives` - الأهداف الاستراتيجية
- ✅ `kpis` - مؤشرات الأداء
- ✅ `projects` - المشاريع
- ✅ `activities` - الأنشطة

### **البيانات الافتراضية:**

#### **الأدوار:**
- `ADMIN` - مدير النظام
- `MANAGER` - مدير إدارة
- `SUPERVISOR` - مشرف
- `EMPLOYEE` - موظف

#### **الأقسام:**
- الإدارة العامة
- إدارة التطوير والابتكار
- إدارة ريادة الأعمال
- إدارة البرامج الشبابية
- الإدارة الثقافية
- إدارة المرافق

## 🔒 **الأمان (RLS):**

### **تم تفعيل:**
- ✅ Row Level Security على جميع الجداول
- ✅ سياسات القراءة والكتابة
- ✅ حماية بيانات المستخدمين
- ✅ التحقق من الهوية

### **الصلاحيات:**
- **القراءة**: متاحة للمستخدمين المسجلين
- **الكتابة**: متاحة للمستخدمين المسجلين
- **الملفات الشخصية**: كل مستخدم يرى ملفه فقط

## ⚡ **تحسينات الأداء:**

### **الفهارس المُنشأة:**
- فهرس على `user_profiles.role_id`
- فهرس على `user_profiles.department_id`
- فهرس على العلاقات الأساسية

### **المحفزات (Triggers):**
- تحديث `updated_at` تلقائياً
- تتبع التغييرات

## 🚨 **استكشاف الأخطاء:**

### **إذا فشل تشغيل SQL:**
1. تأكد من نسخ المحتوى كاملاً
2. تحقق من عدم وجود أخطاء إملائية
3. جرب تشغيل أجزاء منفصلة

### **إذا لم تظهر الجداول:**
```sql
-- تحقق من الجداول الموجودة
\dt public.*

-- أو
SELECT * FROM information_schema.tables 
WHERE table_schema = 'public';
```

### **إذا لم تعمل العلاقات:**
```sql
-- تحقق من المفاتيح الخارجية
SELECT * FROM information_schema.table_constraints 
WHERE constraint_type = 'FOREIGN KEY';
```

## 📞 **الدعم:**

إذا واجهت مشاكل:
1. تحقق من logs في Supabase
2. راجع رسائل الخطأ في Console
3. تأكد من صحة بيانات الاتصال

---

**بعد إكمال هذه الخطوات، سيعمل النظام بشكل كامل!** 🎯
