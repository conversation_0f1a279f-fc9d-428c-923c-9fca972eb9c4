import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { User, Session } from '@supabase/supabase-js';
import { UserService, type ExtendedUserProfile } from '../lib/userService';
import { initializeDefaultAdmin } from '../lib/initializeAdmin';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  userProfile: ExtendedUserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<ExtendedUserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // دالة لجلب ملف المستخدم
  const refreshProfile = async () => {
    try {
      const profile = await UserService.getCurrentUserProfile();
      setUserProfile(profile);
    } catch (error) {
      console.error('Error refreshing user profile:', error);
      setUserProfile(null);
    }
  };

  useEffect(() => {
    // Set up a subscription to auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        // جلب ملف المستخدم عند تسجيل الدخول
        if (session?.user) {
          await refreshProfile();
        } else {
          setUserProfile(null);
        }

        setLoading(false);
      }
    );

    // Initial session check
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);

      // جلب ملف المستخدم إذا كان مسجل الدخول
      if (session?.user) {
        await refreshProfile();
      }

      setLoading(false);
    });

    return () => {
      // Clean up the subscription
      authListener.subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (!error) {
      navigate('/dashboard');
    }
    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  const value = {
    user,
    session,
    userProfile,
    loading,
    signIn,
    signOut,
    refreshProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}