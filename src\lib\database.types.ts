export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      departments: {
        Row: {
          id: string
          name: string
          description: string | null
          parent_id: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "departments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          }
        ]
      }
      strategic_objectives: {
        Row: {
          id: string
          title: string
          description: string | null
          owner: string
          department: string
          priority: string
          status: string
          progress: number
          start_date: string | null
          end_date: string | null
          budget: number
          expected_outcome: string | null
          success_metrics: string | null
          manager: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          owner: string
          department: string
          priority?: string
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          expected_outcome?: string | null
          success_metrics?: string | null
          manager?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          owner?: string
          department?: string
          priority?: string
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          expected_outcome?: string | null
          success_metrics?: string | null
          manager?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      kpis: {
        Row: {
          id: string
          name: string
          category: string
          objective: string | null
          related_project: string | null
          current_value: number
          target_value: number
          unit: string
          trend: string
          change_percentage: number
          status: string
          description: string | null
          frequency: string
          data_source: string | null
          formula: string | null
          responsible: string
          last_updated: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          category: string
          objective?: string | null
          related_project?: string | null
          current_value?: number
          target_value: number
          unit: string
          trend?: string
          change_percentage?: number
          status?: string
          description?: string | null
          frequency?: string
          data_source?: string | null
          formula?: string | null
          responsible: string
          last_updated?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          category?: string
          objective?: string | null
          related_project?: string | null
          current_value?: number
          target_value?: number
          unit?: string
          trend?: string
          change_percentage?: number
          status?: string
          description?: string | null
          frequency?: string
          data_source?: string | null
          formula?: string | null
          responsible?: string
          last_updated?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      initiatives: {
        Row: {
          id: string
          title: string
          description: string | null
          objective: string | null
          related_kpi: string | null
          department: string
          manager: string
          priority: string
          status: string
          progress: number
          start_date: string | null
          end_date: string | null
          budget: number
          participants: number
          expected_outcome: string | null
          success_metrics: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          objective?: string | null
          related_kpi?: string | null
          department: string
          manager: string
          priority?: string
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          participants?: number
          expected_outcome?: string | null
          success_metrics?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          objective?: string | null
          related_kpi?: string | null
          department?: string
          manager?: string
          priority?: string
          status?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          participants?: number
          expected_outcome?: string | null
          success_metrics?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      activities: {
        Row: {
          id: string
          title: string
          description: string | null
          initiative: string | null
          related_project: string | null
          department: string
          assignee: string
          status: string
          priority: string
          progress: number
          start_date: string | null
          end_date: string | null
          budget: number
          participants: number
          estimated_hours: number
          actual_hours: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          initiative?: string | null
          related_project?: string | null
          department: string
          assignee: string
          status?: string
          priority?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          participants?: number
          estimated_hours?: number
          actual_hours?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          initiative?: string | null
          related_project?: string | null
          department?: string
          assignee?: string
          status?: string
          priority?: string
          progress?: number
          start_date?: string | null
          end_date?: string | null
          budget?: number
          participants?: number
          estimated_hours?: number
          actual_hours?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      objective_kpis: {
        Row: {
          id: string
          objective_id: string
          kpi_name: string
          created_at: string
        }
        Insert: {
          id?: string
          objective_id: string
          kpi_name: string
          created_at?: string
        }
        Update: {
          id?: string
          objective_id?: string
          kpi_name?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "objective_kpis_objective_id_fkey"
            columns: ["objective_id"]
            isOneToOne: false
            referencedRelation: "strategic_objectives"
            referencedColumns: ["id"]
          }
        ]
      }
      roles: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          id: string
          full_name: string
          role_id: string | null
          department_id: string | null
          is_active: boolean
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id: string
          full_name: string
          role_id?: string | null
          department_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          full_name?: string
          role_id?: string | null
          department_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_profiles_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Extended interfaces for the application
export interface ExtendedUserProfile {
  id: string;
  full_name: string;
  name?: string;
  email?: string;
  phone?: string;
  role?: string;
  department?: string;
  position?: string;
  status?: string;
  permissions?: string[];
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  lastLogin?: string;
  createdAt?: string;
}