import React, { useState } from 'react';
import { Brain, Copy, Download, Upload, Sparkles, CheckCircle, AlertCircle, Code, FileText } from 'lucide-react';
import { useNotifications } from '../ui/NotificationSystem';

interface AIImportProps {
  dataType: 'activities' | 'departments' | 'users' | 'indicators' | 'projects';
  onImport: (data: any[]) => Promise<void>;
}

const AIImport: React.FC<AIImportProps> = ({ dataType, onImport }) => {
  const [step, setStep] = useState<'prompt' | 'paste' | 'preview' | 'importing'>('prompt');
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [userInput, setUserInput] = useState('');
  const [parsedData, setParsedData] = useState<any[]>([]);
  const [itemCount, setItemCount] = useState(5);
  const notifications = useNotifications();

  // قوالب البيانات لكل نوع
  const dataTemplates = {
    activities: {
      name: 'الأنشطة',
      fields: {
        title: 'عنوان النشاط (نص مطلوب)',
        description: 'وصف النشاط (نص)',
        initiative: 'المبادرة المرتبطة (نص)',
        related_project: 'المشروع المرتبط (نص)',
        department: 'القسم المسؤول (نص مطلوب)',
        assignee: 'المسؤول عن النشاط (نص مطلوب)',
        status: 'الحالة (جاري التنفيذ/مكتمل/متأخر/متعثر)',
        priority: 'الأولوية (عالية/منخفضة)',
        progress: 'نسبة الإنجاز (رقم صحيح من 0 إلى 100)',
        start_date: 'تاريخ البداية (YYYY-MM-DD)',
        end_date: 'تاريخ النهاية (YYYY-MM-DD)',
        budget: 'الميزانية (رقم)',
        participants: 'عدد المشاركين (رقم صحيح)',
        estimated_hours: 'الساعات المقدرة (رقم صحيح)',
        actual_hours: 'الساعات الفعلية (رقم صحيح)'
      },
      constraints: [
        'title و department و assignee مطلوبة',
        'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
        'نسبة الإنجاز رقم صحيح بين 0 و 100',
        'الميزانية وعدد المشاركين والساعات أرقام موجبة',
        'الحالة يجب أن تكون: جاري التنفيذ، مكتمل، متأخر، أو متعثر',
        'الأولوية يجب أن تكون: عالية أو منخفضة'
      ]
    },
    departments: {
      name: 'الأقسام',
      fields: {
        name: 'اسم القسم (نص مطلوب)',
        description: 'وصف القسم (نص)',
        manager: 'رئيس القسم (نص)',
        email: 'البريد الإلكتروني (email)',
        phone: 'رقم الهاتف (نص)',
        employee_count: 'عدد الموظفين (رقم صحيح)',
        budget: 'الميزانية (رقم)',
        status: 'الحالة (نشط/غير نشط)'
      },
      constraints: [
        'name مطلوب',
        'البريد الإلكتروني يجب أن يكون صالح إذا تم توفيره',
        'عدد الموظفين رقم صحيح موجب',
        'الميزانية رقم موجب',
        'الحالة إما نشط أو غير نشط'
      ]
    },
    users: {
      name: 'المستخدمين',
      fields: {
        name: 'الاسم الكامل (نص)',
        email: 'البريد الإلكتروني (email)',
        phone: 'رقم الهاتف (نص)',
        role: 'الدور الوظيفي (نص)',
        department: 'القسم (نص)',
        position: 'المنصب (نص)',
        status: 'الحالة (نشط/غير نشط)',
        join_date: 'تاريخ الانضمام (YYYY-MM-DD)',
        experience: 'سنوات الخبرة (نص)',
        qualification: 'المؤهل العلمي (نص)'
      },
      constraints: [
        'البريد الإلكتروني يجب أن يكون صالح وفريد',
        'تاريخ الانضمام لا يمكن أن يكون في المستقبل',
        'الحالة إما نشط أو غير نشط'
      ]
    },
    indicators: {
      name: 'المؤشرات',
      fields: {
        name: 'اسم المؤشر (نص)',
        description: 'وصف المؤشر (نص)',
        unit: 'وحدة القياس (نص)',
        target_value: 'القيمة المستهدفة (رقم)',
        current_value: 'القيمة الحالية (رقم)',
        frequency: 'تكرار القياس (يومي/أسبوعي/شهري/ربع سنوي/سنوي)',
        responsible_department: 'القسم المسؤول (نص)',
        category: 'الفئة (أداء/جودة/كفاءة/رضا/مالي)',
        status: 'الحالة (نشط/غير نشط)'
      },
      constraints: [
        'القيمة المستهدفة والحالية أرقام موجبة',
        'التكرار من القيم المحددة فقط',
        'الفئة من القيم المحددة فقط'
      ]
    },
    projects: {
      name: 'المشاريع',
      fields: {
        name: 'اسم المشروع (نص)',
        description: 'وصف المشروع (نص)',
        manager: 'مدير المشروع (نص)',
        department: 'القسم المسؤول (نص)',
        start_date: 'تاريخ البداية (YYYY-MM-DD)',
        end_date: 'تاريخ النهاية (YYYY-MM-DD)',
        budget: 'الميزانية (رقم)',
        status: 'الحالة (مخطط/قيد التنفيذ/مكتمل/متوقف)',
        priority: 'الأولوية (عالية/متوسطة/منخفضة)',
        progress: 'نسبة الإنجاز (رقم من 0 إلى 100)',
        category: 'الفئة (تطوير/بنية تحتية/تدريب/أخرى)'
      },
      constraints: [
        'تاريخ النهاية بعد تاريخ البداية',
        'الميزانية رقم موجب',
        'نسبة الإنجاز بين 0 و 100',
        'الحالة من القيم المحددة فقط'
      ]
    }
  };

  const generatePrompt = () => {
    const template = dataTemplates[dataType];
    
    const prompt = `أنت مساعد ذكي متخصص في إنشاء بيانات منظمة. المطلوب منك إنشاء ${itemCount} عنصر من ${template.name} بصيغة JSON.

## المعطيات المطلوبة:
${Object.entries(template.fields).map(([key, desc]) => `- ${key}: ${desc}`).join('\n')}

## القيود والشروط:
${template.constraints.map(constraint => `- ${constraint}`).join('\n')}

## قالب JSON المطلوب:
\`\`\`json
[
  {
${Object.keys(template.fields).map(key => `    "${key}": "قيمة_${key}"`).join(',\n')}
  }
]
\`\`\`

## التعليمات:
1. أنشئ ${itemCount} عنصر بالضبط
2. استخدم بيانات واقعية ومنطقية باللغة العربية
3. التزم بالقيود المذكورة أعلاه
4. تأكد من صحة تنسيق JSON
5. استخدم التواريخ بصيغة YYYY-MM-DD
6. اجعل البيانات متنوعة ومفيدة

الآن، بناءً على المعلومات التالية، أنشئ البيانات المطلوبة:

[هنا سيضع المستخدم المعلومات التي يريد إنشاء البيانات بناءً عليها]`;

    setGeneratedPrompt(prompt);
    setStep('paste');
  };

  const copyPrompt = () => {
    navigator.clipboard.writeText(generatedPrompt);
    notifications.success('تم النسخ', 'تم نسخ المطالبة إلى الحافظة');
  };

  const parseAndValidateData = () => {
    try {
      // محاولة تحليل JSON
      const cleanInput = userInput.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const data = JSON.parse(cleanInput);

      if (!Array.isArray(data)) {
        throw new Error('البيانات يجب أن تكون في شكل مصفوفة');
      }

      // التحقق من صحة البيانات
      const template = dataTemplates[dataType];
      const allFields = Object.keys(template.fields);
      const requiredFields = Object.entries(template.fields)
        .filter(([_, desc]) => desc.includes('مطلوب'))
        .map(([field, _]) => field);

      const validatedData = data.map((item, index) => {
        // التحقق من الحقول المطلوبة
        const missingRequiredFields = requiredFields.filter(field =>
          !item.hasOwnProperty(field) || !item[field] || item[field].toString().trim() === ''
        );

        if (missingRequiredFields.length > 0) {
          throw new Error(`العنصر ${index + 1} يفتقد للحقول المطلوبة: ${missingRequiredFields.join(', ')}`);
        }

        // إضافة الحقول المفقودة بقيم افتراضية
        const completeItem = { ...item };
        allFields.forEach(field => {
          if (!completeItem.hasOwnProperty(field)) {
            completeItem[field] = '';
          }
        });

        return completeItem;
      });

      setParsedData(validatedData);
      setStep('preview');
      notifications.success('تم التحليل', `تم تحليل ${validatedData.length} عنصر بنجاح`);
    } catch (error) {
      notifications.error('خطأ في التحليل', error.message);
    }
  };

  const handleImport = async () => {
    try {
      setStep('importing');
      await onImport(parsedData);
      notifications.success('تم الاستيراد', `تم استيراد ${parsedData.length} عنصر بنجاح`);
      setStep('prompt');
      setUserInput('');
      setParsedData([]);
    } catch (error) {
      notifications.error('خطأ في الاستيراد', 'فشل في استيراد البيانات');
      setStep('preview');
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 'prompt':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Brain className="w-16 h-16 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                الاستيراد بالذكاء الصناعي
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                استخدم الذكاء الصناعي لإنشاء بيانات {dataTemplates[dataType].name} تلقائياً
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                عدد العناصر المطلوبة
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={itemCount}
                onChange={(e) => setItemCount(parseInt(e.target.value) || 1)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>

            <button
              onClick={generatePrompt}
              className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <Sparkles className="w-5 h-5" />
              إنشاء مطالبة الذكاء الصناعي
            </button>
          </div>
        );

      case 'paste':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                مطالبة الذكاء الصناعي
              </h3>
              <button
                onClick={copyPrompt}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
              >
                <Copy className="w-4 h-4" />
                نسخ
              </button>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
              <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                {generatedPrompt}
              </pre>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">خطوات الاستخدام:</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>انسخ المطالبة أعلاه</li>
                    <li>الصقها في أي نموذج ذكاء صناعي (ChatGPT, Claude, إلخ)</li>
                    <li>أضف المعلومات التي تريد إنشاء البيانات بناءً عليها</li>
                    <li>انسخ النتيجة والصقها في الحقل أدناه</li>
                  </ol>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الصق نتيجة الذكاء الصناعي هنا
              </label>
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                rows={10}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500"
                placeholder="الصق كود JSON هنا..."
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setStep('prompt')}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
              >
                رجوع
              </button>
              <button
                onClick={parseAndValidateData}
                disabled={!userInput.trim()}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Code className="w-4 h-4" />
                تحليل البيانات
              </button>
            </div>
          </div>
        );

      case 'preview':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                معاينة البيانات ({parsedData.length} عنصر)
              </h3>
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="text-sm font-medium">تم التحليل بنجاح</span>
              </div>
            </div>

            {/* معاينة البيانات في شكل جدول */}
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <div className="max-h-96 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                    <tr>
                      <th className="px-4 py-3 text-right text-gray-900 dark:text-white font-medium border-b border-gray-200 dark:border-gray-600">
                        #
                      </th>
                      {Object.entries(dataTemplates[dataType].fields).map(([field, desc]) => (
                        <th key={field} className="px-4 py-3 text-right text-gray-900 dark:text-white font-medium border-b border-gray-200 dark:border-gray-600 min-w-32">
                          <div className="flex items-center gap-1">
                            <span>{field}</span>
                            {desc.includes('مطلوب') && (
                              <span className="text-red-500 text-xs">*</span>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {parsedData.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-4 py-3 text-gray-900 dark:text-white font-medium">
                          {index + 1}
                        </td>
                        {Object.entries(dataTemplates[dataType].fields).map(([field, desc]) => {
                          const isEmpty = !item[field] || item[field].toString().trim() === '';
                          const isRequired = desc.includes('مطلوب');
                          const hasError = isEmpty && isRequired;

                          return (
                            <td key={field} className={`px-4 py-3 max-w-48 ${
                              hasError
                                ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                                : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              <div className="truncate" title={item[field]}>
                                {item[field] || (
                                  <span className={`italic ${
                                    hasError
                                      ? 'text-red-500 font-medium'
                                      : 'text-gray-400'
                                  }`}>
                                    {hasError ? 'مطلوب!' : 'فارغ'}
                                  </span>
                                )}
                              </div>
                            </td>
                          );
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* تحليل جودة البيانات */}
            {(() => {
              const template = dataTemplates[dataType];
              const requiredFields = Object.entries(template.fields)
                .filter(([_, desc]) => desc.includes('مطلوب'))
                .map(([field, _]) => field);

              const emptyFields = [];
              const incompleteItems = [];

              parsedData.forEach((item, index) => {
                requiredFields.forEach(field => {
                  if (!item[field] || item[field].toString().trim() === '') {
                    emptyFields.push(`العنصر ${index + 1}: ${field}`);
                  }
                });

                const emptyRequiredCount = requiredFields.filter(field =>
                  !item[field] || item[field].toString().trim() === ''
                ).length;

                if (emptyRequiredCount > 0) {
                  incompleteItems.push(index + 1);
                }
              });

              return (
                <>
                  {/* ملخص البيانات */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 className="text-blue-900 dark:text-blue-200 font-medium mb-2">ملخص البيانات:</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">إجمالي العناصر:</span>
                        <span className="font-bold text-blue-900 dark:text-blue-100 mr-2">{parsedData.length}</span>
                      </div>
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">العناصر المكتملة:</span>
                        <span className="font-bold text-green-600 mr-2">{parsedData.length - incompleteItems.length}</span>
                      </div>
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">العناصر الناقصة:</span>
                        <span className="font-bold text-orange-600 mr-2">{incompleteItems.length}</span>
                      </div>
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">نوع البيانات:</span>
                        <span className="font-bold text-blue-900 dark:text-blue-100 mr-2">{template.name}</span>
                      </div>
                    </div>
                  </div>

                  {/* تحذيرات البيانات الناقصة */}
                  {incompleteItems.length > 0 && (
                    <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-orange-600 mt-0.5" />
                        <div>
                          <h4 className="text-orange-900 dark:text-orange-200 font-medium mb-2">
                            تحذير: بيانات ناقصة ({incompleteItems.length} عنصر)
                          </h4>
                          <p className="text-orange-800 dark:text-orange-300 text-sm mb-2">
                            العناصر التالية تحتوي على حقول مطلوبة فارغة:
                          </p>
                          <div className="text-orange-700 dark:text-orange-400 text-sm">
                            <strong>العناصر:</strong> {incompleteItems.join(', ')}
                          </div>
                          {emptyFields.length > 0 && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-orange-800 dark:text-orange-300 text-sm font-medium">
                                عرض التفاصيل ({emptyFields.length} حقل فارغ)
                              </summary>
                              <div className="mt-2 text-xs text-orange-700 dark:text-orange-400 max-h-32 overflow-y-auto">
                                {emptyFields.map((field, index) => (
                                  <div key={index}>• {field}</div>
                                ))}
                              </div>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* رسالة نجاح */}
                  {incompleteItems.length === 0 && (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <div>
                          <h4 className="text-green-900 dark:text-green-200 font-medium">
                            ممتاز! جميع البيانات مكتملة
                          </h4>
                          <p className="text-green-800 dark:text-green-300 text-sm">
                            جميع الحقول المطلوبة مملوءة وجاهزة للاستيراد
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              );
            })()}

            {/* عرض JSON الخام (قابل للطي) */}
            <details className="bg-gray-50 dark:bg-gray-700 rounded-lg">
              <summary className="px-4 py-3 cursor-pointer text-gray-900 dark:text-white font-medium hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
                عرض البيانات الخام (JSON)
              </summary>
              <div className="px-4 pb-4 max-h-60 overflow-y-auto">
                <pre className="text-xs text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 p-3 rounded border">
                  {JSON.stringify(parsedData, null, 2)}
                </pre>
              </div>
            </details>

            <div className="flex gap-3">
              <button
                onClick={() => setStep('paste')}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
              >
                تعديل
              </button>
              <button
                onClick={handleImport}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Upload className="w-4 h-4" />
                استيراد {parsedData.length} عنصر
              </button>
            </div>
          </div>
        );

      case 'importing':
        return (
          <div className="text-center py-12">
            <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
              جاري الاستيراد...
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              يتم استيراد {parsedData.length} عنصر إلى قاعدة البيانات
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {renderStepContent()}
    </div>
  );
};

export default AIImport;
