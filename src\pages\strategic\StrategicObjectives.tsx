import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Plus, Calendar, Target, TrendingUp, Users, BarChart3, FileText, CheckCircle, Clock, AlertCircle, ChevronDown, MoreHorizontal, Edit, Trash2, Shield, UserCheck, Eye, Settings, Award, Sun, Moon, ArrowLeft, X, Lightbulb, Star, Zap, Loader2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { StrategicObjectiveService, type StrategicObjective } from '../../lib/strategicObjectiveService';
import { KPIService } from '../../lib/kpiService';
import { DepartmentService } from '../../lib/departmentService';
import { UserService } from '../../lib/userService';
import StrategicObjectiveForm from '../../components/forms/StrategicObjectiveForm';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../lib/database.types';

// استخدام النوع من الخدمة
type StrategicObjectiveInsert = Database['public']['Tables']['strategic_objectives']['Insert'];


const StrategicObjectives = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [activeView, setActiveView] = useState('الكل');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [detailTab, setDetailTab] = useState('معلومات');
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('strategicObjectivesListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { user } = useAuth();
  const notifications = useNotifications();
  
  // State for data management
  const [objectives, setObjectives] = useState<StrategicObjective[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableKPIs, setAvailableKPIs] = useState<string[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [availableUsers, setAvailableUsers] = useState<string[]>([]);

  // تحميل البيانات من قاعدة البيانات
  useEffect(() => {
    fetchObjectives();
    fetchKPIs();
    fetchDepartments();
    fetchUsers();
  }, []);

  // تطبيق الوضع الليلي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  const fetchObjectives = async () => {
    try {
      setLoading(true);
      const data = await StrategicObjectiveService.getAllObjectives();
      setObjectives(data || []);
    } catch (error) {
      console.error('Error fetching objectives:', error);
      notifications.error('خطأ في تحميل البيانات', 'فشل في تحميل الأهداف الاستراتيجية');
    } finally {
      setLoading(false);
    }
  };

  const fetchKPIs = async () => {
    try {
      const kpis = await KPIService.getAllKPIs();
      setAvailableKPIs(kpis.map(kpi => kpi.name) || []);
    } catch (error) {
      console.error('Error fetching KPIs:', error);
      setAvailableKPIs([]);
    }
  };

  const fetchDepartments = async () => {
    try {
      const departments = await DepartmentService.getDepartmentNames();
      setAvailableDepartments(departments);
    } catch (error) {
      console.error('Error fetching departments:', error);
      setAvailableDepartments([]);
    }
  };

  const fetchUsers = async () => {
    try {
      const users = await UserService.getUserNames();
      setAvailableUsers(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      setAvailableUsers([]);
    }
  };

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('strategicObjectivesListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل': return 'bg-green-500';
      case 'جاري': return 'bg-blue-500';
      case 'التخطيط': return 'bg-yellow-500';
      case 'مجدول': return 'bg-purple-500';
      case 'متأخر': return 'bg-red-500';
      case 'معلق': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'عالية': return 'text-red-400 bg-red-500/20';
      case 'متوسطة': return 'text-yellow-400 bg-yellow-500/20';
      case 'منخفضة': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  // وظائف إدارة الأهداف الاستراتيجية
  const handleSaveObjective = async (objectiveData: any) => {
    try {
      if (editingItem) {
        // تحديث هدف موجود
        const data = await StrategicObjectiveService.updateObjective(editingItem.id, objectiveData);

        // تحديث العلاقات مع مؤشرات الأداء
        if (objectiveData.relatedKPIs) {
          await updateObjectiveKPIs(editingItem.id, objectiveData.relatedKPIs);
        }

        setObjectives(prev =>
          prev.map(obj =>
            obj.id === editingItem.id ? data : obj
          )
        );
        setSelectedItem(data);
        notifications.success('تم التحديث', 'تم تحديث الهدف الاستراتيجي بنجاح');
      } else {
        // إنشاء هدف جديد
        const data = await StrategicObjectiveService.createObjective(objectiveData);

        // إنشاء العلاقات مع مؤشرات الأداء
        if (objectiveData.relatedKPIs) {
          await updateObjectiveKPIs(data.id, objectiveData.relatedKPIs);
        }

        setObjectives(prev => [data, ...prev]);
        setSelectedItem(data);
        notifications.success('تم الإضافة', 'تم إضافة الهدف الاستراتيجي بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving objective:', error);
      notifications.error('خطأ', 'فشل في حفظ الهدف الاستراتيجي');
    }
  };

  const updateObjectiveKPIs = async (objectiveId: string, kpiNames: string[]) => {
    try {
      // حذف العلاقات الموجودة
      await supabase
        .from('objective_kpis')
        .delete()
        .eq('objective_id', objectiveId);

      // إنشاء علاقات جديدة
      if (kpiNames.length > 0) {
        const relationships = kpiNames.map(kpiName => ({
          objective_id: objectiveId,
          kpi_name: kpiName
        }));

        const { error } = await supabase
          .from('objective_kpis')
          .insert(relationships);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error updating objective KPIs:', error);
    }
  };
  
  const handleDeleteObjective = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الهدف؟')) {
      try {
        await StrategicObjectiveService.deleteObjective(id);
        setObjectives(prev => prev.filter(obj => obj.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف الهدف الاستراتيجي بنجاح');
      } catch (error) {
        console.error('Error deleting objective:', error);
        notifications.error('خطأ في الحذف', 'فشل في حذف الهدف الاستراتيجي');
      }
    }
  };
  
  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item: any) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item: any) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    // إذا كنا في وضع التعديل، حدث العنصر المحرر إلى العنصر الجديد
    if (isEditMode) {
      setEditingItem(item);
    }
    // لا نغلق وضع التعديل - نبقيه مفتوحاً
  };


  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.title}</h3>
          {isBeingEdited && (
            <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
              قيد التحرير
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(item.priority)}`}>
            {item.priority}
          </span>
          <span className={`text-xs px-2 py-1 rounded ${getStatusColor(item.status)} text-white`}>
            {item.status}
          </span>
        </div>
      </div>
      
      <p className="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">
        {item.description}
      </p>
      
      <div className="mb-2">
        <div className="flex justify-between text-xs mb-1">
          <span className="text-gray-600 dark:text-gray-400">التقدم</span>
          <span className="text-gray-900 dark:text-white">{item.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
          <div 
            className="bg-blue-500 h-1 rounded-full transition-all"
            style={{ width: `${item.progress}%` }}
          ></div>
        </div>
      </div>
      
      <div className="text-gray-600 dark:text-gray-400 text-xs">
        الإدارة: {item.department}
      </div>
    </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    // إذا كنا في وضع الإضافة أو التعديل، اعرض النموذج مباشرة
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <StrategicObjectiveForm
            editData={editingItem}
            onSave={handleSaveObjective}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
            availableKPIs={availableKPIs}
            availableDepartments={availableDepartments}
            availableUsers={availableUsers}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Target className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر هدف لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر هدف استراتيجي من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-blue-500">
                <Target size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.title}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedItem.status)} text-white`}>
                    {selectedItem.status}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">الأولوية:</span>
                    <span className={`px-2 py-0.5 rounded text-xs ${getPriorityColor(selectedItem.priority)}`}>
                      {selectedItem.priority}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {selectedItem.end_date ? new Date(selectedItem.end_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">{selectedItem.department}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasPermission('تعديل') && (
                <>
                  <button 
                    onClick={() => handleEdit(selectedItem)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                    title="تعديل"
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    onClick={() => handleDeleteObjective(selectedItem.id)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                    title="حذف"
                  >
                    <Trash2 size={18} />
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* التقدم العام */}
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-gray-600 dark:text-gray-400 text-sm">التقدم العام</span>
                <span className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.progress}%</span>
              </div>
              <div className="flex items-center gap-1">
                {selectedItem.progress < 30 && <AlertCircle className="w-4 h-4 text-red-500" />}
                {selectedItem.progress >= 30 && selectedItem.progress < 70 && <Clock className="w-4 h-4 text-yellow-500" />}
                {selectedItem.progress >= 70 && <CheckCircle className="w-4 h-4 text-green-500" />}
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all ${
                  selectedItem.progress < 30 ? 'bg-red-500' :
                  selectedItem.progress < 70 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${selectedItem.progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Content - عرض المعلومات مباشرة */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">الوصف</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* الإحصائيات الرئيسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">الإحصائيات الرئيسية</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {selectedItem.progress}%
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">نسبة الإنجاز</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">من الهدف الكلي</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {selectedItem.milestones?.filter(m => m.completed).length || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">مراحل مكتملة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">من {selectedItem.milestones?.length || 0}</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {selectedItem.relatedKPIs?.length || selectedItem.kpis?.length || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">مؤشرات مرتبطة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">KPIs</div>
                  </div>
                </div>
              </div>

              {/* المعلومات الأساسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">المعلومات الأساسية</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الإدارة المسؤولة</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.department}</p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">المدير المسؤول</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.manager}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الميزانية المخصصة</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">
                        {selectedItem.budget ? `${selectedItem.budget.toLocaleString('ar-SA')} ريال` : 'غير محدد'}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">المدة الزمنية</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">
                        {selectedItem.start_date && selectedItem.end_date 
                          ? `${Math.ceil((new Date(selectedItem.end_date).getTime() - new Date(selectedItem.start_date).getTime()) / (1000 * 60 * 60 * 24))} يوم`
                          : 'غير محدد'
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الجدول الزمني</span>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-gray-900 dark:text-white text-sm">
                        {selectedItem.start_date ? new Date(selectedItem.start_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-400 rotate-90" />
                      <span className="text-gray-900 dark:text-white text-sm">
                        {selectedItem.end_date ? new Date(selectedItem.end_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* مؤشرات الأداء المرتبطة */}
              {(selectedItem.relatedKPIs?.length > 0 || selectedItem.kpis?.length > 0) && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-3">مؤشرات الأداء المرتبطة</h4>
                  <div className="flex flex-wrap gap-2">
                    {(selectedItem.relatedKPIs || selectedItem.kpis || []).map((kpi: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-2 px-3 py-1.5 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 rounded-full text-sm"
                      >
                        <TrendingUp className="w-3 h-3" />
                        {kpi}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* المراحل الرئيسية */}
              {selectedItem.milestones && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-3">المراحل الرئيسية</h4>
                  <div className="space-y-2">
                    {selectedItem.milestones.map((milestone: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            milestone.completed ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                          }`}>
                            {milestone.completed ? <CheckCircle className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                          </div>
                          <div>
                            <p className="text-gray-900 dark:text-white text-sm font-medium">{milestone.name}</p>
                            <p className="text-gray-600 dark:text-gray-400 text-xs">
                              {new Date(milestone.date).toLocaleDateString('ar-SA')}
                            </p>
                          </div>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded ${
                          milestone.completed 
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' 
                            : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-400'
                        }`}>
                          {milestone.completed ? 'مكتمل' : 'قيد الانتظار'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

          </div>
        </div>
      </div>
    );
  };

  // فلترة الأهداف
  const filteredObjectives = objectives.filter(objective => {
    // فلترة حسب العرض
    let viewFilter = false;
    switch(activeView) {
      case 'الكل': viewFilter = true; break;
      case 'جاري': viewFilter = objective.status === 'جاري'; break;
      case 'مكتمل': viewFilter = objective.status === 'مكتمل'; break;
      case 'متأخر': viewFilter = objective.status === 'متأخر' || (objective.status === 'جاري' && new Date(objective.endDate) < new Date()); break;
      default: viewFilter = true;
    }
    
    // فلترة حسب البحث
    const searchFilter = !searchTerm || 
      objective.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      objective.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      objective.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
      objective.manager.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلترة حسب الحالة
    const statusMatch = !statusFilter || objective.status === statusFilter;
    
    // فلترة حسب الأولوية  
    const priorityMatch = !priorityFilter || objective.priority === priorityFilter;
    
    // فلترة حسب الإدارة
    const departmentMatch = !departmentFilter || objective.department === departmentFilter;
    
    return viewFilter && searchFilter && statusMatch && priorityMatch && departmentMatch;
  });

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">الأهداف الاستراتيجية</h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">إدارة ومتابعة الأهداف الاستراتيجية للمؤسسة</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-x-auto">
        <div className="flex flex-1">
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'الكل' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('الكل')}
          >
            جميع الأهداف
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'جاري' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('جاري')}
          >
            قيد التنفيذ
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'مكتمل' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('مكتمل')}
          >
            مكتملة
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'متأخر' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('متأخر')}
          >
            متأخرة
          </button>
        </div>
        
        {/* New Button in Navigation Tabs */}
        <div className="flex items-center px-4">
          {hasPermission('إضافة') && (
            <button 
              onClick={handleAddNew}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
            >
              <Plus size={16} />
              جديد
            </button>
          )}
        </div>
      </div>

      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
          {/* List Panel */}
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
            style={{ width: `${listWidth}%` }}
          >
            {/* Search Header */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              {/* البحث والفلاتر في صف واحد */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400" size={16} />
                  <input 
                    type="text" 
                    placeholder="البحث..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm"
                  />
                </div>
                
                {/* الفلاتر */}
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[100px]"
                >
                  <option value="">الحالة</option>
                  <option value="جاري">جاري</option>
                  <option value="مكتمل">مكتمل</option>
                  <option value="متأخر">متأخر</option>
                  <option value="مجدول">مجدول</option>
                  <option value="معلق">معلق</option>
                </select>
                
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[100px]"
                >
                  <option value="">الأولوية</option>
                  <option value="عالية">عالية</option>
                  <option value="متوسطة">متوسطة</option>
                  <option value="منخفضة">منخفضة</option>
                </select>
                
                {listWidth > 35 && (
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[120px]"
                  >
                    <option value="">الإدارة</option>
                    {availableDepartments.map((dept, index) => (
                      <option key={index} value={dept}>{dept}</option>
                    ))}
                  </select>
                )}
                

              </div>
            </div>
            
            {/* List Items */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="mr-2 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {filteredObjectives.map(objective => (
                    <ListItem 
                      key={objective.id} 
                      item={objective} 
                      onClick={handleItemClick}
                      isSelected={selectedItem?.id === objective.id}
                    />
                  ))}
                  {filteredObjectives.length === 0 && !loading && (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">لا توجد أهداف مطابقة للبحث</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Resize Handle */}
          <div 
            className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
              isResizing 
                ? 'bg-blue-500' 
                : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
            }`}
            onMouseDown={() => setIsResizing(true)}
          >
            <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
          </div>

          {/* Detail Panel */}
          <div className="flex-1 overflow-hidden">
            <DetailPanel />
          </div>
        </div>

      {/* Role Switcher */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default StrategicObjectives;