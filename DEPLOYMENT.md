# دليل النشر على Netlify

## الخطوات المطلوبة

### 1. إعد<PERSON> GitHub Repository

```bash
# إنشاء مستودع جديد على GitHub
git init
git add .
git commit -m "Initial commit: Strategic Planning System"
git branch -M main
git remote add origin https://github.com/YOUR_USERNAME/strategic-planning-system.git
git push -u origin main
```

### 2. ربط المشروع بـ Netlify

1. اذهب إلى [netlify.com](https://netlify.com)
2. سجل دخول أو أنشئ حساب جديد
3. اضغط على "New site from Git"
4. اختر GitHub واربط حسابك
5. اختر المستودع `strategic-planning-system`
6. اضبط إعدادات البناء:
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`
   - **Node version**: `18`

### 3. متغيرات البيئة (اختياري)

إذا كنت تريد إعداد متغيرات البيئة في Netlify بدلاً من الملف:

1. اذهب إلى Site settings > Environment variables
2. أضف المتغيرات التالية:
   ```
   VITE_SUPABASE_URL=https://jqtguuuzezhorpyydhwz.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxdGd1dXV6ZXpob3JweXlkaHd6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzU5NjUsImV4cCI6MjA2NDYxMTk2NX0.x-o758cf0D0bH4NUXuNdMvIHmt1NTOI0-X5nrO-s4F0
   ```

### 4. النشر

بعد ربط المستودع، سيتم النشر تلقائياً. يمكنك متابعة حالة النشر من لوحة تحكم Netlify.

## معلومات المشروع

### قاعدة البيانات
- **Supabase Project**: strategic-planning-system
- **URL**: https://jqtguuuzezhorpyydhwz.supabase.co
- **Region**: ap-southeast-2

### بيانات الدخول
- **Email**: <EMAIL>
- **Password**: Strategic@123

### الملفات المهمة للنشر
- `netlify.toml` - إعدادات Netlify
- `public/_redirects` - توجيه الصفحات
- `.env` - متغيرات البيئة

## استكشاف الأخطاء

### مشكلة في البناء
إذا فشل البناء، تأكد من:
1. تثبيت جميع المكتبات: `npm install`
2. تشغيل البناء محلياً: `npm run build`
3. التأكد من صحة متغيرات البيئة

### مشكلة في التوجيه
إذا لم تعمل الصفحات بشكل صحيح:
1. تأكد من وجود ملف `public/_redirects`
2. تأكد من إعدادات `netlify.toml`

### مشكلة في قاعدة البيانات
إذا لم تعمل المصادقة:
1. تأكد من صحة متغيرات البيئة
2. تأكد من إنشاء المستخدم في Supabase
3. تحقق من إعدادات Auth في Supabase

## الدعم

للحصول على المساعدة:
1. راجع وثائق [Netlify](https://docs.netlify.com)
2. راجع وثائق [Supabase](https://supabase.com/docs)
3. تحقق من سجلات البناء في Netlify
