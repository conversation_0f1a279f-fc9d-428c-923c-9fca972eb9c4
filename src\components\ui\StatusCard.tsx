import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface StatusCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  color: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error';
}

const colorClasses = {
  primary: 'bg-primary-50 text-primary-700',
  secondary: 'bg-secondary-50 text-secondary-700',
  accent: 'bg-accent-50 text-accent-800',
  success: 'bg-success-50 text-success-700',
  warning: 'bg-warning-50 text-warning-700',
  error: 'bg-error-50 text-error-700',
};

const cardColors = {
  primary: 'from-primary-500 to-primary-700',
  secondary: 'from-secondary-500 to-secondary-700',
  accent: 'from-accent-500 to-accent-700',
  success: 'from-success-500 to-success-700',
  warning: 'from-warning-500 to-warning-700',
  error: 'from-error-500 to-error-700',
};

const StatusCard = ({ title, value, icon, color }: StatusCardProps) => {
  return (
    <div className="card overflow-hidden relative">
      <div className={`absolute top-0 left-0 w-1 h-full bg-gradient-to-b ${cardColors[color]}`}></div>
      <div className="flex items-center gap-3 pl-3">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-xs font-medium text-neutral-500 truncate">{title}</h3>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-xl font-bold text-neutral-800"
          >
            {value}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default StatusCard;