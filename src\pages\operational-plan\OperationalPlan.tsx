import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Plus, Calendar, Target, TrendingUp, Users, BarChart3, FileText, CheckCircle, Clock, AlertCircle, ChevronDown, MoreHorizontal, Edit, Trash2, Shield, User<PERSON>heck, Eye, Settings, Award, Table, Sun, Moon, ArrowLeft, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useQuickNotifications } from '../../components/ui/NotificationSystem';
import DetailForm from './components/DetailForm';

// بيانات الأهداف الاستراتيجية
const initialStrategicGoals = [
  {
    id: 1,
    title: 'تحسين الأداء المالي',
    description: 'زيادة الإيرادات وتحسين الربحية بنسبة 25% خلال العام من خلال تطوير استراتيجيات جديدة وتحسين العمليات الحالية',
    owner: 'الإدارة المالية',
    manager: 'أحمد الحمدان',
    status: 'جاري',
    priority: 'عالية',
    progress: 65,
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    budget: '500,000 ريال',
    department: 'الإدارة المالية',
    kpis: ['معدل النمو السنوي', 'هامش الربح', 'العائد على الاستثمار'],
    relatedProjects: ['مشروع تحسين العمليات المالية'],
    team: ['أحمد الحمدان', 'نورا القحطاني', 'خالد الغامدي'],
    milestones: [
      { name: 'تحليل الوضع الحالي', date: '2025-02-28', completed: true },
      { name: 'وضع الاستراتيجية', date: '2025-04-30', completed: true },
      { name: 'بدء التنفيذ', date: '2025-06-30', completed: false },
      { name: 'المراجعة النهائية', date: '2025-11-30', completed: false }
    ]
  },
  {
    id: 2,
    title: 'الرقمنة والتطوير التقني',
    description: 'تحويل جميع العمليات الأساسية إلى نظم رقمية متطورة لتحسين الكفاءة وتقليل الأخطاء',
    owner: 'إدارة تقنية المعلومات',
    manager: 'سارة العتيبي',
    status: 'جاري',
    priority: 'عالية',
    progress: 45,
    startDate: '2025-02-01',
    endDate: '2025-11-30',
    budget: '800,000 ريال',
    department: 'تقنية المعلومات',
    kpis: ['معدل الرقمنة', 'رضا المستخدمين', 'كفاءة العمليات'],
    relatedProjects: ['برنامج التحول الرقمي'],
    team: ['سارة العتيبي', 'محمد السالم', 'سارة أحمد'],
    milestones: [
      { name: 'دراسة النظم الحالية', date: '2025-03-15', completed: true },
      { name: 'تصميم الحلول', date: '2025-05-30', completed: false },
      { name: 'التطوير والاختبار', date: '2025-09-30', completed: false },
      { name: 'التشغيل الكامل', date: '2025-11-30', completed: false }
    ]
  }
];

// مؤشرات الأداء الرئيسية
const initialKpis = [
  {
    id: 1,
    name: 'معدل النمو السنوي',
    goal: 'تحسين الأداء المالي',
    target: '25%',
    current: '18%',
    unit: '%',
    frequency: 'شهري',
    responsible: 'فريق المالية',
    status: 'جاري',
    trend: 'متزايد',
    lastUpdate: '2025-06-01',
    description: 'مؤشر يقيس نمو الإيرادات السنوية مقارنة بالعام السابق',
    dataSource: 'النظام المالي',
    formula: '((الإيرادات الحالية - إيرادات العام السابق) / إيرادات العام السابق) × 100',
    history: [
      { month: 'يناير', value: 15 },
      { month: 'فبراير', value: 16 },
      { month: 'مارس', value: 17 },
      { month: 'أبريل', value: 17.5 },
      { month: 'مايو', value: 18 }
    ]
  },
  {
    id: 2,
    name: 'رضا العملاء',
    goal: 'تحسين الأداء المالي',
    target: '95%',
    current: '87%',
    unit: '%',
    frequency: 'ربع سنوي',
    responsible: 'قسم خدمة العملاء',
    status: 'جاري',
    trend: 'متزايد',
    lastUpdate: '2025-05-15',
    description: 'مؤشر يقيس مستوى رضا العملاء من خلال الاستبيانات والمراجعات',
    dataSource: 'نظام إدارة العلاقات',
    formula: '(عدد العملاء الراضين / إجمالي العملاء المستطلعين) × 100',
    history: [
      { quarter: 'Q1 2024', value: 82 },
      { quarter: 'Q2 2024', value: 84 },
      { quarter: 'Q3 2024', value: 85 },
      { quarter: 'Q4 2024', value: 86 },
      { quarter: 'Q1 2025', value: 87 }
    ]
  }
];

// المشاريع والبرامج
const initialProjects = [
  {
    id: 1,
    title: 'برنامج التحول الرقمي',
    description: 'تطوير منصة رقمية شاملة لجميع العمليات مع تطبيقات متقدمة وواجهات سهلة الاستخدام',
    goal: 'الرقمنة والتطوير التقني',
    owner: 'إدارة تقنية المعلومات',
    manager: 'محمد السالم',
    status: 'جاري',
    priority: 'عالية',
    progress: 70,
    startDate: '2025-03-01',
    endDate: '2025-09-30',
    budget: '600,000 ريال',
    spent: '420,000 ريال',
    team: ['سارة أحمد', 'علي محمد', 'فاطمة الزهراني'],
    activities: ['تطوير التطبيق', 'تدريب المستخدمين', 'الاختبار والتشغيل'],
    risks: [
      { risk: 'تأخير في التطوير', probability: 'متوسطة', impact: 'عالي' },
      { risk: 'مقاومة التغيير', probability: 'عالية', impact: 'متوسط' }
    ],
    deliverables: [
      { name: 'تطبيق الموبايل', status: 'مكتمل', date: '2025-05-15' },
      { name: 'لوحة التحكم', status: 'جاري', date: '2025-07-01' },
      { name: 'نظام التقارير', status: 'مجدول', date: '2025-08-15' }
    ]
  },
  {
    id: 2,
    title: 'مشروع تحسين العمليات المالية',
    description: 'أتمتة العمليات المالية وتحسين الكفاءة من خلال نظم محاسبية متطورة',
    goal: 'تحسين الأداء المالي',
    owner: 'الإدارة المالية',
    manager: 'نورا القحطاني',
    status: 'التخطيط',
    priority: 'متوسطة',
    progress: 25,
    startDate: '2025-04-01',
    endDate: '2025-08-31',
    budget: '300,000 ريال',
    spent: '75,000 ريال',
    team: ['خالد الغامدي', 'ريم الشهري'],
    activities: ['دراسة العمليات', 'تصميم النظام', 'التنفيذ'],
    risks: [
      { risk: 'تعقيد العمليات الحالية', probability: 'عالية', impact: 'متوسط' }
    ],
    deliverables: [
      { name: 'دراسة العمليات', status: 'مكتمل', date: '2025-04-30' },
      { name: 'تصميم النظام', status: 'جاري', date: '2025-06-15' },
      { name: 'التطبيق والاختبار', status: 'مجدول', date: '2025-08-31' }
    ]
  }
];

// الأنشطة
const initialActivities = [
  {
    id: 1,
    title: 'تطوير واجهة المستخدم',
    project: 'برنامج التحول الرقمي',
    assignee: 'سارة أحمد',
    status: 'جاري',
    priority: 'عالية',
    progress: 80,
    startDate: '2025-03-15',
    endDate: '2025-06-09',
    department: 'تقنية المعلومات',
    dependencies: [],
    description: 'تطوير واجهة مستخدم حديثة وسهلة الاستخدام للتطبيق الجديد',
    estimatedHours: 200,
    actualHours: 160,
    comments: [
      { date: '2025-06-01', user: 'سارة أحمد', comment: 'تم الانتهاء من 80% من التصميم' },
      { date: '2025-05-15', user: 'محمد السالم', comment: 'مراجعة التصميم وتم الموافقة' }
    ],
    attachments: ['wireframes.pdf', 'design_mockups.png']
  },
  {
    id: 2,
    title: 'مراجعة وثائق المشروع',
    project: 'برنامج التحول الرقمي',
    assignee: 'سارة أحمد',
    status: 'مكتمل',
    priority: 'متوسطة',
    progress: 100,
    startDate: '2025-06-05',
    endDate: '2025-06-10',
    department: 'تقنية المعلومات',
    dependencies: [],
    description: 'مراجعة جميع وثائق المشروع والتأكد من اكتمالها',
    estimatedHours: 16,
    actualHours: 14,
    comments: [],
    attachments: []
  },
  {
    id: 3,
    title: 'تحديث قاعدة البيانات',
    project: 'برنامج التحول الرقمي',
    assignee: 'سارة أحمد',
    status: 'جاري',
    priority: 'عالية',
    progress: 60,
    startDate: '2025-06-08',
    endDate: '2025-06-12',
    department: 'تقنية المعلومات',
    dependencies: [],
    description: 'تحديث هيكل قاعدة البيانات لتتماشى مع التطبيق الجديد',
    estimatedHours: 24,
    actualHours: 18,
    comments: [],
    attachments: []
  },
  {
    id: 4,
    title: 'تدريب فريق المبيعات',
    project: 'برنامج التحول الرقمي',
    assignee: 'علي محمد',
    status: 'مجدول',
    priority: 'متوسطة',
    progress: 0,
    startDate: '2025-07-01',
    endDate: '2025-07-31',
    department: 'المبيعات',
    dependencies: ['تطوير واجهة المستخدم'],
    description: 'تدريب فريق المبيعات على استخدام النظام الجديد',
    estimatedHours: 40,
    actualHours: 0,
    comments: [],
    attachments: ['training_plan.pdf']
  }
];

const OperationalPlan = () => {
  const [activeTab, setActiveTab] = useState('مهامّي');
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [activitiesViewMode, setActivitiesViewMode] = useState('البطاقات');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // أولاً جرب قراءة الإعداد المحفوظ
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      // إذا لم يوجد إعداد محفوظ، احترم إعدادات النظام
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [myTasksView, setMyTasksView] = useState('اليوم');
  const [objectivesView, setObjectivesView] = useState('الكل');
  const [kpisView, setKpisView] = useState('الكل');
  const [projectsView, setProjectsView] = useState('الكل');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [listWidth, setListWidth] = useState(() => {
    // قراءة العرض المحفوظ أو استخدام 50% كافتراضي
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('operationalPlanListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { user } = useAuth();
  const notifications = useQuickNotifications();
  
  // State for data management
  const [objectives, setObjectives] = useState(initialStrategicGoals);
  const [kpiList, setKpiList] = useState(initialKpis);
  const [projectList, setProjectList] = useState(initialProjects);
  const [activityList, setActivityList] = useState(initialActivities);
  
  // تطبيق الوضع الليلي عند تحميل الصفحة
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);
  
  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      // حساب للواجهة العربية RTL - عكس الحركة
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      // Limit between 20% and 80%
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      // حفظ العرض الجديد
      if (typeof window !== 'undefined') {
        localStorage.setItem('operationalPlanListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      // Prevent text selection while resizing
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // دوال مساعدة للتواريخ
  const isToday = (date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isThisWeek = (date) => {
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return date >= startOfWeek && date <= endOfWeek;
  };


  // مهامي - الأنشطة الخاصة بالمستخدم الحالي
  const myTasks = activityList
    .filter(activity => activity.assignee === user?.user_metadata?.full_name || activity.assignee === 'سارة أحمد')
    .map(task => ({
      ...task,
      isCompleted: task.status === 'مكتمل',
      dueDate: new Date(task.endDate),
      isToday: isToday(new Date(task.endDate)),
      isThisWeek: isThisWeek(new Date(task.endDate))
    }));

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل': return 'bg-green-500';
      case 'جاري': return 'bg-blue-500';
      case 'التخطيط': return 'bg-yellow-500';
      case 'مجدول': return 'bg-purple-500';
      case 'متأخر': return 'bg-red-500';
      case 'معلق': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'عالية': return 'text-red-400 bg-red-500/20';
      case 'متوسطة': return 'text-yellow-400 bg-yellow-500/20';
      case 'منخفضة': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    // حفظ الإعداد في localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      // تطبيق الكلاس على body
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  // دالة تبديل حالة المهمة
  const toggleTaskCompletion = (taskId) => {
    setActivityList(prev => 
      prev.map(activity => 
        activity.id === taskId 
          ? { ...activity, status: activity.status === 'مكتمل' ? 'جاري' : 'مكتمل' }
          : activity
      )
    );
  };
  
  // وظائف إدارة الأهداف الاستراتيجية
  const handleSaveObjective = async (objectiveData: any) => {
    try {
      if (editingItem && activeTab === 'الأهداف') {
        setObjectives(prev =>
          prev.map(obj =>
            obj.id === editingItem.id
              ? { ...objectiveData, id: editingItem.id }
              : obj
          )
        );
      } else {
        setObjectives(prev => [...prev, objectiveData]);
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
    } catch (error) {
      throw error;
    }
  };
  
  const handleDeleteObjective = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الهدف؟')) {
      try {
        setObjectives(prev => prev.filter(obj => obj.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف الهدف الاستراتيجي بنجاح');
      } catch (error) {
        notifications.error('خطأ في الحذف', 'فشل في حذف الهدف الاستراتيجي');
      }
    }
  };
  
  // وظائف إدارة مؤشرات الأداء
  const handleSaveKPI = async (kpiData: any) => {
    try {
      if (editingItem && activeTab === 'مؤشرات الأداء') {
        setKpiList(prev =>
          prev.map(kpi =>
            kpi.id === editingItem.id
              ? { ...kpiData, id: editingItem.id }
              : kpi
          )
        );
      } else {
        setKpiList(prev => [...prev, kpiData]);
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
    } catch (error) {
      throw error;
    }
  };
  
  const handleDeleteKPI = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المؤشر؟')) {
      try {
        setKpiList(prev => prev.filter(kpi => kpi.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف مؤشر الأداء بنجاح');
      } catch (error) {
        notifications.error('خطأ في الحذف', 'فشل في حذف مؤشر الأداء');
      }
    }
  };
  
  // وظائف فتح النماذج
  const handleAddNew = () => {
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item: any) => {
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };
  
  const handleDelete = (item: any) => {
    switch (activeTab) {
      case 'الأهداف':
        handleDeleteObjective(item.id);
        break;
      case 'مؤشرات الأداء':
        handleDeleteKPI(item.id);
        break;
      case 'المشاريع والبرامج':
        handleDeleteProject(item.id);
        break;
      case 'الأنشطة':
        handleDeleteActivity(item.id);
        break;
      default:
        console.log('حذف غير مدعوم لهذا النوع');
    }
  };

  // تحديد ألوان السمة حسب النمط
  const themeClasses = {
    mainBg: isDarkMode 
      ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white'
      : 'bg-gradient-to-br from-blue-50 via-white to-purple-50 text-gray-900',
    
    sidebar: isDarkMode
      ? 'bg-gray-900 border-gray-700'
      : 'bg-white border-gray-200 shadow-lg',
    
    card: isDarkMode
      ? 'bg-gray-800/50 border-gray-700 hover:border-gray-600'
      : 'bg-white/80 border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md',
    
    listPanel: isDarkMode
      ? 'bg-gray-800/70 border-gray-700'
      : 'bg-white/90 border-gray-200 shadow-lg',
    
    detailPanel: isDarkMode
      ? 'bg-gray-800/50 border-gray-700'
      : 'bg-white/80 border-gray-200 shadow-sm',
    
    input: isDarkMode
      ? 'bg-gray-800 border-gray-700 focus:border-gray-600 text-white placeholder-gray-400'
      : 'bg-white border-gray-300 focus:border-blue-500 text-gray-900 placeholder-gray-500',
    
    text: isDarkMode ? 'text-white' : 'text-gray-900',
    textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
    textTertiary: isDarkMode ? 'text-gray-300' : 'text-gray-700',
    
    button: isDarkMode ? 'bg-gray-800' : 'bg-gray-100',
    buttonHover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-200',
    
    progressBg: isDarkMode ? 'bg-gray-700' : 'bg-gray-200',
    divider: isDarkMode ? 'border-gray-700' : 'border-gray-200'
  };

  // دالة للحصول على العنصر المحدد
  const getSelectedItemData = () => {
    if (!selectedItem) return null;
    
    switch (activeTab) {
      case 'مهامّي':
        return myTasks.find(task => task.id === selectedItem.id);
      case 'الأهداف':
        return objectives.find(goal => goal.id === selectedItem.id);
      case 'مؤشرات الأداء':
        return kpiList.find(kpi => kpi.id === selectedItem.id);
      case 'المشاريع والبرامج':
        return projectList.find(project => project.id === selectedItem.id);
      case 'الأنشطة':
        return activityList.find(activity => activity.id === selectedItem.id);
      default:
        return null;
    }
  };

  // دالة لعرض محتوى التفاصيل حسب التبويب
  const renderDetailContent = (item: any, tab: string) => {
    if (!item) return null;

    switch (tab) {
      case 'عام':
        return (
          <div className="space-y-6">
            {/* الوصف */}
            <div className={`p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-lg`}>
              <h4 className={`${themeClasses.text} font-medium mb-2`}>الوصف</h4>
              <p className={`${themeClasses.textSecondary} text-sm leading-relaxed`}>
                {item.description || 'لا يوجد وصف'}
              </p>
            </div>

            {/* المعلومات الأساسية */}
            <div className={`p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-lg`}>
              <h4 className={`${themeClasses.text} font-medium mb-3`}>المعلومات الأساسية</h4>
              <div className="grid grid-cols-2 gap-4">
                {activeTab === 'الأهداف' && (
                  <>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>الإدارة المسؤولة</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.owner}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>المدير المسؤول</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.manager}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>الميزانية المخصصة</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.budget}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>القسم</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.department}</p>
                    </div>
                  </>
                )}
                {activeTab === 'مؤشرات الأداء' && (
                  <>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>القيمة المستهدفة</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.target}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>القيمة الحالية</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.current}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>وحدة القياس</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.unit}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>التكرار</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.frequency}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>المسؤول</span>
                      <p className={`${themeClasses.text} text-sm font-medium`}>{item.responsible}</p>
                    </div>
                    <div>
                      <span className={`${themeClasses.textSecondary} text-xs`}>الاتجاه</span>
                      <p className={`${themeClasses.text} text-sm font-medium flex items-center gap-1`}>
                        {item.trend === 'متزايد' ? <TrendingUp className="w-4 h-4 text-green-500" /> : <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />}
                        {item.trend}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* الجدول الزمني */}
            {(item.startDate || item.endDate) && (
              <div className={`p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-lg`}>
                <h4 className={`${themeClasses.text} font-medium mb-3`}>الجدول الزمني</h4>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <span className={`${themeClasses.textSecondary} text-xs`}>تاريخ البداية</span>
                    <p className={`${themeClasses.text} text-sm font-medium`}>
                      {item.startDate ? new Date(item.startDate).toLocaleDateString('ar-SA') : '-'}
                    </p>
                  </div>
                  <ChevronDown className={`w-4 h-4 ${themeClasses.textSecondary} rotate-90`} />
                  <div className="flex-1">
                    <span className={`${themeClasses.textSecondary} text-xs`}>تاريخ النهاية</span>
                    <p className={`${themeClasses.text} text-sm font-medium`}>
                      {item.endDate ? new Date(item.endDate).toLocaleDateString('ar-SA') : '-'}
                    </p>
                  </div>
                </div>
                {item.endDate && (
                  <div className="mt-3">
                    <span className={`${themeClasses.textSecondary} text-xs`}>المدة المتبقية</span>
                    <p className={`${themeClasses.text} text-sm font-medium`}>
                      {Math.ceil((new Date(item.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      
      case 'مؤشرات الأداء':
        return activeTab === 'الأهداف' && (
          <div className="space-y-4">
            {item.kpis?.map((kpiName: string, index: number) => {
              const kpi = kpiList.find(k => k.name === kpiName);
              if (!kpi) return null;
              
              return (
                <div key={index} className={`p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-lg`}>
                  <div className="flex items-start justify-between mb-2">
                    <h5 className={`${themeClasses.text} font-medium`}>{kpi.name}</h5>
                    <span className={`text-xs px-2 py-1 rounded ${getStatusColor(kpi.status)} text-white`}>
                      {kpi.status}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    <div>
                      <span className={themeClasses.textSecondary}>المستهدف: </span>
                      <span className={themeClasses.text}>{kpi.target}</span>
                    </div>
                    <div>
                      <span className={themeClasses.textSecondary}>الحالي: </span>
                      <span className={themeClasses.text}>{kpi.current}</span>
                    </div>
                  </div>
                  <div className="mt-2">
                    <div className={`w-full ${themeClasses.progressBg} rounded-full h-2`}>
                      <div 
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${(parseFloat(kpi.current) / parseFloat(kpi.target)) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        );

      case 'الفريق':
        return item.team && (
          <div className="grid grid-cols-2 gap-4">
            {item.team.map((member: string, index: number) => (
              <div key={index} className={`p-4 ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-lg flex items-center gap-3`}>
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                  {member.split(' ').map(n => n[0]).join('')}
                </div>
                <div>
                  <p className={`${themeClasses.text} text-sm font-medium`}>{member}</p>
                  <p className={`${themeClasses.textSecondary} text-xs`}>عضو الفريق</p>
                </div>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <div className={`p-8 text-center ${themeClasses.textSecondary}`}>
            <p>محتوى {tab} قيد التطوير</p>
          </div>
        );
    }
  };

  // مكونات القائمة
  const ListItem = ({ item, type, onClick, isSelected }) => (
    <div 
      className={`p-4 cursor-pointer border-b ${themeClasses.divider} transition-colors ${
        isSelected ? `${isDarkMode ? 'bg-gray-700/50' : 'bg-blue-50'} border-l-4 border-l-blue-500` : `hover:${isDarkMode ? 'bg-gray-700/30' : 'bg-gray-50'}`
      }`}
      onClick={() => onClick(item)}
    >
      <div className="flex items-start justify-between mb-2">
        <h3 className={`${themeClasses.text} font-medium text-sm`}>{item.title || item.name}</h3>
        <div className="flex items-center gap-2">
          {item.priority && (
            <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(item.priority)}`}>
              {item.priority}
            </span>
          )}
          {item.status && (
            <span className={`text-xs px-2 py-1 rounded ${getStatusColor(item.status)} text-white`}>
              {item.status}
            </span>
          )}
        </div>
      </div>
      
      <p className={`${themeClasses.textSecondary} text-xs mb-2 line-clamp-2`}>
        {item.description}
      </p>
      
      {item.progress !== undefined && (
        <div className="mb-2">
          <div className="flex justify-between text-xs mb-1">
            <span className={themeClasses.textSecondary}>التقدم</span>
            <span className={themeClasses.text}>{item.progress}%</span>
          </div>
          <div className={`w-full ${themeClasses.progressBg} rounded-full h-1`}>
            <div 
              className="bg-blue-500 h-1 rounded-full transition-all"
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
        </div>
      )}
      
      <div className={`${themeClasses.textSecondary} text-xs`}>
        {type === 'goal' && `الإدارة: ${item.owner}`}
        {type === 'kpi' && `المسؤول: ${item.responsible}`}
        {type === 'project' && `المدير: ${item.manager}`}
        {type === 'activity' && `المسؤول: ${item.assignee}`}
      </div>
    </div>
  );

  // مكون التفاصيل
  const DetailPanel = () => {
    const item = getSelectedItemData();
    const [activeDetailTab, setActiveDetailTab] = useState('عام');
    
    // إذا كنا في وضع الإضافة أو التعديل، اعرض النموذج
    if (isAddMode || isEditMode) {
      const formType = getCurrentFormType();
      if (!formType) return null;
      
      return (
        <div className={`${themeClasses.detailPanel} backdrop-blur-sm rounded-lg border h-full`}>
          <DetailForm
            type={formType}
            data={editingItem}
            onSave={getCurrentSaveFunction()}
            onCancel={handleCancelEdit}
            objectives={objectives}
            projects={projectList}
            isDarkMode={isDarkMode}
          />
        </div>
      );
    }
    
    if (!item) {
      return (
        <div className={`${themeClasses.detailPanel} backdrop-blur-sm rounded-lg border h-full flex items-center justify-center`}>
          <div className="text-center">
            <div className={`w-20 h-20 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'} rounded-full flex items-center justify-center mb-4 mx-auto`}>
              <FileText className={themeClasses.textSecondary} size={40} />
            </div>
            <h3 className={`${themeClasses.text} text-lg font-medium mb-2`}>اختر عنصر لعرض التفاصيل</h3>
            <p className={`${themeClasses.textSecondary} text-sm`}>اختر عنصر من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    // دالة للحصول على الأيقونة المناسبة حسب نوع العنصر
    const getItemIcon = () => {
      switch (activeTab) {
        case 'مهامّي': return <CheckCircle className="w-8 h-8" />;
        case 'الأهداف': return <Target className="w-8 h-8" />;
        case 'مؤشرات الأداء': return <TrendingUp className="w-8 h-8" />;
        case 'المشاريع والبرامج': return <Award className="w-8 h-8" />;
        case 'الأنشطة': return <FileText className="w-8 h-8" />;
        default: return <FileText className="w-8 h-8" />;
      }
    };

    // دالة للحصول على التبويبات المناسبة حسب نوع العنصر
    const getDetailTabs = () => {
      switch (activeTab) {
        case 'الأهداف':
          return ['عام', 'مؤشرات الأداء', 'المشاريع', 'الفريق', 'المعالم', 'المخاطر'];
        case 'مؤشرات الأداء':
          return ['عام', 'البيانات', 'التاريخ', 'التحليل'];
        case 'المشاريع والبرامج':
          return ['عام', 'الأنشطة', 'الميزانية', 'الفريق', 'المخاطر', 'المخرجات'];
        case 'الأنشطة':
          return ['عام', 'التفاصيل', 'التعليقات', 'المرفقات'];
        default:
          return ['عام', 'التفاصيل'];
      }
    };

    return (
      <div className={`${themeClasses.detailPanel} backdrop-blur-sm rounded-lg border h-full flex flex-col`}>
        {/* Header */}
        <div className={`p-4 border-b ${themeClasses.divider}`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className={`p-2 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'} ${
                activeTab === 'الأهداف' ? 'text-blue-500' :
                activeTab === 'مؤشرات الأداء' ? 'text-green-500' :
                activeTab === 'المشاريع والبرامج' ? 'text-purple-500' :
                activeTab === 'الأنشطة' ? 'text-yellow-500' :
                'text-green-500'
              }`}>
                {getItemIcon()}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className={`${themeClasses.text} text-xl font-bold`}>{item.title || item.name}</h2>
                  {item.status && (
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(item.status)} text-white`}>
                      {item.status}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm">
                  {item.priority && (
                    <div className="flex items-center gap-1">
                      <span className={themeClasses.textSecondary}>الأولوية:</span>
                      <span className={`px-2 py-0.5 rounded text-xs ${getPriorityColor(item.priority)}`}>
                        {item.priority}
                      </span>
                    </div>
                  )}
                  {item.endDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className={`w-4 h-4 ${themeClasses.textSecondary}`} />
                      <span className={themeClasses.textSecondary}>
                        {new Date(item.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  )}
                  {item.owner && (
                    <div className="flex items-center gap-1">
                      <Users className={`w-4 h-4 ${themeClasses.textSecondary}`} />
                      <span className={themeClasses.textSecondary}>{item.owner}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {hasPermission('تعديل') && (
              <div className="flex items-center gap-2">
                <button 
                  onClick={() => handleEdit(item)}
                  className={`p-2 rounded-lg ${themeClasses.button} ${themeClasses.buttonHover} ${themeClasses.text}`}
                  title="تعديل"
                >
                  <Edit size={18} />
                </button>
                <button 
                  onClick={() => handleDelete(item)}
                  className={`p-2 rounded-lg ${themeClasses.button} ${themeClasses.buttonHover} text-red-500`}
                  title="حذف"
                >
                  <Trash2 size={18} />
                </button>
                <button 
                  className={`p-2 rounded-lg ${themeClasses.button} ${themeClasses.buttonHover} ${themeClasses.text}`}
                  title="المزيد"
                >
                  <MoreHorizontal size={18} />
                </button>
              </div>
            )}
          </div>
          
          {/* التقدم العام */}
          {item.progress !== undefined && (
            <div className="mt-3">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2">
                  <span className={`${themeClasses.textSecondary} text-sm`}>التقدم العام</span>
                  <span className={`${themeClasses.text} text-sm font-medium`}>{item.progress}%</span>
                </div>
                <div className="flex items-center gap-1">
                  {item.progress < 30 && <AlertCircle className="w-4 h-4 text-red-500" />}
                  {item.progress >= 30 && item.progress < 70 && <Clock className="w-4 h-4 text-yellow-500" />}
                  {item.progress >= 70 && <CheckCircle className="w-4 h-4 text-green-500" />}
                </div>
              </div>
              <div className={`w-full ${themeClasses.progressBg} rounded-full h-2`}>
                <div 
                  className={`h-2 rounded-full transition-all ${
                    item.progress < 30 ? 'bg-red-500' :
                    item.progress < 70 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${item.progress}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className={`flex border-b ${themeClasses.divider} px-4`}>
          {getDetailTabs().map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveDetailTab(tab)}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeDetailTab === tab
                  ? `${themeClasses.text} border-blue-500`
                  : `${themeClasses.textSecondary} border-transparent hover:${themeClasses.text}`
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {renderDetailContent(item, activeDetailTab)}
        </div>
      </div>
    );
  };

  // دالة اختيار العنصر
  const handleItemSelect = (item) => {
    setSelectedItem(item);
    setIsEditMode(false);
    setIsAddMode(false);
  };
  
  // دالة للحصول على نوع النموذج الحالي
  const getCurrentFormType = () => {
    switch (activeTab) {
      case 'الأهداف': return 'objective';
      case 'مؤشرات الأداء': return 'kpi';
      case 'المشاريع والبرامج': return 'project';
      case 'الأنشطة': return 'activity';
      default: return null;
    }
  };
  
  // دالة للحصول على دالة الحفظ المناسبة
  const getCurrentSaveFunction = () => {
    switch (activeTab) {
      case 'الأهداف': return handleSaveObjective;
      case 'مؤشرات الأداء': return handleSaveKPI;
      case 'المشاريع والبرامج': return handleSaveProject;
      case 'الأنشطة': return handleSaveActivity;
      default: return async () => {};
    }
  };
  
  // وظائف إدارة المشاريع
  const handleSaveProject = async (projectData: any) => {
    try {
      if (editingItem && activeTab === 'المشاريع والبرامج') {
        setProjectList(prev =>
          prev.map(proj =>
            proj.id === editingItem.id
              ? { ...projectData, id: editingItem.id }
              : proj
          )
        );
      } else {
        setProjectList(prev => [...prev, projectData]);
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
    } catch (error) {
      throw error;
    }
  };
  
  const handleDeleteProject = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
      try {
        setProjectList(prev => prev.filter(proj => proj.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف المشروع بنجاح');
      } catch (error) {
        notifications.error('خطأ في الحذف', 'فشل في حذف المشروع');
      }
    }
  };
  
  // وظائف إدارة الأنشطة
  const handleSaveActivity = async (activityData: any) => {
    try {
      if (editingItem && activeTab === 'الأنشطة') {
        setActivityList(prev =>
          prev.map(act =>
            act.id === editingItem.id
              ? { ...activityData, id: editingItem.id }
              : act
          )
        );
      } else {
        setActivityList(prev => [...prev, activityData]);
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
    } catch (error) {
      throw error;
    }
  };
  
  const handleDeleteActivity = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا النشاط؟')) {
      try {
        setActivityList(prev => prev.filter(act => act.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف النشاط بنجاح');
      } catch (error) {
        notifications.error('خطأ في الحذف', 'فشل في حذف النشاط');
      }
    }
  };

  return (
    <div className={`min-h-screen ${themeClasses.mainBg}`} dir="rtl">
      {/* Background overlay */}
      <div 
        className="fixed inset-0"
        style={{
          background: `radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), 
                      radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)`
        }}
      />
      
      {/* Sidebar - للتنقل بين الصفحات الرئيسية */}
      <div className={`fixed right-0 top-0 h-full w-16 ${themeClasses.sidebar} border-l flex flex-col items-center py-4 z-10`}>
        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded mb-8"></div>
        
        <nav className="flex flex-col gap-4">
          <button 
            className={`p-2 rounded ${activeTab === 'مهامّي' ? `${themeClasses.text} ${themeClasses.button}` : `${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`}`}
            onClick={() => {setActiveTab('مهامّي'); setSelectedItem(null);}}
            title="مهامّي"
          >
            <CheckCircle size={20} />
          </button>
          <div className="w-8 h-px bg-gray-600 my-2"></div>
          <button 
            className={`p-2 rounded ${activeTab === 'الأهداف' ? `${themeClasses.text} ${themeClasses.button}` : `${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`}`}
            onClick={() => {setActiveTab('الأهداف'); setSelectedItem(null);}}
            title="الأهداف الاستراتيجية"
          >
            <Target size={20} />
          </button>
          <button 
            className={`p-2 rounded ${activeTab === 'مؤشرات الأداء' ? `${themeClasses.text} ${themeClasses.button}` : `${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`}`}
            onClick={() => {setActiveTab('مؤشرات الأداء'); setSelectedItem(null);}}
            title="مؤشرات الأداء"
          >
            <TrendingUp size={20} />
          </button>
          <button 
            className={`p-2 rounded ${activeTab === 'المشاريع والبرامج' ? `${themeClasses.text} ${themeClasses.button}` : `${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`}`}
            onClick={() => {setActiveTab('المشاريع والبرامج'); setSelectedItem(null);}}
            title="المشاريع والبرامج"
          >
            <Award size={20} />
          </button>
          <button 
            className={`p-2 rounded ${activeTab === 'الأنشطة' ? `${themeClasses.text} ${themeClasses.button}` : `${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`}`}
            onClick={() => {setActiveTab('الأنشطة'); setSelectedItem(null);}}
            title="جميع الأنشطة"
          >
            <FileText size={20} />
          </button>
          <div className="w-8 h-px bg-gray-600 my-2"></div>
          <button className={`p-2 rounded ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`} title="الفرق">
            <Users size={20} />
          </button>
          <button className={`p-2 rounded ${themeClasses.textSecondary} hover:${themeClasses.text} ${themeClasses.buttonHover}`} title="التقارير">
            <BarChart3 size={20} />
          </button>
        </nav>
      </div>

      {/* Main Content */}
      <div className="mr-16 relative z-10 h-screen flex flex-col">
        {/* Header */}
        <div className={`p-4 border-b ${themeClasses.divider}`}>
          <div>
            <h1 className={`text-2xl font-bold mb-1 ${themeClasses.text}`}>الخطة التشغيلية 2025</h1>
            <p className={`${themeClasses.textSecondary} text-sm`}>إدارة الأهداف والمؤشرات والمشاريع والأنشطة</p>
          </div>
        </div>

        {/* Navigation Tabs - للتنقل داخل الصفحة (طرق العرض) */}
        <div className={`flex ${themeClasses.card} backdrop-blur-sm border-b ${themeClasses.divider} overflow-x-auto`}>
          {/* عرض تبويبات مختلفة حسب الصفحة النشطة */}
          {activeTab === 'مهامّي' && (
            <>
              <button 
                className={`px-6 py-3 border-b-2 ${myTasksView === 'اليوم' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setMyTasksView('اليوم')}
              >
                مهام اليوم
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${myTasksView === 'الأسبوع' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setMyTasksView('الأسبوع')}
              >
                مهام الأسبوع
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${myTasksView === 'المكتملة' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setMyTasksView('المكتملة')}
              >
                المكتملة
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${myTasksView === 'المتأخرة' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setMyTasksView('المتأخرة')}
              >
                المتأخرة
              </button>
            </>
          )}
          
          {activeTab === 'الأهداف' && (
            <>
              <button 
                className={`px-6 py-3 border-b-2 ${objectivesView === 'الكل' ? 'border-blue-500 text-blue-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setObjectivesView('الكل')}
              >
                جميع الأهداف
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${objectivesView === 'جاري' ? 'border-blue-500 text-blue-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setObjectivesView('جاري')}
              >
                قيد التنفيذ
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${objectivesView === 'مكتمل' ? 'border-blue-500 text-blue-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setObjectivesView('مكتمل')}
              >
                مكتملة
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${objectivesView === 'متأخر' ? 'border-blue-500 text-blue-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setObjectivesView('متأخر')}
              >
                متأخرة
              </button>
            </>
          )}
          
          {activeTab === 'مؤشرات الأداء' && (
            <>
              <button 
                className={`px-6 py-3 border-b-2 ${kpisView === 'الكل' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setKpisView('الكل')}
              >
                جميع المؤشرات
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${kpisView === 'جاري' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setKpisView('جاري')}
              >
                جاري التنفيذ
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${kpisView === 'محقق' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setKpisView('محقق')}
              >
                محققة
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${kpisView === 'متأخر' ? 'border-green-500 text-green-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setKpisView('متأخر')}
              >
                تحت المراقبة
              </button>
            </>
          )}
          
          {activeTab === 'المشاريع والبرامج' && (
            <>
              <button 
                className={`px-6 py-3 border-b-2 ${projectsView === 'الكل' ? 'border-purple-500 text-purple-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setProjectsView('الكل')}
              >
                جميع المشاريع
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${projectsView === 'جاري' ? 'border-purple-500 text-purple-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setProjectsView('جاري')}
              >
                قيد التنفيذ
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${projectsView === 'التخطيط' ? 'border-purple-500 text-purple-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setProjectsView('التخطيط')}
              >
                في التخطيط
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${projectsView === 'مكتمل' ? 'border-purple-500 text-purple-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setProjectsView('مكتمل')}
              >
                مكتملة
              </button>
            </>
          )}
          
          {activeTab === 'الأنشطة' && (
            <>
              <button 
                className={`px-6 py-3 border-b-2 ${activitiesViewMode === 'البطاقات' ? 'border-yellow-500 text-yellow-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setActivitiesViewMode('البطاقات')}
              >
                البطاقات
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${activitiesViewMode === 'الجدول' ? 'border-yellow-500 text-yellow-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setActivitiesViewMode('الجدول')}
              >
                الجدول
              </button>
              <button 
                className={`px-6 py-3 border-b-2 ${activitiesViewMode === 'الكانبان' ? 'border-yellow-500 text-yellow-500' : `border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text}`} whitespace-nowrap`}
                onClick={() => setActivitiesViewMode('الكانبان')}
              >
                الكانبان
              </button>
              <button className={`px-6 py-3 border-b-2 border-transparent ${themeClasses.textSecondary} hover:${themeClasses.text} whitespace-nowrap`}>
                التقويم
              </button>
            </>
          )}
        </div>

        {/* Split View Content */}
        <div className="flex-1 flex min-h-0" ref={containerRef}>
          {/* List Panel - دائماً ظاهرة */}
          <div 
            className={`${themeClasses.listPanel} backdrop-blur-sm border-l ${themeClasses.divider} flex flex-col`}
            style={{ width: `${listWidth}%` }}
          >
            {/* Search Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 space-y-3">
              <div className="relative">
                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${themeClasses.textSecondary}`} size={16} />
                <input 
                  type="text" 
                  placeholder="البحث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full ${themeClasses.input} rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm`}
                />
              </div>
              
              {/* Advanced Filters */}
              <div className="grid grid-cols-1 gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className={`w-full ${themeClasses.input} rounded-lg px-3 py-1.5 text-sm focus:outline-none`}
                >
                  <option value="">جميع الحالات</option>
                  <option value="جاري">جاري</option>
                  <option value="مكتمل">مكتمل</option>
                  <option value="متأخر">متأخر</option>
                  <option value="مجدول">مجدول</option>
                  <option value="معلق">معلق</option>
                </select>
                
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className={`w-full ${themeClasses.input} rounded-lg px-3 py-1.5 text-sm focus:outline-none`}
                >
                  <option value="">جميع الأولويات</option>
                  <option value="عالية">عالية</option>
                  <option value="متوسطة">متوسطة</option>
                  <option value="منخفضة">منخفضة</option>
                </select>
                
                <select
                  value={departmentFilter}
                  onChange={(e) => setDepartmentFilter(e.target.value)}
                  className={`w-full ${themeClasses.input} rounded-lg px-3 py-1.5 text-sm focus:outline-none`}
                >
                  <option value="">جميع الإدارات</option>
                  <option value="الإدارة المالية">الإدارة المالية</option>
                  <option value="تقنية المعلومات">تقنية المعلومات</option>
                  <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                  <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                  <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                  <option value="إدارة المرافق">إدارة المرافق</option>
                </select>
              </div>
            </div>
            
            {/* List Items */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'مهامّي' && myTasks
                .filter(task => {
                  // فلترة حسب العرض
                  let viewFilter = false;
                  switch(myTasksView) {
                    case 'اليوم': viewFilter = task.isToday; break;
                    case 'الأسبوع': viewFilter = task.isThisWeek; break;
                    case 'المكتملة': viewFilter = task.isCompleted; break;
                    case 'المتأخرة': viewFilter = !task.isCompleted && new Date(task.endDate) < new Date(); break;
                    default: viewFilter = task.isToday;
                  }
                  
                  // فلترة حسب البحث
                  const searchFilter = !searchTerm || 
                    task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    task.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    task.assignee.toLowerCase().includes(searchTerm.toLowerCase());
                  
                  // فلترة حسب الحالة
                  const statusMatch = !statusFilter || task.status === statusFilter;
                  
                  // فلترة حسب الأولوية
                  const priorityMatch = !priorityFilter || task.priority === priorityFilter;
                  
                  // فلترة حسب الإدارة
                  const departmentMatch = !departmentFilter || task.department === departmentFilter;
                  
                  return viewFilter && searchFilter && statusMatch && priorityMatch && departmentMatch;
                })
                .map(task => (
                  <div
                    key={task.id}
                    className={`p-4 border-b ${themeClasses.divider} transition-colors hover:${isDarkMode ? 'bg-gray-700/30' : 'bg-gray-50'} ${
                      selectedItem?.id === task.id ? `${isDarkMode ? 'bg-gray-700/50' : 'bg-blue-50'} border-l-4 border-l-green-500` : ''
                    }`}
                    onClick={() => setSelectedItem(task)}
                  >
                    <div className="flex items-start gap-3">
                      <button
                        className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                          task.isCompleted 
                            ? 'bg-green-500 border-green-500 text-white' 
                            : `border-gray-400 hover:border-green-500 ${isDarkMode ? 'hover:bg-green-500/10' : 'hover:bg-green-50'}`
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleTaskCompletion(task.id);
                        }}
                      >
                        {task.isCompleted && <CheckCircle size={12} />}
                      </button>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className={`font-medium text-sm ${task.isCompleted ? `line-through ${themeClasses.textSecondary}` : themeClasses.text}`}>
                          {task.title}
                        </h3>
                        <p className={`text-xs mt-1 ${themeClasses.textSecondary}`}>
                          {task.project}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </span>
                          <span className={`text-xs ${task.isToday ? 'text-red-500' : themeClasses.textSecondary}`}>
                            {task.dueDate.toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              }
              
              {activeTab === 'الأهداف' && objectives
                .filter(goal => {
                  // فلترة حسب العرض
                  let viewFilter = false;
                  switch(objectivesView) {
                    case 'الكل': viewFilter = true; break;
                    case 'جاري': viewFilter = goal.status === 'جاري'; break;
                    case 'مكتمل': viewFilter = goal.status === 'مكتمل'; break;
                    case 'متأخر': viewFilter = goal.status === 'متأخر' || (goal.status === 'جاري' && new Date(goal.endDate) < new Date()); break;
                    default: viewFilter = true;
                  }
                  
                  // فلترة حسب البحث
                  const searchFilter = !searchTerm || 
                    goal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    goal.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    goal.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    goal.manager.toLowerCase().includes(searchTerm.toLowerCase());
                  
                  // فلترة حسب الحالة
                  const statusMatch = !statusFilter || goal.status === statusFilter;
                  
                  // فلترة حسب الأولوية  
                  const priorityMatch = !priorityFilter || goal.priority === priorityFilter;
                  
                  // فلترة حسب الإدارة
                  const departmentMatch = !departmentFilter || goal.department === departmentFilter;
                  
                  return viewFilter && searchFilter && statusMatch && priorityMatch && departmentMatch;
                })
                .map(goal => (
                <ListItem 
                  key={goal.id} 
                  item={goal} 
                  type="goal" 
                  onClick={handleItemSelect}
                  isSelected={selectedItem?.id === goal.id}
                />
              ))}
              
              {activeTab === 'مؤشرات الأداء' && kpiList
                .filter(kpi => {
                  // فلترة حسب العرض
                  let viewFilter = false;
                  switch(kpisView) {
                    case 'الكل': viewFilter = true; break;
                    case 'جاري': viewFilter = kpi.status === 'جاري'; break;
                    case 'محقق': viewFilter = kpi.status === 'محقق'; break;
                    case 'متأخر': viewFilter = kpi.status === 'متأخر' || kpi.status === 'تحت المراقبة'; break;
                    default: viewFilter = true;
                  }
                  
                  // فلترة حسب البحث
                  const searchFilter = !searchTerm || 
                    kpi.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    kpi.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    kpi.goal.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    kpi.responsible.toLowerCase().includes(searchTerm.toLowerCase());
                  
                  return viewFilter && searchFilter;
                })
                .map(kpi => (
                <ListItem 
                  key={kpi.id} 
                  item={kpi} 
                  type="kpi" 
                  onClick={handleItemSelect}
                  isSelected={selectedItem?.id === kpi.id}
                />
              ))}
              
              {activeTab === 'المشاريع والبرامج' && projectList
                .filter(project => {
                  // فلترة حسب العرض
                  let viewFilter = false;
                  switch(projectsView) {
                    case 'الكل': viewFilter = true; break;
                    case 'جاري': viewFilter = project.status === 'جاري'; break;
                    case 'التخطيط': viewFilter = project.status === 'التخطيط'; break;
                    case 'مكتمل': viewFilter = project.status === 'مكتمل'; break;
                    default: viewFilter = true;
                  }
                  
                  // فلترة حسب البحث
                  const searchFilter = !searchTerm || 
                    project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    project.goal.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    project.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    project.manager.toLowerCase().includes(searchTerm.toLowerCase());
                  
                  return viewFilter && searchFilter;
                })
                .map(project => (
                <ListItem 
                  key={project.id} 
                  item={project} 
                  type="project" 
                  onClick={handleItemSelect}
                  isSelected={selectedItem?.id === project.id}
                />
              ))}
              
              {activeTab === 'الأنشطة' && (activitiesViewMode === 'البطاقات' || activitiesViewMode === 'الجدول') && 
                activityList
                .filter(activity => {
                  // فلترة حسب البحث
                  return !searchTerm || 
                    activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    activity.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    activity.assignee.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    activity.department.toLowerCase().includes(searchTerm.toLowerCase());
                })
                .map(activity => (
                <ListItem 
                  key={activity.id} 
                  item={activity} 
                  type="activity" 
                  onClick={handleItemSelect}
                  isSelected={selectedItem?.id === activity.id}
                />
              ))}
              
              {activeTab === 'الأنشطة' && activitiesViewMode === 'الكانبان' && (
                <div className="p-4 text-center">
                  <p className={`${themeClasses.textSecondary} text-sm`}>
                    عرض الكانبان قيد التطوير
                  </p>
                </div>
              )}
            </div>
          </div>
          
          {/* Resize Handle */}
          <div 
            className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
              isResizing 
                ? 'bg-blue-500' 
                : isDarkMode ? 'bg-gray-700 hover:bg-blue-500' : 'bg-gray-300 hover:bg-blue-500'
            }`}
            onMouseDown={() => setIsResizing(true)}
          >
            <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
          </div>

          {/* Detail Panel - دائماً ظاهرة */}
          <div className="flex-1 p-4 overflow-hidden">
            <DetailPanel />
          </div>
        </div>
      </div>

      {/* Role Switcher (for demo) */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className={`${themeClasses.input} rounded-lg px-3 py-2 text-sm focus:outline-none`}
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default OperationalPlan;