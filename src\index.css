@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* متغيرات الألوان المحسنة */
  --color-primary: #0047ab;
  --color-primary-light: #2563eb;
  --color-primary-dark: #003a8c;
  --color-secondary: #34A853;
  --color-warning: #FBBC05;
  --color-danger: #EA4335;
  --color-info: #4285F4;
  --color-success: #0F9D58;
  --color-neutral: #757575;
  --color-accent: #9C27B0;

  /* متغيرات حالات العناصر */
  --color-status-red: #FFEBEE;
  --color-status-yellow: #FFF8E1;
  --color-status-green: #E8F5E9;
  --color-status-blue: #E3F2FD;

  /* إعدادات الخط */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
}

@layer base {
  html, body {
    font-family: 'Tajawal', system-ui, sans-serif;
    background-color: #f7f9fc;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Tajawal', system-ui, sans-serif;
    line-height: 1.3;
  }

  /* ضبط أحجام الخطوط */
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.375rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.875rem; }

  * {
    @apply selection:bg-primary-300 selection:text-primary-900;
  }
}

@layer components {
  .app-container {
    @apply min-h-screen bg-neutral-50;
  }
  
  .main-content {
    @apply p-3 md:p-4 lg:p-5;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-neutral-100 p-3 md:p-4 transition-all duration-300;
  }

  .card:hover {
    @apply shadow-md;
  }

  .card-enhanced {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
  }

  .card-enhanced:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }

  .btn {
    @apply px-3 py-1.5 rounded-lg transition-all duration-200 font-medium text-sm;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800;
  }
  
  .btn-accent {
    @apply bg-accent-500 text-neutral-900 hover:bg-accent-600 active:bg-accent-700;
  }
  
  .btn-outline {
    @apply border border-neutral-300 hover:bg-neutral-50 active:bg-neutral-100;
  }

  .btn-sm {
    @apply px-2 py-1 text-xs;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 bg-white text-sm;
  }

  .label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }

  .badge {
    @apply px-2 py-0.5 text-xs rounded-full font-medium;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply bg-error-100 text-error-800;
  }
  
  .badge-info {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .progress-bar {
    @apply h-1.5 rounded-full bg-neutral-200 overflow-hidden;
  }

  .progress-value {
    @apply h-full rounded-full transition-all duration-300;
  }

  /* أنماط حالات العناصر */
  .status-red {
    background-color: var(--color-status-red);
  }

  .status-yellow {
    background-color: var(--color-status-yellow);
  }

  .status-green {
    background-color: var(--color-status-green);
  }

  .status-blue {
    background-color: var(--color-status-blue);
  }

  /* أنيميشن */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease;
  }

  .animate-fadeInRight {
    animation: fadeInRight 0.3s ease;
  }

  /* تحسينات القائمة الجانبية */
  .menu-item {
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 0;
    display: flex;
    align-items: center;
  }

  .menu-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .menu-item.active {
    background-color: rgba(0, 71, 171, 0.08);
    color: var(--color-primary);
    font-weight: 500;
  }

  .menu-item .menu-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    opacity: 0.8;
    font-size: 18px;
  }

  .menu-item.active .menu-icon {
    opacity: 1;
  }
}

/* تعريف الحركات */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInRight {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}