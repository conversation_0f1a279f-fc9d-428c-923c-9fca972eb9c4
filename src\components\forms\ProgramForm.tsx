import { useState } from 'react';
import { X, Save, Loader2, Plus, Trash2 } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface ProgramFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (program: any) => Promise<void>;
  editData?: any;
}

const ProgramForm = ({ isOpen, onClose, onSave, editData }: ProgramFormProps) => {
  const [loading, setLoading] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    objective: editData?.objective || '',
    manager: editData?.manager || '',
    department: editData?.department || '',
    status: editData?.status || 'جديد',
    startDate: editData?.startDate || '',
    endDate: editData?.endDate || '',
    budget: editData?.budget || '',
    participants: editData?.participants || '',
    expectedOutcome: editData?.expectedOutcome || ''
  });

  const [projects, setProjects] = useState<string[]>(
    editData?.projects || ['']
  );
  
  const [kpis, setKpis] = useState<string[]>(
    editData?.kpis || ['']
  );

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان البرنامج مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف البرنامج مطلوب';
    }

    if (!formData.objective.trim()) {
      newErrors.objective = 'الهدف الاستراتيجي مطلوب';
    }

    if (!formData.manager.trim()) {
      newErrors.manager = 'مدير البرنامج مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'القسم المسؤول مطلوب';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية مطلوب';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    // التحقق من المشاريع
    const validProjects = projects.filter(p => p.trim());
    if (validProjects.length === 0) {
      newErrors.projects = 'يجب إضافة مشروع واحد على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      const validProjects = projects.filter(p => p.trim());
      const validKpis = kpis.filter(k => k.trim());

      await onSave({
        ...formData,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        participants: formData.participants ? parseInt(formData.participants) : 1,
        projects: validProjects,
        kpis: validKpis,
        id: editData?.id || `program-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        progress: editData?.progress || 0
      });
      
      notifications.success(
        editData ? 'تم تحديث البرنامج' : 'تم إضافة البرنامج', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // إدارة المشاريع
  const addProject = () => {
    setProjects(prev => [...prev, '']);
  };

  const updateProject = (index: number, value: string) => {
    setProjects(prev => prev.map((p, i) => i === index ? value : p));
    if (errors.projects) {
      setErrors(prev => ({ ...prev, projects: '' }));
    }
  };

  const removeProject = (index: number) => {
    if (projects.length > 1) {
      setProjects(prev => prev.filter((_, i) => i !== index));
    }
  };

  // إدارة مؤشرات الأداء
  const addKpi = () => {
    setKpis(prev => [...prev, '']);
  };

  const updateKpi = (index: number, value: string) => {
    setKpis(prev => prev.map((k, i) => i === index ? value : k));
  };

  const removeKpi = (index: number) => {
    if (kpis.length > 1) {
      setKpis(prev => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل البرنامج' : 'إضافة برنامج جديد'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* العنوان */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عنوان البرنامج *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`input-field ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان البرنامج"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف البرنامج *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للبرنامج"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* الهدف الاستراتيجي */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الهدف الاستراتيجي المرتبط *
            </label>
            <input
              type="text"
              value={formData.objective}
              onChange={(e) => handleChange('objective', e.target.value)}
              className={`input-field ${errors.objective ? 'border-red-500' : ''}`}
              placeholder="الهدف الاستراتيجي الذي يحققه هذا البرنامج"
            />
            {errors.objective && <p className="text-red-500 text-xs mt-1">{errors.objective}</p>}
          </div>

          {/* المدير والقسم */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مدير البرنامج *
              </label>
              <input
                type="text"
                value={formData.manager}
                onChange={(e) => handleChange('manager', e.target.value)}
                className={`input-field ${errors.manager ? 'border-red-500' : ''}`}
                placeholder="اسم مدير البرنامج"
              />
              {errors.manager && <p className="text-red-500 text-xs mt-1">{errors.manager}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القسم المسؤول *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`input-field ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر القسم</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>
          </div>

          {/* الحالة والمشاركين */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                حالة البرنامج
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="input-field"
              >
                <option value="جديد">جديد</option>
                <option value="جاري التنفيذ">جاري التنفيذ</option>
                <option value="مكتمل">مكتمل</option>
                <option value="متأخر">متأخر</option>
                <option value="متعثر">متعثر</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عدد المستفيدين
              </label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => handleChange('participants', e.target.value)}
                className="input-field"
                placeholder="1"
                min="1"
              />
            </div>
          </div>

          {/* التواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`input-field ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={`input-field ${errors.endDate ? 'border-red-500' : ''}`}
              />
              {errors.endDate && <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>}
            </div>
          </div>

          {/* الميزانية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الميزانية المقدرة (ريال)
            </label>
            <input
              type="number"
              value={formData.budget}
              onChange={(e) => handleChange('budget', e.target.value)}
              className="input-field"
              placeholder="0"
              min="0"
            />
          </div>

          {/* المشاريع المرتبطة */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                المشاريع المرتبطة *
              </label>
              <button
                type="button"
                onClick={addProject}
                className="text-primary-600 hover:text-primary-700 text-sm flex items-center gap-1"
              >
                <Plus className="w-3 h-3" />
                إضافة مشروع
              </button>
            </div>
            <div className="space-y-2">
              {projects.map((project, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={project}
                    onChange={(e) => updateProject(index, e.target.value)}
                    className="input-field flex-1"
                    placeholder={`المشروع ${index + 1}`}
                  />
                  {projects.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeProject(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
            {errors.projects && <p className="text-red-500 text-xs mt-1">{errors.projects}</p>}
          </div>

          {/* مؤشرات الأداء */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                مؤشرات الأداء
              </label>
              <button
                type="button"
                onClick={addKpi}
                className="text-primary-600 hover:text-primary-700 text-sm flex items-center gap-1"
              >
                <Plus className="w-3 h-3" />
                إضافة مؤشر
              </button>
            </div>
            <div className="space-y-2">
              {kpis.map((kpi, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={kpi}
                    onChange={(e) => updateKpi(index, e.target.value)}
                    className="input-field flex-1"
                    placeholder={`مؤشر الأداء ${index + 1}`}
                  />
                  {kpis.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeKpi(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* النتائج المتوقعة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              النتائج المتوقعة
            </label>
            <textarea
              value={formData.expectedOutcome}
              onChange={(e) => handleChange('expectedOutcome', e.target.value)}
              rows={2}
              className="input-field"
              placeholder="النتائج والمخرجات المتوقعة من هذا البرنامج"
            />
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ البرنامج'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProgramForm;
