import { useState } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface KpiFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (kpi: any) => Promise<void>;
  editData?: any;
}

const KpiForm = ({ isOpen, onClose, onSave, editData }: KpiFormProps) => {
  const [loading, setLoading] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    description: editData?.description || '',
    objective: editData?.objective || '',
    category: editData?.category || '',
    unit: editData?.unit || '',
    direction: editData?.direction || 'INCREASING',
    currentValue: editData?.currentValue || '',
    targetValue: editData?.targetValue || '',
    frequency: editData?.frequency || 'شهري',
    dataSource: editData?.dataSource || '',
    responsible: editData?.responsible || '',
    department: editData?.department || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المؤشر مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المؤشر مطلوب';
    }

    if (!formData.objective.trim()) {
      newErrors.objective = 'الهدف المرتبط مطلوب';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'فئة المؤشر مطلوبة';
    }

    if (!formData.unit.trim()) {
      newErrors.unit = 'وحدة القياس مطلوبة';
    }

    if (!formData.targetValue.trim()) {
      newErrors.targetValue = 'القيمة المستهدفة مطلوبة';
    }

    if (!formData.responsible.trim()) {
      newErrors.responsible = 'المسؤول عن المؤشر مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'القسم المسؤول مطلوب';
    }

    // التحقق من القيم الرقمية
    if (formData.currentValue && isNaN(Number(formData.currentValue))) {
      newErrors.currentValue = 'القيمة الحالية يجب أن تكون رقم';
    }

    if (formData.targetValue && isNaN(Number(formData.targetValue))) {
      newErrors.targetValue = 'القيمة المستهدفة يجب أن تكون رقم';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        currentValue: formData.currentValue ? parseFloat(formData.currentValue) : 0,
        targetValue: parseFloat(formData.targetValue),
        id: editData?.id || `kpi-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: editData?.status || 'نشط',
        trend: editData?.trend || 'stable',
        change: editData?.change || 0,
        lastUpdated: new Date().toISOString().split('T')[0]
      });
      
      notifications.success(
        editData ? 'تم تحديث المؤشر' : 'تم إضافة المؤشر', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل مؤشر الأداء' : 'إضافة مؤشر أداء جديد'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* اسم المؤشر */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اسم المؤشر *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`input-field ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل اسم مؤشر الأداء"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف المؤشر *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي لمؤشر الأداء وطريقة قياسه"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* الهدف المرتبط والفئة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الهدف الاستراتيجي المرتبط *
              </label>
              <input
                type="text"
                value={formData.objective}
                onChange={(e) => handleChange('objective', e.target.value)}
                className={`input-field ${errors.objective ? 'border-red-500' : ''}`}
                placeholder="الهدف الذي يقيسه هذا المؤشر"
              />
              {errors.objective && <p className="text-red-500 text-xs mt-1">{errors.objective}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                فئة المؤشر *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                className={`input-field ${errors.category ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الفئة</option>
                <option value="جودة الخدمة">جودة الخدمة</option>
                <option value="الكفاءة التشغيلية">الكفاءة التشغيلية</option>
                <option value="رضا المستفيدين">رضا المستفيدين</option>
                <option value="الأداء المالي">الأداء المالي</option>
                <option value="التطوير والابتكار">التطوير والابتكار</option>
                <option value="الموارد البشرية">الموارد البشرية</option>
              </select>
              {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
            </div>
          </div>

          {/* وحدة القياس واتجاه المؤشر */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                وحدة القياس *
              </label>
              <input
                type="text"
                value={formData.unit}
                onChange={(e) => handleChange('unit', e.target.value)}
                className={`input-field ${errors.unit ? 'border-red-500' : ''}`}
                placeholder="مثل: %, عدد, ريال, يوم"
              />
              {errors.unit && <p className="text-red-500 text-xs mt-1">{errors.unit}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اتجاه المؤشر
              </label>
              <select
                value={formData.direction}
                onChange={(e) => handleChange('direction', e.target.value)}
                className="input-field"
              >
                <option value="INCREASING">تصاعدي (كلما زاد كان أفضل)</option>
                <option value="DECREASING">تنازلي (كلما قل كان أفضل)</option>
              </select>
            </div>
          </div>

          {/* القيم */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القيمة الحالية
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.currentValue}
                onChange={(e) => handleChange('currentValue', e.target.value)}
                className={`input-field ${errors.currentValue ? 'border-red-500' : ''}`}
                placeholder="0"
              />
              {errors.currentValue && <p className="text-red-500 text-xs mt-1">{errors.currentValue}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القيمة المستهدفة *
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.targetValue}
                onChange={(e) => handleChange('targetValue', e.target.value)}
                className={`input-field ${errors.targetValue ? 'border-red-500' : ''}`}
                placeholder="0"
              />
              {errors.targetValue && <p className="text-red-500 text-xs mt-1">{errors.targetValue}</p>}
            </div>
          </div>

          {/* تكرار القياس ومصدر البيانات */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تكرار القياس
              </label>
              <select
                value={formData.frequency}
                onChange={(e) => handleChange('frequency', e.target.value)}
                className="input-field"
              >
                <option value="يومي">يومي</option>
                <option value="أسبوعي">أسبوعي</option>
                <option value="شهري">شهري</option>
                <option value="ربع سنوي">ربع سنوي</option>
                <option value="نصف سنوي">نصف سنوي</option>
                <option value="سنوي">سنوي</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مصدر البيانات
              </label>
              <input
                type="text"
                value={formData.dataSource}
                onChange={(e) => handleChange('dataSource', e.target.value)}
                className="input-field"
                placeholder="مثل: نظام إدارة العملاء، الاستبيانات، التقارير المالية"
              />
            </div>
          </div>

          {/* المسؤول والقسم */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المسؤول عن المؤشر *
              </label>
              <input
                type="text"
                value={formData.responsible}
                onChange={(e) => handleChange('responsible', e.target.value)}
                className={`input-field ${errors.responsible ? 'border-red-500' : ''}`}
                placeholder="اسم المسؤول عن جمع وتحديث البيانات"
              />
              {errors.responsible && <p className="text-red-500 text-xs mt-1">{errors.responsible}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القسم المسؤول *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`input-field ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر القسم</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
                <option value="الإدارة المالية">الإدارة المالية</option>
                <option value="إدارة الموارد البشرية">إدارة الموارد البشرية</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ المؤشر'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default KpiForm;
