import React, { useState } from 'react';
import { Trash2, AlertTriangle, CheckCircle, Database, Target, BarChart3, Building2, Activity, Users, FileText } from 'lucide-react';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { supabase } from '../../lib/supabase';

const DeleteDataPage = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const notifications = useNotifications();

  // قائمة الجداول المتاحة للحذف
  const availableTables = [
    {
      id: 'strategic_objectives',
      name: 'الأهداف الاستراتيجية',
      icon: Target,
      color: 'blue',
      description: 'حذف جميع الأهداف الاستراتيجية'
    },
    {
      id: 'kpis',
      name: 'مؤشرات الأداء',
      icon: BarChart3,
      color: 'green',
      description: 'حذف جميع مؤشرات الأداء الرئيسية'
    },
    {
      id: 'initiatives',
      name: 'المشاريع',
      icon: Building2,
      color: 'purple',
      description: 'حذف جميع المشاريع والمبادرات'
    },
    {
      id: 'activities',
      name: 'الأنشطة',
      icon: Activity,
      color: 'orange',
      description: 'حذف جميع الأنشطة التشغيلية'
    },
    {
      id: 'departments',
      name: 'الأقسام',
      icon: Building2,
      color: 'indigo',
      description: 'حذف جميع الأقسام والإدارات'
    },
    {
      id: 'users',
      name: 'المستخدمين',
      icon: Users,
      color: 'pink',
      description: 'حذف جميع المستخدمين (عدا الحساب الحالي)'
    },
    {
      id: 'reports',
      name: 'التقارير',
      icon: FileText,
      color: 'yellow',
      description: 'حذف جميع التقارير المحفوظة'
    }
  ];

  const handleTableSelection = (tableId: string) => {
    setSelectedTables(prev => 
      prev.includes(tableId) 
        ? prev.filter(id => id !== tableId)
        : [...prev, tableId]
    );
  };

  const selectAllTables = () => {
    setSelectedTables(availableTables.map(table => table.id));
  };

  const clearSelection = () => {
    setSelectedTables([]);
  };

  const handleDeleteSelected = async () => {
    if (selectedTables.length === 0) {
      notifications.warning('لا توجد جداول محددة', 'يرجى تحديد الجداول المراد حذفها');
      return;
    }

    const confirmed = window.confirm(
      `هل أنت متأكد من حذف البيانات من ${selectedTables.length} جدول؟\n\nهذا الإجراء لا يمكن التراجع عنه!`
    );

    if (!confirmed) return;

    setLoading(true);
    const results = {
      success: [],
      failed: []
    };

    try {
      for (const tableId of selectedTables) {
        try {
          // حذف خاص للمستخدمين (عدا الحساب الحالي)
          if (tableId === 'users') {
            const { error } = await supabase
              .from('users')
              .delete()
              .neq('email', '<EMAIL>'); // حماية الحساب الرئيسي
          } else {
            const { error } = await supabase
              .from(tableId)
              .delete()
              .neq('id', '00000000-0000-0000-0000-000000000000'); // حذف جميع السجلات
          }

          const tableName = availableTables.find(t => t.id === tableId)?.name || tableId;
          results.success.push(tableName);
        } catch (error) {
          console.error(`Error deleting ${tableId}:`, error);
          const tableName = availableTables.find(t => t.id === tableId)?.name || tableId;
          results.failed.push(tableName);
        }
      }

      // عرض النتائج
      if (results.success.length > 0) {
        notifications.success(
          'تم الحذف بنجاح! ✅',
          `تم حذف البيانات من: ${results.success.join(', ')}`
        );
      }

      if (results.failed.length > 0) {
        notifications.error(
          'فشل في بعض العمليات ❌',
          `فشل حذف البيانات من: ${results.failed.join(', ')}`
        );
      }

      // مسح التحديد
      setSelectedTables([]);

    } catch (error) {
      console.error('Error in bulk delete:', error);
      notifications.error('خطأ عام', 'حدث خطأ أثناء عملية الحذف');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    const confirmed = window.confirm(
      'هل أنت متأكد من حذف جميع البيانات؟\n\nسيتم حذف جميع البيانات من النظام (عدا بيانات الدخول)!\n\nهذا الإجراء لا يمكن التراجع عنه!'
    );

    if (!confirmed) return;

    const doubleConfirmed = window.confirm(
      'تأكيد نهائي!\n\nهذا سيحذف جميع البيانات نهائياً. هل أنت متأكد 100%؟'
    );

    if (!doubleConfirmed) return;

    setLoading(true);
    
    try {
      // حذف جميع الجداول
      const allTableIds = availableTables.map(table => table.id);
      setSelectedTables(allTableIds);
      await handleDeleteSelected();
      
      notifications.success(
        'تم حذف جميع البيانات! 🗑️',
        'تم مسح النظام بالكامل بنجاح'
      );
    } catch (error) {
      console.error('Error in delete all:', error);
      notifications.error('خطأ', 'فشل في حذف جميع البيانات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Trash2 className="w-6 h-6 text-red-600" />
        <div>
          <h1 className="text-2xl font-bold text-neutral-800">حذف البيانات</h1>
          <p className="text-sm text-neutral-600">أداة مؤقتة لحذف البيانات أثناء التطوير والاختبار</p>
        </div>
      </div>

      {/* تحذير */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
          <div>
            <h3 className="text-red-900 font-medium mb-1">تحذير مهم!</h3>
            <p className="text-red-800 text-sm mb-2">
              هذه الأداة مخصصة للتطوير والاختبار فقط. عملية الحذف نهائية ولا يمكن التراجع عنها.
            </p>
            <ul className="text-red-700 text-sm space-y-1">
              <li>• سيتم حذف جميع البيانات من الجداول المحددة</li>
              <li>• لن يتم حذف بيانات الدخول الأساسية</li>
              <li>• تأكد من عمل نسخة احتياطية قبل الحذف</li>
            </ul>
          </div>
        </div>
      </div>

      {/* أزرار التحكم */}
      <div className="flex gap-3">
        <button
          onClick={selectAllTables}
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-900 rounded-lg transition-colors"
        >
          تحديد الكل
        </button>
        <button
          onClick={clearSelection}
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-900 rounded-lg transition-colors"
        >
          إلغاء التحديد
        </button>
        <div className="flex-1"></div>
        <button
          onClick={handleDeleteAll}
          disabled={loading}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg transition-colors"
        >
          {loading ? 'جاري الحذف...' : 'حذف جميع البيانات'}
        </button>
      </div>

      {/* قائمة الجداول */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableTables.map(table => {
          const Icon = table.icon;
          const isSelected = selectedTables.includes(table.id);
          
          return (
            <div
              key={table.id}
              onClick={() => handleTableSelection(table.id)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                isSelected
                  ? `border-${table.color}-500 bg-${table.color}-50`
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start gap-3">
                <Icon className={`w-6 h-6 text-${table.color}-500 mt-1`} />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">{table.name}</h3>
                    {isSelected && (
                      <CheckCircle className={`w-5 h-5 text-${table.color}-500`} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{table.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* زر الحذف */}
      {selectedTables.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900">
                تم تحديد {selectedTables.length} جدول للحذف
              </h3>
              <p className="text-sm text-gray-600">
                الجداول المحددة: {selectedTables.map(id => 
                  availableTables.find(t => t.id === id)?.name
                ).join(', ')}
              </p>
            </div>
            <button
              onClick={handleDeleteSelected}
              disabled={loading}
              className="flex items-center gap-2 px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg transition-colors"
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
              {loading ? 'جاري الحذف...' : 'حذف المحدد'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeleteDataPage;
