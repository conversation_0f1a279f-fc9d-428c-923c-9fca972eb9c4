import { supabase } from './supabase';

export interface AdminCredentials {
  email: string;
  password: string;
}

// بيانات المدير الافتراضي
export const DEFAULT_ADMIN: AdminCredentials = {
  email: '<EMAIL>',
  password: 'Strategic@123'
};

/**
 * إنشاء مستخدم إداري افتراضي
 */
export async function initializeDefaultAdmin(): Promise<boolean> {
  try {
    console.log('🔄 جاري التحقق من وجود المدير الافتراضي...');

    // التحقق من وجود المدير الافتراضي
    const { data: existingUser } = await supabase.auth.admin.listUsers();
    const adminExists = existingUser.users.some(user => user.email === DEFAULT_ADMIN.email);

    if (adminExists) {
      console.log('✅ المدير الافتراضي موجود بالفعل');
      return true;
    }

    console.log('🔄 جاري إنشاء المدير الافتراضي...');

    // إنشاء المستخدم في نظام المصادقة
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: DEFAULT_ADMIN.email,
      password: DEFAULT_ADMIN.password,
      email_confirm: true
    });

    if (authError) {
      console.error('❌ خطأ في إنشاء المستخدم:', authError);
      return false;
    }

    // استخدام المعرفات الموجودة في قاعدة البيانات
    const adminRole = { id: '1a6bfbe0-f9d0-4377-93fd-9411d01ce444' }; // ADMIN role
    const adminDept = { id: 'bcab861a-5a84-443e-b452-2f7ab01f86ae' }; // قسم المالية والإدارة

    // إنشاء ملف المستخدم
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: authUser.user.id,
        full_name: 'مدير النظام',
        role_id: adminRole.id,
        department_id: adminDept.id,
        is_active: true
      });

    if (profileError) {
      console.error('❌ خطأ في إنشاء ملف المستخدم:', profileError);
      return false;
    }

    console.log('✅ تم إنشاء المدير الافتراضي بنجاح');
    console.log(`📧 البريد الإلكتروني: ${DEFAULT_ADMIN.email}`);
    console.log(`🔑 كلمة المرور: ${DEFAULT_ADMIN.password}`);
    
    return true;
  } catch (error) {
    console.error('❌ خطأ عام في إنشاء المدير الافتراضي:', error);
    return false;
  }
}

/**
 * التحقق من صحة بيانات الدخول
 */
export async function validateAdminCredentials(email: string, password: string): Promise<boolean> {
  try {
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (!error) {
      // تسجيل الخروج فوراً بعد التحقق
      await supabase.auth.signOut();
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

/**
 * إعادة تعيين كلمة مرور المدير
 */
export async function resetAdminPassword(newPassword: string): Promise<boolean> {
  try {
    // البحث عن المدير
    const { data: users } = await supabase.auth.admin.listUsers();
    const adminUser = users.users.find(user => user.email === DEFAULT_ADMIN.email);

    if (!adminUser) {
      console.error('❌ لم يتم العثور على المدير');
      return false;
    }

    // تحديث كلمة المرور
    const { error } = await supabase.auth.admin.updateUserById(adminUser.id, {
      password: newPassword
    });

    if (error) {
      console.error('❌ خطأ في تحديث كلمة المرور:', error);
      return false;
    }

    console.log('✅ تم تحديث كلمة مرور المدير بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ عام في تحديث كلمة المرور:', error);
    return false;
  }
}

/**
 * حذف جميع البيانات وإعادة التهيئة
 */
export async function resetSystem(): Promise<boolean> {
  try {
    console.log('🔄 جاري إعادة تعيين النظام...');

    // حذف جميع ملفات المستخدمين
    await supabase.from('user_profiles').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // حذف جميع المستخدمين من المصادقة
    const { data: users } = await supabase.auth.admin.listUsers();
    for (const user of users.users) {
      await supabase.auth.admin.deleteUser(user.id);
    }

    // إعادة إنشاء المدير الافتراضي
    const success = await initializeDefaultAdmin();
    
    if (success) {
      console.log('✅ تم إعادة تعيين النظام بنجاح');
    }
    
    return success;
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين النظام:', error);
    return false;
  }
}
