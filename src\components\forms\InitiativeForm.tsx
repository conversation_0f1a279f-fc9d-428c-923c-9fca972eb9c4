import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Save, X } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface InitiativeFormProps {
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  initialData?: any;
  isEditing?: boolean;
}

const InitiativeForm = ({ onSubmit, onCancel, initialData, isEditing = false }: InitiativeFormProps) => {
  const notifications = useQuickNotifications();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    strategic_objective: '',
    status: 'في التخطيط',
    priority: 'متوسطة',
    start_date: '',
    end_date: '',
    budget: '',
    owner: '',
    manager: '',
    department: '',
    progress: 0,
    kpis: [''],
    activities: [''],
    team: [''],
    risks: [{ risk: '', probability: 'متوسطة', impact: 'متوسط' }],
    deliverables: [{ name: '', status: 'مجدول', date: '' }],
    expected_outcomes: [''],
    success_criteria: ['']
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        description: initialData.description || '',
        strategic_objective: initialData.strategic_objective || '',
        status: initialData.status || 'في التخطيط',
        priority: initialData.priority || 'متوسطة',
        start_date: initialData.start_date || '',
        end_date: initialData.end_date || '',
        budget: initialData.budget || '',
        owner: initialData.owner || '',
        manager: initialData.manager || '',
        department: initialData.department || '',
        progress: initialData.progress || 0,
        kpis: initialData.kpis || [''],
        activities: initialData.activities || [''],
        team: initialData.team || [''],
        risks: initialData.risks || [{ risk: '', probability: 'متوسطة', impact: 'متوسط' }],
        deliverables: initialData.deliverables || [{ name: '', status: 'مجدول', date: '' }],
        expected_outcomes: initialData.expected_outcomes || [''],
        success_criteria: initialData.success_criteria || ['']
      });
    }
  }, [initialData]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان المبادرة مطلوب';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'وصف المبادرة مطلوب';
    }
    if (!formData.strategic_objective.trim()) {
      newErrors.strategic_objective = 'الهدف الاستراتيجي مطلوب';
    }
    if (!formData.owner.trim()) {
      newErrors.owner = 'الجهة المسؤولة مطلوبة';
    }
    if (!formData.manager.trim()) {
      newErrors.manager = 'مدير المبادرة مطلوب';
    }

    // Date validation
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
      newErrors.end_date = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      const dataToSubmit = {
        ...formData,
        id: initialData?.id || `initiative-${Date.now()}`,
        created_at: initialData?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Clean up arrays
        kpis: formData.kpis.filter(k => k.trim()),
        activities: formData.activities.filter(a => a.trim()),
        team: formData.team.filter(t => t.trim()),
        expected_outcomes: formData.expected_outcomes.filter(o => o.trim()),
        success_criteria: formData.success_criteria.filter(c => c.trim()),
        risks: formData.risks.filter(r => r.risk.trim()),
        deliverables: formData.deliverables.filter(d => d.name.trim())
      };

      await onSubmit(dataToSubmit);
      notifications.success(
        isEditing ? 'تم التحديث' : 'تم الإضافة',
        `تم ${isEditing ? 'تحديث' : 'إضافة'} المبادرة بنجاح`
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ المبادرة');
    } finally {
      setLoading(false);
    }
  };

  const addArrayItem = (field: string, defaultValue: any = '') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], defaultValue]
    }));
  };

  const updateArrayItem = (field: string, index: number, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item: any, i: number) => i === index ? value : item)
    }));
  };

  const removeArrayItem = (field: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_: any, i: number) => i !== index)
    }));
  };

  const inputClass = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const errorClass = "text-red-500 text-sm mt-1";

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      <form onSubmit={handleSubmit}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              {isEditing ? 'تعديل المبادرة' : 'إضافة مبادرة جديدة'}
            </h2>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium"
                disabled={loading}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center gap-2 disabled:opacity-50"
                disabled={loading}
              >
                <Save size={16} />
                {loading ? 'جاري الحفظ...' : isEditing ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="px-6 py-4 max-h-[calc(100vh-200px)] overflow-y-auto space-y-6">
          {/* المعلومات الأساسية */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className={labelClass}>عنوان المبادرة *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className={`${inputClass} ${errors.title ? 'border-red-500' : ''}`}
                  placeholder="أدخل عنوان المبادرة"
                />
                {errors.title && <p className={errorClass}>{errors.title}</p>}
              </div>

              <div className="md:col-span-2">
                <label className={labelClass}>الوصف *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className={`${inputClass} ${errors.description ? 'border-red-500' : ''}`}
                  placeholder="وصف تفصيلي للمبادرة"
                />
                {errors.description && <p className={errorClass}>{errors.description}</p>}
              </div>

              <div>
                <label className={labelClass}>الهدف الاستراتيجي *</label>
                <input
                  type="text"
                  value={formData.strategic_objective}
                  onChange={(e) => setFormData({ ...formData, strategic_objective: e.target.value })}
                  className={`${inputClass} ${errors.strategic_objective ? 'border-red-500' : ''}`}
                  placeholder="الهدف الاستراتيجي المرتبط"
                />
                {errors.strategic_objective && <p className={errorClass}>{errors.strategic_objective}</p>}
              </div>

              <div>
                <label className={labelClass}>الحالة</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className={inputClass}
                >
                  <option value="في التخطيط">في التخطيط</option>
                  <option value="جاري التنفيذ">جاري التنفيذ</option>
                  <option value="مكتمل">مكتمل</option>
                  <option value="معلق">معلق</option>
                  <option value="ملغي">ملغي</option>
                </select>
              </div>

              <div>
                <label className={labelClass}>الأولوية</label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                  className={inputClass}
                >
                  <option value="عالية">عالية</option>
                  <option value="متوسطة">متوسطة</option>
                  <option value="منخفضة">منخفضة</option>
                </select>
              </div>

              <div>
                <label className={labelClass}>نسبة الإنجاز</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={formData.progress}
                  onChange={(e) => setFormData({ ...formData, progress: parseInt(e.target.value) || 0 })}
                  className={inputClass}
                />
              </div>
            </div>
          </div>

          {/* الإدارة والمسؤوليات */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الإدارة والمسؤوليات</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className={labelClass}>الجهة المسؤولة *</label>
                <input
                  type="text"
                  value={formData.owner}
                  onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                  className={`${inputClass} ${errors.owner ? 'border-red-500' : ''}`}
                  placeholder="الجهة أو الإدارة المسؤولة"
                />
                {errors.owner && <p className={errorClass}>{errors.owner}</p>}
              </div>

              <div>
                <label className={labelClass}>مدير المبادرة *</label>
                <input
                  type="text"
                  value={formData.manager}
                  onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                  className={`${inputClass} ${errors.manager ? 'border-red-500' : ''}`}
                  placeholder="اسم مدير المبادرة"
                />
                {errors.manager && <p className={errorClass}>{errors.manager}</p>}
              </div>

              <div>
                <label className={labelClass}>القسم</label>
                <input
                  type="text"
                  value={formData.department}
                  onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                  className={inputClass}
                  placeholder="القسم أو الإدارة"
                />
              </div>

              <div>
                <label className={labelClass}>الميزانية</label>
                <input
                  type="text"
                  value={formData.budget}
                  onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                  className={inputClass}
                  placeholder="مثال: 500,000 ريال"
                />
              </div>
            </div>
          </div>

          {/* الجدول الزمني */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الجدول الزمني</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className={labelClass}>تاريخ البداية</label>
                <input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                  className={inputClass}
                />
              </div>

              <div>
                <label className={labelClass}>تاريخ النهاية</label>
                <input
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                  className={`${inputClass} ${errors.end_date ? 'border-red-500' : ''}`}
                />
                {errors.end_date && <p className={errorClass}>{errors.end_date}</p>}
              </div>
            </div>
          </div>

          {/* فريق العمل */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">فريق العمل</h3>
              <button
                type="button"
                onClick={() => addArrayItem('team')}
                className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Plus size={16} />
                إضافة عضو
              </button>
            </div>
            
            {formData.team.map((member, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={member}
                  onChange={(e) => updateArrayItem('team', index, e.target.value)}
                  placeholder="اسم عضو الفريق"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                />
                {formData.team.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeArrayItem('team', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* مؤشرات الأداء المرتبطة */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">مؤشرات الأداء المرتبطة</h3>
              <button
                type="button"
                onClick={() => addArrayItem('kpis')}
                className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Plus size={16} />
                إضافة مؤشر
              </button>
            </div>
            
            {formData.kpis.map((kpi, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={kpi}
                  onChange={(e) => updateArrayItem('kpis', index, e.target.value)}
                  placeholder="اسم مؤشر الأداء"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                />
                {formData.kpis.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeArrayItem('kpis', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* النتائج المتوقعة */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">النتائج المتوقعة</h3>
              <button
                type="button"
                onClick={() => addArrayItem('expected_outcomes')}
                className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Plus size={16} />
                إضافة نتيجة
              </button>
            </div>
            
            {formData.expected_outcomes.map((outcome, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={outcome}
                  onChange={(e) => updateArrayItem('expected_outcomes', index, e.target.value)}
                  placeholder="النتيجة المتوقعة"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                />
                {formData.expected_outcomes.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeArrayItem('expected_outcomes', index)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      </form>
    </div>
  );
};

export default InitiativeForm;