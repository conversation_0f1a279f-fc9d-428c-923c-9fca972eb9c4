import { supabase } from './supabase';
import type { Database } from './database.types';

type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];
type Role = Database['public']['Tables']['roles']['Row'];
type Department = Database['public']['Tables']['departments']['Row'];

export interface ExtendedUserProfile extends UserProfile {
  role?: Role;
  department?: Department;
  email?: string;
}

export class UserService {
  // جلب جميع المستخدمين من جدول users الموجود
  static async getAllUsers(): Promise<ExtendedUserProfile[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*');

      if (error) throw error;

      // تحويل البيانات إلى الشكل المطلوب
      const transformedUsers = (data || []).map(user => ({
        id: user.id,
        full_name: user.name || '',
        name: user.name || '', // إضافة name للتوافق
        email: user.email || '',
        phone: user.phone || '',
        role_id: null,
        department_id: null,
        is_active: user.status === 'نشط',
        created_at: user.created_at,
        updated_at: user.updated_at,
        // تحويل الكائنات إلى نصوص
        role: user.role || '',
        department: user.department || '',
        position: user.position || '',
        permissions: user.permissions || [],
        status: user.status || 'نشط',
        lastLogin: user.updated_at || user.created_at,
        createdAt: user.created_at
      }));

      return transformedUsers;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  // إنشاء مستخدم جديد مع دعم المصادقة
  static async createUser(userData: {
    email: string;
    password: string;
    full_name: string;
    role?: string;
    department?: string;
    position?: string;
    phone?: string;
    createAuthAccount?: boolean; // خيار لإنشاء حساب مصادقة
  }): Promise<any> {
    try {
      let authUserId = null;

      // إنشاء حساب مصادقة إذا طُلب ذلك
      if (userData.createAuthAccount) {
        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true
        });

        if (authError) {
          console.error('Error creating auth user:', authError);
          throw new Error(`فشل في إنشاء حساب المصادقة: ${authError.message}`);
        }

        authUserId = authUser.user.id;
      }

      // إنشاء المستخدم في جدول users
      const { data: user, error: userError } = await supabase
        .from('users')
        .insert({
          auth_id: authUserId, // ربط بحساب المصادقة
          name: userData.full_name,
          email: userData.email,
          phone: userData.phone || '',
          role: userData.role || 'موظف',
          department: userData.department || '',
          position: userData.position || '',
          status: 'نشط',
          permissions: ['قراءة'],
          has_system_account: !!userData.createAuthAccount,
          is_system_user: !!userData.createAuthAccount
        })
        .select()
        .single();

      if (userError) throw userError;

      return user;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // تحديث بيانات المستخدم
  static async updateUser(userId: string, updates: any): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // حذف مستخدم
  static async deleteUser(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // جلب الأدوار
  static async getRoles(): Promise<Role[]> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  }

  // جلب الأقسام
  static async getDepartments(): Promise<Department[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  // تغيير حالة المستخدم (تفعيل/إلغاء تفعيل)
  static async toggleUserStatus(userId: string, isActive: boolean): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          status: isActive ? 'نشط' : 'غير نشط',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }

  // تغيير كلمة مرور المستخدم
  static async updateUserPassword(userId: string, newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: newPassword
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error updating user password:', error);
      throw error;
    }
  }

  // جلب أسماء المستخدمين فقط (للقوائم المنسدلة)
  static async getUserNames(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('name')
        .eq('status', 'نشط')
        .order('name');

      if (error) throw error;
      return data?.map(user => user.name) || [];
    } catch (error) {
      console.error('Error fetching user names:', error);
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'مدير النظام',
        'مدير التطوير',
        'مدير المشاريع',
        'مدير التسويق',
        'مدير العمليات'
      ];
    }
  }

  // جلب أسماء المديرين فقط
  static async getManagerNames(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('name')
        .in('role', ['مدير', 'مدير قسم', 'مدير عام'])
        .eq('status', 'نشط')
        .order('name');

      if (error) throw error;
      return data?.map(user => user.name) || [];
    } catch (error) {
      console.error('Error fetching manager names:', error);
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'مدير النظام',
        'مدير التطوير',
        'مدير المشاريع',
        'مدير التسويق',
        'مدير العمليات'
      ];
    }
  }

  // جلب المديرين مع جميع البيانات
  static async getManagers(): Promise<User[]> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .in('role', ['مدير', 'مدير قسم', 'مدير عام'])
        .eq('status', 'نشط')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching managers:', error);
      throw error;
    }
  }

  // جلب أسماء الإدارات من جدول departments
  static async getDepartmentNames(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('name')
        .eq('status', 'نشط')
        .order('name');

      if (error) throw error;
      return data?.map(dept => dept.name) || [];
    } catch (error) {
      console.error('Error fetching department names:', error);
      // إرجاع قائمة افتراضية في حالة الخطأ
      return [
        'قسم البحث والابتكار',
        'قسم التسويق والمبيعات',
        'قسم التطوير والهندسة2',
        'قسم العمليات وخدمة العملاء1',
        'قسم المالية والإدارة'
      ];
    }
  }

  // جلب بيانات المستخدم الحالي
  static async getCurrentUserProfile(): Promise<ExtendedUserProfile | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) return null;

      // البحث في جدول users بـ auth_id أولاً (الطريقة الجديدة)
      let { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', user.id)
        .single();

      // إذا لم يوجد بـ auth_id، ابحث بالبريد الإلكتروني (للتوافق مع البيانات القديمة)
      if (error || !data) {
        console.log('User not found by auth_id, trying email...');
        const { data: emailData, error: emailError } = await supabase
          .from('users')
          .select('*')
          .eq('email', user.email)
          .single();

        if (emailError || !emailData) {
          console.log('User not found in users table, returning basic info');
          return {
            id: user.id,
            full_name: user.email?.split('@')[0] || 'مستخدم',
            email: user.email || '',
            is_active: true,
            created_at: user.created_at,
            updated_at: user.updated_at
          };
        }

        data = emailData;

        // تحديث auth_id للمستخدم الموجود (ترقية تدريجية)
        await supabase
          .from('users')
          .update({ auth_id: user.id })
          .eq('id', data.id);
      }

      return {
        id: data.id,
        full_name: data.name,
        name: data.name,
        email: data.email || user.email,
        phone: data.phone || '',
        role: data.role || '',
        department: data.department || '',
        position: data.position || '',
        is_active: data.status === 'نشط',
        created_at: data.created_at,
        updated_at: data.updated_at,
        auth_id: data.auth_id || user.id
      };
    } catch (error) {
      console.error('Error fetching current user profile:', error);
      return null;
    }
  }
}
