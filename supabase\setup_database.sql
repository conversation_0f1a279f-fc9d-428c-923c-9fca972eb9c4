-- إعد<PERSON> قاعدة البيانات الكاملة لنظام التخطيط الاستراتيجي
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- تفعيل Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- إن<PERSON><PERSON>ء جدول الأدوار
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- إنشاء جدول الأقسام
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    manager_id UUID,
    parent_id UUID REFERENCES public.departments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إنشاء جدول ملفات المستخدمين
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name VARCHAR(200) NOT NULL,
    role_id UUID REFERENCES public.roles(id),
    department_id UUID REFERENCES public.departments(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إنشاء جدول الأهداف الاستراتيجية
CREATE TABLE IF NOT EXISTS public.strategic_objectives (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    target_date DATE,
    status VARCHAR(50) DEFAULT 'قيد التنفيذ',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    responsible_department_id UUID REFERENCES public.departments(id),
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إنشاء جدول مؤشرات الأداء
CREATE TABLE IF NOT EXISTS public.kpis (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(300) NOT NULL,
    description TEXT,
    target_value DECIMAL(15,2),
    current_value DECIMAL(15,2) DEFAULT 0,
    unit VARCHAR(50),
    measurement_frequency VARCHAR(50),
    objective_id UUID REFERENCES public.strategic_objectives(id) ON DELETE CASCADE,
    responsible_user_id UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إنشاء جدول المشاريع
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(300) NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'مخطط',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    objective_id UUID REFERENCES public.strategic_objectives(id),
    manager_id UUID REFERENCES public.user_profiles(id),
    department_id UUID REFERENCES public.departments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إنشاء جدول الأنشطة
CREATE TABLE IF NOT EXISTS public.activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(300) NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'مخطط',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    project_id UUID REFERENCES public.projects(id),
    assigned_to UUID REFERENCES public.user_profiles(id),
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- إدراج البيانات الأساسية

-- إدراج الأدوار الافتراضية
INSERT INTO public.roles (name, description) VALUES
('ADMIN', 'مدير النظام مع صلاحيات كاملة'),
('MANAGER', 'مدير إدارة'),
('SUPERVISOR', 'مشرف'),
('EMPLOYEE', 'موظف')
ON CONFLICT (name) DO NOTHING;

-- إدراج الأقسام الافتراضية
INSERT INTO public.departments (name, description) VALUES
('الإدارة العامة', 'قسم الإدارة العامة للنظام'),
('إدارة التطوير والابتكار', 'قسم التطوير والابتكار'),
('إدارة ريادة الأعمال', 'قسم ريادة الأعمال'),
('إدارة البرامج الشبابية', 'قسم البرامج الشبابية'),
('الإدارة الثقافية', 'قسم الشؤون الثقافية'),
('إدارة المرافق', 'قسم إدارة المرافق')
ON CONFLICT DO NOTHING;

-- تفعيل Row Level Security
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.strategic_objectives ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان (RLS Policies)

-- سياسات جدول الأدوار
CREATE POLICY "Enable read access for all users" ON public.roles FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.roles FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users only" ON public.roles FOR UPDATE USING (auth.role() = 'authenticated');

-- سياسات جدول الأقسام
CREATE POLICY "Enable read access for all users" ON public.departments FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.departments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users only" ON public.departments FOR UPDATE USING (auth.role() = 'authenticated');

-- سياسات جدول ملفات المستخدمين
CREATE POLICY "Users can view own profile" ON public.user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Enable insert for authenticated users only" ON public.user_profiles FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- سياسات الأهداف الاستراتيجية
CREATE POLICY "Enable read access for authenticated users" ON public.strategic_objectives FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.strategic_objectives FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.strategic_objectives FOR UPDATE USING (auth.role() = 'authenticated');

-- سياسات مؤشرات الأداء
CREATE POLICY "Enable read access for authenticated users" ON public.kpis FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.kpis FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.kpis FOR UPDATE USING (auth.role() = 'authenticated');

-- سياسات المشاريع
CREATE POLICY "Enable read access for authenticated users" ON public.projects FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.projects FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.projects FOR UPDATE USING (auth.role() = 'authenticated');

-- سياسات الأنشطة
CREATE POLICY "Enable read access for authenticated users" ON public.activities FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.activities FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.activities FOR UPDATE USING (auth.role() = 'authenticated');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_profiles_role_id ON public.user_profiles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_department_id ON public.user_profiles(department_id);
CREATE INDEX IF NOT EXISTS idx_strategic_objectives_department_id ON public.strategic_objectives(responsible_department_id);
CREATE INDEX IF NOT EXISTS idx_kpis_objective_id ON public.kpis(objective_id);
CREATE INDEX IF NOT EXISTS idx_projects_objective_id ON public.projects(objective_id);
CREATE INDEX IF NOT EXISTS idx_activities_project_id ON public.activities(project_id);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء المحفزات (Triggers) لتحديث updated_at
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON public.departments FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_strategic_objectives_updated_at BEFORE UPDATE ON public.strategic_objectives FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_kpis_updated_at BEFORE UPDATE ON public.kpis FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON public.activities FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
