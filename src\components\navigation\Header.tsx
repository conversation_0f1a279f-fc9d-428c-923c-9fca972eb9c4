import { Bell, User, LogOut, Menu } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface HeaderProps {
  onMenuClick: () => void;
}

const Header = ({ onMenuClick }: HeaderProps) => {
  const { user, signOut } = useAuth();
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <header className="h-14 border-b border-neutral-200 bg-white flex items-center px-4 lg:px-6 shadow-sm">
      <button
        onClick={onMenuClick}
        className="p-2 rounded-lg text-neutral-600 hover:bg-neutral-100 lg:hidden transition-colors"
      >
        <Menu className="w-4 h-4" />
      </button>

      <div className="flex-1"></div>

      <div className="flex items-center gap-1">
        {/* Notifications */}
        <button className="p-2 rounded-lg text-neutral-600 hover:bg-neutral-100 relative transition-colors">
          <Bell className="w-4 h-4" />
          <span className="absolute top-1 right-1 bg-error-500 w-2 h-2 rounded-full"></span>
        </button>
        
        {/* User menu */}
        <div className="relative" ref={userMenuRef}>
          <button
            onClick={() => setUserMenuOpen(!userMenuOpen)}
            className="flex items-center gap-2 p-2 rounded-lg text-neutral-600 hover:bg-neutral-100 transition-colors"
          >
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-primary-100 text-primary-700">
              <User className="w-4 h-4" />
            </div>
            <span className="hidden md:inline-block font-medium text-sm">
              {user?.email?.split('@')[0]}
            </span>
          </button>

          {userMenuOpen && (
            <div className="absolute left-0 top-full mt-2 w-44 bg-white rounded-lg shadow-lg border border-neutral-200 py-1 z-10">
              <button
                onClick={() => signOut()}
                className="w-full text-right px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-50 flex items-center gap-2 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span>تسجيل الخروج</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;