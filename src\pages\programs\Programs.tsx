import { useState, useEffect } from 'react';
import { Lightbulb, Plus, Search, Filter, Target, Users, Calendar, DollarSign, ChevronDown, ChevronUp, Upload, Edit, Trash2 } from 'lucide-react';
import DataImporter from '../../components/import/DataImporter';
import ProgramForm from '../../components/forms/ProgramForm';
import { useNotifications } from '../../components/ui/NotificationSystem';

const Programs = () => {
  const [loading, setLoading] = useState(true);
  const [expandedProgram, setExpandedProgram] = useState<string | null>(null);
  const [showImporter, setShowImporter] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingProgram, setEditingProgram] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const notifications = useNotifications();
  
  // Mock data - in a real application, we would fetch from Supabase
  const [programs, setPrograms] = useState([
    {
      id: '1',
      title: 'برنامج تطوير المهارات الرقمية',
      description: 'برنامج شامل لتطوير المهارات التقنية والرقمية للشباب',
      objective: 'تطوير مهارات الشباب التقنية والرقمية',
      department: 'إدارة التطوير والابتكار',
      manager: 'أحمد محمد علي',
      progress: 75,
      status: 'جاري التنفيذ',
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      budget: 2500000,
      participants: 500,
      projects: [
        'مشروع تدريب البرمجة',
        'مشروع تطوير التطبيقات',
        'مشروع الذكاء الاصطناعي'
      ],
      kpis: [
        'عدد المتدربين المتخرجين',
        'نسبة النجاح في الاختبارات',
        'معدل رضا المتدربين'
      ]
    },
    {
      id: '2',
      title: 'برنامج دعم ريادة الأعمال',
      description: 'برنامج لدعم رواد الأعمال الشباب وتوفير البيئة المناسبة لنمو المشاريع',
      objective: 'تمكين رواد الأعمال الشباب',
      department: 'إدارة ريادة الأعمال',
      manager: 'فاطمة أحمد الزهراني',
      progress: 60,
      status: 'جاري التنفيذ',
      startDate: '2025-02-01',
      endDate: '2025-11-30',
      budget: 3200000,
      participants: 200,
      projects: [
        'مشروع الحاضنات التقنية',
        'مشروع التمويل الأولي',
        'مشروع التوجيه والإرشاد'
      ],
      kpis: [
        'عدد الشركات الناشئة',
        'معدل نجاح المشاريع',
        'حجم الاستثمارات المجذوبة'
      ]
    },
    {
      id: '3',
      title: 'برنامج التطوع المجتمعي',
      description: 'برنامج لتعزيز مشاركة الشباب في خدمة المجتمع والعمل التطوعي',
      objective: 'تعزيز المشاركة المجتمعية للشباب',
      department: 'إدارة البرامج الشبابية',
      manager: 'محمد علي القحطاني',
      progress: 45,
      status: 'متأخر',
      startDate: '2025-01-15',
      endDate: '2025-10-15',
      budget: 800000,
      participants: 1000,
      projects: [
        'مشروع تنظيف البيئة',
        'مشروع مساعدة كبار السن',
        'مشروع التوعية المجتمعية'
      ],
      kpis: [
        'عدد المتطوعين النشطين',
        'ساعات العمل التطوعي',
        'عدد المستفيدين من الخدمات'
      ]
    },
    {
      id: '4',
      title: 'برنامج الثقافة والتراث',
      description: 'برنامج لتعزيز الهوية الوطنية والحفاظ على التراث الثقافي',
      objective: 'تعزيز الهوية الوطنية والقيم المجتمعية',
      department: 'الإدارة الثقافية',
      manager: 'سارة خالد العتيبي',
      progress: 30,
      status: 'متأخر',
      startDate: '2025-03-01',
      endDate: '2025-12-31',
      budget: 1500000,
      participants: 800,
      projects: [
        'مشروع المهرجان الثقافي',
        'مشروع توثيق التراث',
        'مشروع الفنون الشعبية'
      ],
      kpis: [
        'عدد الفعاليات الثقافية',
        'نسبة مشاركة الشباب',
        'مستوى الوعي الثقافي'
      ]
    }
  ]);
  
  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // دالة استيراد البرامج
  const handleImportPrograms = async (data: any[]) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const successCount = data.length;
      const errors: any[] = [];

      const newPrograms = data.map((item, index) => ({
        id: `imported-${Date.now()}-${index}`,
        title: item.title || `برنامج مستورد ${index + 1}`,
        description: item.description || 'وصف البرنامج',
        objective: item.objective || 'هدف عام',
        department: item.department || 'إدارة عامة',
        manager: item.manager || 'غير محدد',
        status: item.status || 'جديد',
        progress: parseInt(item.progress) || 0,
        startDate: item.startDate || new Date().toISOString().split('T')[0],
        endDate: item.endDate || new Date().toISOString().split('T')[0],
        budget: parseInt(item.budget) || 0,
        participants: parseInt(item.participants) || 1,
        projects: item.projects ? item.projects.split(',') : ['مشروع افتراضي'],
        kpis: item.kpis ? item.kpis.split(',') : ['مؤشر افتراضي']
      }));

      setPrograms(prev => [...prev, ...newPrograms]);

      return {
        success: true,
        totalRows: data.length,
        successCount,
        errorCount: errors.length,
        errors
      };
    } catch (error) {
      return {
        success: false,
        totalRows: data.length,
        successCount: 0,
        errorCount: data.length,
        errors: [{ row: 1, message: 'فشل في الاستيراد' }]
      };
    }
  };

  // دالة حفظ البرنامج (إضافة أو تعديل)
  const handleSaveProgram = async (programData: any) => {
    try {
      if (editingProgram) {
        // تعديل برنامج موجود
        setPrograms(prev =>
          prev.map(program =>
            program.id === editingProgram.id
              ? { ...programData, id: editingProgram.id }
              : program
          )
        );
      } else {
        // إضافة برنامج جديد
        setPrograms(prev => [...prev, programData]);
      }

      setShowForm(false);
      setEditingProgram(null);
    } catch (error) {
      throw error;
    }
  };

  // دالة تعديل البرنامج
  const handleEditProgram = (program: any) => {
    setEditingProgram(program);
    setShowForm(true);
  };

  // دالة حذف البرنامج
  const handleDeleteProgram = (programId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا البرنامج؟')) {
      setPrograms(prev => prev.filter(program => program.id !== programId));
      notifications.success('تم الحذف', 'تم حذف البرنامج بنجاح');
    }
  };

  // دالة إغلاق النموذج
  const handleCloseForm = () => {
    setShowForm(false);
    setEditingProgram(null);
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-success-100 text-success-800';
      case 'جاري التنفيذ':
        return 'bg-primary-100 text-primary-800';
      case 'متأخر':
        return 'bg-warning-100 text-warning-800';
      case 'متعثر':
        return 'bg-error-100 text-error-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-neutral-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-5">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
        <div className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-primary-600" />
          <h1 className="text-xl font-bold text-neutral-800">البرامج والمشاريع</h1>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => setShowForm(true)}
            className="btn btn-primary flex items-center gap-2 text-sm"
          >
            <Plus className="w-4 h-4" />
            <span>إضافة برنامج</span>
          </button>

          <button
            onClick={() => setShowImporter(true)}
            className="btn btn-outline flex items-center gap-2 text-sm"
          >
            <Upload className="w-4 h-4" />
            <span>استيراد</span>
          </button>
        </div>
      </div>
      
      {/* Filters */}
      <div className="card">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 w-5 h-5" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pr-10"
              placeholder="بحث عن برنامج..."
            />
          </div>

          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field min-w-[120px]"
            >
              <option value="">جميع الحالات</option>
              <option value="مكتمل">مكتمل</option>
              <option value="جاري التنفيذ">جاري التنفيذ</option>
              <option value="متوقف">متوقف</option>
              <option value="ملغي">ملغي</option>
              <option value="مجدول">مجدول</option>
            </select>
          </div>

          <div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="input-field min-w-[120px]"
            >
              <option value="">جميع الأنواع</option>
              <option value="برنامج">برنامج</option>
              <option value="مشروع">مشروع</option>
              <option value="مبادرة">مبادرة</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Programs List */}
      <div className="space-y-3">
        {programs.filter(program => {
          const matchesSearch = !searchTerm || 
                               program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               program.description.toLowerCase().includes(searchTerm.toLowerCase());
          const matchesStatus = !statusFilter || program.status === statusFilter;
          const matchesType = !typeFilter || program.type === typeFilter;
          return matchesSearch && matchesStatus && matchesType;
        }).map((program) => {
          const isExpanded = expandedProgram === program.id;
          
          return (
            <div key={program.id} className="card hover:shadow-md transition-all duration-200 cursor-pointer">
              {/* Compact View */}
              <div 
                className="flex items-center justify-between"
                onClick={() => setExpandedProgram(isExpanded ? null : program.id)}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-sm text-neutral-800 truncate">
                      {program.title}
                    </h3>
                  </div>
                  
                  <div className="flex items-center gap-4 text-xs text-neutral-600">
                    <span className="flex items-center gap-1">
                      <Target className="w-3 h-3" />
                      {program.objective}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {program.participants} مستفيد
                    </span>
                    <span className="hidden sm:inline flex items-center gap-1">
                      <DollarSign className="w-3 h-3" />
                      {program.budget.toLocaleString('ar-SA')} ريال
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 shrink-0 mr-2">
                  <span className={`badge text-xs ${getStatusColor(program.status)}`}>
                    {program.status}
                  </span>
                  
                  <div className="w-12 text-center">
                    <div className="text-xs font-medium text-primary-600">{program.progress}%</div>
                  </div>
                  
                  <button className="p-1 hover:bg-neutral-100 rounded">
                    {isExpanded ? (
                      <ChevronUp className="w-4 h-4 text-neutral-500" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-neutral-500" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="mt-2">
                <div className="progress-bar">
                  <div 
                    className={`progress-value ${
                      program.progress >= 70 
                        ? 'bg-success-500' 
                        : program.progress >= 40 
                          ? 'bg-primary-500' 
                          : 'bg-warning-500'
                    }`} 
                    style={{ width: `${program.progress}%` }}
                  ></div>
                </div>
              </div>
              
              {/* Expanded Details */}
              {isExpanded && (
                <div className="mt-3 pt-3 border-t border-neutral-100 space-y-3">
                  <p className="text-sm text-neutral-600">{program.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-medium text-neutral-800 mb-2">المشاريع المرتبطة:</h4>
                      <ul className="space-y-1">
                        {program.projects.map((project, index) => (
                          <li key={index} className="text-neutral-600 text-xs">
                            • {project}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-neutral-800 mb-2">مؤشرات الأداء:</h4>
                      <ul className="space-y-1">
                        {program.kpis.map((kpi, index) => (
                          <li key={index} className="text-neutral-600 text-xs">
                            • {kpi}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between pt-2 border-t border-neutral-100 text-xs text-neutral-600">
                    <div>
                      <span className="font-medium">{program.manager}</span>
                      <span className="mx-1">•</span>
                      <span>{program.department}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>
                          {new Date(program.startDate).toLocaleDateString('ar-SA')} -
                          {new Date(program.endDate).toLocaleDateString('ar-SA')}
                        </span>
                      </div>

                      <div className="flex gap-1 mr-2">
                        <button
                          onClick={() => handleEditProgram(program)}
                          className="p-1 text-primary-600 hover:bg-primary-50 rounded transition-colors"
                          title="تعديل البرنامج"
                        >
                          <Edit className="w-3 h-3" />
                        </button>

                        <button
                          onClick={() => handleDeleteProgram(program.id)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                          title="حذف البرنامج"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* مكون الاستيراد */}
      <DataImporter
        title="استيراد البرامج والمشاريع"
        description="يمكنك استيراد البرامج من ملف Excel أو CSV. تأكد من أن الملف يحتوي على الحقول المطلوبة."
        requiredFields={['title', 'description', 'objective', 'department', 'manager', 'startDate', 'endDate']}
        onImport={handleImportPrograms}
        isOpen={showImporter}
        onClose={() => setShowImporter(false)}
      />

      {/* نموذج إضافة/تعديل البرنامج */}
      <ProgramForm
        isOpen={showForm}
        onClose={handleCloseForm}
        onSave={handleSaveProgram}
        editData={editingProgram}
      />
    </div>
  );
};

export default Programs;
