import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>hart, Plus, Search, Filter, TrendingUp, TrendingDown, Target, AlertTriangle, Edit, Trash2, Shield, User<PERSON>heck, Eye, Sun, Moon, Save, Loader2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../lib/database.types';

type KPI = Database['public']['Tables']['kpis']['Row'];
type KPIInsert = Database['public']['Tables']['kpis']['Insert'];

// مكون النموذج المدمج
const InlineKpiForm = ({ editData, onSave, onCancel, onDataChange }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    category: editData?.category || '',
    objective: editData?.objective || '',
    relatedProject: editData?.relatedProject || '',
    currentValue: editData?.currentValue || 0,
    targetValue: editData?.targetValue || 0,
    unit: editData?.unit || '',
    trend: editData?.trend || 'up',
    change: editData?.change || 0,
    status: editData?.status || 'متوسط',
    description: editData?.description || '',
    frequency: editData?.frequency || 'شهري',
    dataSource: editData?.dataSource || '',
    formula: editData?.formula || '',
    responsible: editData?.responsible || ''
  });

  const [errors, setErrors] = useState({});
  
  // تحديث البيانات عند تغيير editData
  useEffect(() => {
    if (editData) {
      setFormData({
        name: editData?.name || '',
        category: editData?.category || '',
        objective: editData?.objective || '',
        relatedProject: editData?.relatedProject || '',
        currentValue: editData?.currentValue || 0,
        targetValue: editData?.targetValue || 0,
        unit: editData?.unit || '',
        trend: editData?.trend || 'up',
        change: editData?.change || 0,
        status: editData?.status || 'متوسط',
        description: editData?.description || '',
        frequency: editData?.frequency || 'شهري',
        dataSource: editData?.dataSource || '',
        formula: editData?.formula || '',
        responsible: editData?.responsible || ''
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المؤشر مطلوب';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'تصنيف المؤشر مطلوب';
    }

    if (!formData.unit.trim()) {
      newErrors.unit = 'وحدة القياس مطلوبة';
    }

    if (!formData.targetValue || formData.targetValue <= 0) {
      newErrors.targetValue = 'القيمة المستهدفة مطلوبة ويجب أن تكون أكبر من صفر';
    }

    if (!formData.responsible.trim()) {
      newErrors.responsible = 'المسؤول عن المؤشر مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        currentValue: parseFloat(formData.currentValue),
        targetValue: parseFloat(formData.targetValue),
        change: parseFloat(formData.change),
        id: editData?.id || `kpi-${Date.now()}`,
        lastUpdated: new Date().toISOString().split('T')[0],
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث المؤشر' : 'تم إضافة المؤشر', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    // إشعار المكون الأب بوجود تغييرات غير محفوظة
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* أزرار التحكم في الأعلى */}
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ المؤشر'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      {/* محتوى النموذج */}
      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* الاسم */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              اسم المؤشر *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل اسم المؤشر"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف المؤشر
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="وصف تفصيلي للمؤشر"
            />
          </div>

          {/* التصنيف والمشاريع المرتبطة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تصنيف المؤشر *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.category ? 'border-red-500' : ''}`}
              >
                <option value="">اختر التصنيف</option>
                <option value="جودة الخدمة">جودة الخدمة</option>
                <option value="الأداء التشغيلي">الأداء التشغيلي</option>
                <option value="النمو">النمو</option>
                <option value="المشاركة">المشاركة</option>
                <option value="التطوير">التطوير</option>
                <option value="الكفاءة">الكفاءة</option>
                <option value="المالية">المالية</option>
              </select>
              {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                المشروع المرتبط
              </label>
              <select
                value={formData.relatedProject || ''}
                onChange={(e) => handleChange('relatedProject', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="">اختر المشروع المرتبط</option>
                <option value="مشروع تطوير المهارات الرقمية للشباب">مشروع تطوير المهارات الرقمية للشباب</option>
                <option value="مشروع دعم رواد الأعمال الشباب">مشروع دعم رواد الأعمال الشباب</option>
                <option value="مشروع التطوع المجتمعي الشبابي">مشروع التطوع المجتمعي الشبابي</option>
                <option value="مشروع تعزيز الهوية الوطنية والتراث">مشروع تعزيز الهوية الوطنية والتراث</option>
              </select>
            </div>
          </div>

          {/* القيم والوحدة */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                القيمة الحالية
              </label>
              <input
                type="number"
                value={formData.currentValue}
                onChange={(e) => handleChange('currentValue', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                القيمة المستهدفة *
              </label>
              <input
                type="number"
                value={formData.targetValue}
                onChange={(e) => handleChange('targetValue', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.targetValue ? 'border-red-500' : ''}`}
                placeholder="0"
                min="0"
                step="0.01"
              />
              {errors.targetValue && <p className="text-red-500 text-xs mt-1">{errors.targetValue}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                وحدة القياس *
              </label>
              <input
                type="text"
                value={formData.unit}
                onChange={(e) => handleChange('unit', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.unit ? 'border-red-500' : ''}`}
                placeholder="مثل: %, عدد, نقطة"
              />
              {errors.unit && <p className="text-red-500 text-xs mt-1">{errors.unit}</p>}
            </div>
          </div>

          {/* الحالة والاتجاه */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                حالة المؤشر
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="ممتاز">ممتاز</option>
                <option value="جيد">جيد</option>
                <option value="متوسط">متوسط</option>
                <option value="يحتاج تحسين">يحتاج تحسين</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                اتجاه المؤشر
              </label>
              <select
                value={formData.trend}
                onChange={(e) => handleChange('trend', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="up">متزايد</option>
                <option value="down">متناقص</option>
                <option value="stable">ثابت</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                نسبة التغيير (%)
              </label>
              <input
                type="number"
                value={formData.change}
                onChange={(e) => handleChange('change', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                step="0.1"
              />
            </div>
          </div>

          {/* التكرار والمسؤول */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تكرار القياس
              </label>
              <select
                value={formData.frequency}
                onChange={(e) => handleChange('frequency', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="يومي">يومي</option>
                <option value="أسبوعي">أسبوعي</option>
                <option value="شهري">شهري</option>
                <option value="ربع سنوي">ربع سنوي</option>
                <option value="سنوي">سنوي</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                المسؤول عن المؤشر *
              </label>
              <input
                type="text"
                value={formData.responsible}
                onChange={(e) => handleChange('responsible', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.responsible ? 'border-red-500' : ''}`}
                placeholder="اسم المسؤول أو القسم"
              />
              {errors.responsible && <p className="text-red-500 text-xs mt-1">{errors.responsible}</p>}
            </div>
          </div>

          {/* مصدر البيانات */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              مصدر البيانات
            </label>
            <input
              type="text"
              value={formData.dataSource}
              onChange={(e) => handleChange('dataSource', e.target.value)}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="مثل: النظام المالي، نظام إدارة العلاقات"
            />
          </div>

          {/* طريقة الحساب */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              طريقة الحساب / المعادلة
            </label>
            <textarea
              value={formData.formula}
              onChange={(e) => handleChange('formula', e.target.value)}
              rows={2}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="وصف طريقة حساب المؤشر"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

const KPIs = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [activeView, setActiveView] = useState('الكل');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [objectiveFilter, setObjectiveFilter] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('kpisListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  
  const { user } = useAuth();
  const notifications = useNotifications();
  
  // State for data management
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableObjectives, setAvailableObjectives] = useState<string[]>([]);

  // تحميل البيانات من قاعدة البيانات
  useEffect(() => {
    fetchKPIs();
    fetchObjectives();
  }, []);

  const fetchKPIs = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('kpis')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setKpis(data || []);
    } catch (error) {
      console.error('Error fetching KPIs:', error);
      notifications.error('خطأ في تحميل البيانات', 'فشل في تحميل مؤشرات الأداء');
    } finally {
      setLoading(false);
    }
  };

  const fetchObjectives = async () => {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('title')
        .order('title', { ascending: true });

      if (error) throw error;
      setAvailableObjectives(data?.map(obj => obj.title) || []);
    } catch (error) {
      console.error('Error fetching objectives:', error);
    }
  };

  // تطبيق الوضع الليلي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('kpisListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ممتاز': return 'bg-green-500';
      case 'جيد': return 'bg-blue-500';
      case 'متوسط': return 'bg-yellow-500';
      case 'يحتاج تحسين': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'جودة الخدمة': return 'text-blue-400 bg-blue-500/20';
      case 'الأداء التشغيلي': return 'text-green-400 bg-green-500/20';
      case 'النمو': return 'text-purple-400 bg-purple-500/20';
      case 'المشاركة': return 'text-yellow-400 bg-yellow-500/20';
      case 'التطوير': return 'text-indigo-400 bg-indigo-500/20';
      case 'الكفاءة': return 'text-teal-400 bg-teal-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  // وظائف إدارة المؤشرات
  const handleSaveKpi = async (kpiData: any) => {
    try {
      if (editingItem) {
        // تحديث مؤشر موجود
        const { data, error } = await supabase
          .from('kpis')
          .update({
            name: kpiData.name,
            category: kpiData.category,
            objective: kpiData.objective,
            related_project: kpiData.relatedProject,
            current_value: kpiData.currentValue,
            target_value: kpiData.targetValue,
            unit: kpiData.unit,
            trend: kpiData.trend,
            change_percentage: kpiData.change,
            status: kpiData.status,
            description: kpiData.description,
            frequency: kpiData.frequency,
            data_source: kpiData.dataSource,
            formula: kpiData.formula,
            responsible: kpiData.responsible,
            last_updated: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString()
          })
          .eq('id', editingItem.id)
          .select()
          .single();

        if (error) throw error;

        setKpis(prev =>
          prev.map(kpi =>
            kpi.id === editingItem.id ? data : kpi
          )
        );
        setSelectedItem(data);
        notifications.success('تم التحديث', 'تم تحديث مؤشر الأداء بنجاح');
      } else {
        // إنشاء مؤشر جديد
        const { data, error } = await supabase
          .from('kpis')
          .insert({
            name: kpiData.name,
            category: kpiData.category,
            objective: kpiData.objective,
            related_project: kpiData.relatedProject,
            current_value: kpiData.currentValue,
            target_value: kpiData.targetValue,
            unit: kpiData.unit,
            trend: kpiData.trend,
            change_percentage: kpiData.change,
            status: kpiData.status,
            description: kpiData.description,
            frequency: kpiData.frequency,
            data_source: kpiData.dataSource,
            formula: kpiData.formula,
            responsible: kpiData.responsible,
            last_updated: new Date().toISOString().split('T')[0]
          })
          .select()
          .single();

        if (error) throw error;

        setKpis(prev => [data, ...prev]);
        setSelectedItem(data);
        notifications.success('تم الإضافة', 'تم إضافة مؤشر الأداء بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving KPI:', error);
      notifications.error('خطأ', 'فشل في حفظ مؤشر الأداء');
      throw error;
    }
  };
  
  const handleDeleteKpi = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المؤشر؟')) {
      try {
        const { error } = await supabase
          .from('kpis')
          .delete()
          .eq('id', id);

        if (error) throw error;

        setKpis(prev => prev.filter(kpi => kpi.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف مؤشر الأداء بنجاح');
      } catch (error) {
        console.error('Error deleting KPI:', error);
        notifications.error('خطأ في الحذف', 'فشل في حذف مؤشر الأداء');
      }
    }
  };
  
  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    // إذا كنا في وضع التعديل، حدث العنصر المحرر إلى العنصر الجديد
    if (isEditMode) {
      setEditingItem(item);
    }
    // لا نغلق وضع التعديل - نبقيه مفتوحاً
  };

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    const progressPercentage = getProgressPercentage(item.current_value, item.target_value);
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.name}</h3>
            {isBeingEdited && (
              <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                قيد التحرير
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(item.category)}`}>
              {item.category}
            </span>
            <span className={`text-xs px-2 py-1 rounded ${getStatusColor(item.status)} text-white`}>
              {item.status}
            </span>
          </div>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">
          {item.description}
        </p>
        
        <div className="mb-2">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-gray-600 dark:text-gray-400">التقدم نحو الهدف</span>
            <span className="text-gray-900 dark:text-white">{progressPercentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div 
              className={`h-1 rounded-full transition-all ${
                progressPercentage >= 90 ? 'bg-green-500' :
                progressPercentage >= 70 ? 'bg-blue-500' :
                'bg-yellow-500'
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
        
        <div className="flex justify-between items-center text-xs">
          <span className="text-gray-600 dark:text-gray-400">
            الحالي: {item.current_value.toLocaleString('ar-SA')} {item.unit}
          </span>
          <span className="text-gray-600 dark:text-gray-400">
            المستهدف: {item.target_value.toLocaleString('ar-SA')} {item.unit}
          </span>
        </div>
      </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    // إذا كنا في وضع الإضافة أو التعديل، اعرض النموذج مباشرة
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineKpiForm 
            editData={editingItem}
            onSave={handleSaveKpi}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <TrendingUp className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر مؤشر لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر مؤشر أداء من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    const progressPercentage = getProgressPercentage(selectedItem.current_value, selectedItem.target_value);

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-green-500">
                <TrendingUp size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.name}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedItem.status)} text-white`}>
                    {selectedItem.status}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">التصنيف:</span>
                    <span className={`px-2 py-0.5 rounded text-xs ${getCategoryColor(selectedItem.category)}`}>
                      {selectedItem.category}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">التكرار:</span>
                    <span className="text-gray-600 dark:text-gray-400">{selectedItem.frequency}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">المسؤول:</span>
                    <span className="text-gray-600 dark:text-gray-400">{selectedItem.responsible}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasPermission('تعديل') && (
                <>
                  <button 
                    onClick={() => handleEdit(selectedItem)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                    title="تعديل"
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    onClick={() => handleDeleteKpi(selectedItem.id)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                    title="حذف"
                  >
                    <Trash2 size={18} />
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* التقدم العام */}
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-gray-600 dark:text-gray-400 text-sm">التقدم نحو الهدف</span>
                <span className="text-gray-900 dark:text-white text-sm font-medium">{progressPercentage.toFixed(1)}%</span>
              </div>
              <div className="flex items-center gap-1">
                {selectedItem.trend === 'up' ? (
                  <TrendingUp className="w-4 h-4 text-green-500" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-500" />
                )}
                <span className={`text-sm ${selectedItem.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                  {selectedItem.change_percentage}%
                </span>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all ${
                  progressPercentage >= 90 ? 'bg-green-500' :
                  progressPercentage >= 70 ? 'bg-blue-500' :
                  'bg-yellow-500'
                }`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
        </div>

        {/* Content - عرض المعلومات مباشرة */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">الوصف</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* القيم الأساسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">القيم الأساسية</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {selectedItem.current_value.toLocaleString('ar-SA')}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">القيمة الحالية</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">{selectedItem.unit}</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {selectedItem.target_value.toLocaleString('ar-SA')}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">القيمة المستهدفة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">{selectedItem.unit}</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className={`text-2xl font-bold ${selectedItem.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                      {selectedItem.change_percentage > 0 ? '+' : ''}{selectedItem.change_percentage}%
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">التغيير</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">من الفترة السابقة</div>
                  </div>
                </div>
              </div>

              {/* المعلومات التقنية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">المعلومات التقنية</h4>
                <div className="space-y-3">
                  {selectedItem.objective && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الهدف الاستراتيجي المرتبط</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.objective}</p>
                    </div>
                  )}
                  
                  {selectedItem.related_project && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">المشروع المرتبط</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.related_project}</p>
                    </div>
                  )}
                  
                  {selectedItem.data_source && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">مصدر البيانات</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.data_source}</p>
                    </div>
                  )}
                  
                  {selectedItem.formula && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">طريقة الحساب</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.formula}</p>
                    </div>
                  )}
                  
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">آخر تحديث</span>
                    <p className="text-gray-900 dark:text-white text-sm font-medium">
                      {new Date(selectedItem.last_updated).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // فلترة المؤشرات
  const filteredKpis = kpis.filter(kpi => {
    // فلترة حسب العرض
    let viewFilter = false;
    switch(activeView) {
      case 'الكل': viewFilter = true; break;
      case 'ممتاز': viewFilter = kpi.status === 'ممتاز'; break;
      case 'جيد': viewFilter = kpi.status === 'جيد'; break;
      case 'متوسط': viewFilter = kpi.status === 'متوسط'; break;
      case 'يحتاج تحسين': viewFilter = kpi.status === 'يحتاج تحسين'; break;
      default: viewFilter = true;
    }
    
    // فلترة حسب البحث
    const searchFilter = !searchTerm || 
      kpi.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      kpi.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      kpi.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      kpi.responsible.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلترة حسب الحالة
    const statusMatch = !statusFilter || kpi.status === statusFilter;
    
    // فلترة حسب التصنيف
    const categoryMatch = !categoryFilter || kpi.category === categoryFilter;
    
    // فلترة حسب الهدف المرتبط
    const objectiveMatch = !objectiveFilter || kpi.objective === objectiveFilter;

    return viewFilter && searchFilter && statusMatch && categoryMatch && objectiveMatch;
  });

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">مؤشرات الأداء الرئيسية</h1>
        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">إدارة ومتابعة مؤشرات الأداء والقياس</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex overflow-x-auto">
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'الكل' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('الكل')}
        >
          جميع المؤشرات
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'ممتاز' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('ممتاز')}
        >
          ممتازة
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'جيد' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('جيد')}
        >
          جيدة
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'متوسط' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('متوسط')}
        >
          متوسطة
        </button>
        <button 
          className={`px-6 py-3 border-b-2 ${activeView === 'يحتاج تحسين' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
          onClick={() => setActiveView('يحتاج تحسين')}
        >
          تحتاج تحسين
        </button>
        </div>
        
        {/* Add Button */}
        <div className="px-4">
          {hasPermission('إضافة') && (
            <button 
              onClick={handleAddNew}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
            >
              <Plus size={16} />
              جديد
            </button>
          )}
        </div>
      </div>

      {/* Split View Content */}
      <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
          {/* List Panel */}
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
            style={{ width: `${listWidth}%` }}
          >
            {/* Search Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              {/* البحث والفلاتر متجاوبة مع العرض */}
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400" size={16} />
                  <input 
                    type="text" 
                    placeholder="البحث..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm"
                  />
                </div>
                
                {/* الفلاتر - تتجاوب مع عرض القائمة */}
                <div className={`grid gap-2 ${listWidth > 40 ? 'grid-cols-3' : listWidth > 30 ? 'grid-cols-2' : 'grid-cols-1'}`}>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الحالة</option>
                    <option value="ممتاز">ممتاز</option>
                    <option value="جيد">جيد</option>
                    <option value="متوسط">متوسط</option>
                    <option value="يحتاج تحسين">يحتاج تحسين</option>
                  </select>
                  
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">التصنيف</option>
                    <option value="جودة الخدمة">جودة الخدمة</option>
                    <option value="الأداء التشغيلي">الأداء التشغيلي</option>
                    <option value="النمو">النمو</option>
                    <option value="المشاركة">المشاركة</option>
                    <option value="التطوير">التطوير</option>
                    <option value="الكفاءة">الكفاءة</option>
                  </select>
                  
                  <select
                    value={objectiveFilter}
                    onChange={(e) => setObjectiveFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none"
                  >
                    <option value="">الهدف المرتبط</option>
                    {availableObjectives.map((objective, index) => (
                      <option key={index} value={objective}>{objective}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            
            {/* List Items */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="mr-2 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {filteredKpis.map(kpi => (
                    <ListItem 
                      key={kpi.id} 
                      item={kpi} 
                      onClick={handleItemClick}
                      isSelected={selectedItem?.id === kpi.id}
                    />
                  ))}
                  {filteredKpis.length === 0 && !loading && (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">لا توجد مؤشرات مطابقة للبحث</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Resize Handle */}
          <div 
            className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
              isResizing 
                ? 'bg-blue-500' 
                : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
            }`}
            onMouseDown={() => setIsResizing(true)}
          >
            <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
          </div>

          {/* Detail Panel */}
          <div className="flex-1 overflow-hidden">
            <DetailPanel />
          </div>
        </div>

      {/* Role Switcher */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default KPIs;