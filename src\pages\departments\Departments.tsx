import React, { useState, useEffect, useRef } from 'react';
import { Building2, Plus, Search, Edit, Trash2, Mail, Phone, User, Loader2, Save, Brain } from 'lucide-react';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { DepartmentService, type Department } from '../../lib/departmentService';
import AIImport from '../../components/ai-import/AIImport';

// مكون النموذج المدمج للإدارات
const InlineDepartmentForm = ({ editData, onSave, onCancel, onDataChange }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    description: editData?.description || '',
    manager: editData?.manager || '',
    email: editData?.email || '',
    phone: editData?.phone || '',
    budget: editData?.budget || ''
  });

  const [errors, setErrors] = useState({});
  
  useEffect(() => {
    if (editData) {
      setFormData({
        name: editData?.name || '',
        description: editData?.description || '',
        manager: editData?.manager || '',
        email: editData?.email || '',
        phone: editData?.phone || '',
        budget: editData?.budget || ''
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم الإدارة مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف الإدارة مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        id: editData?.id || `dept-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث الإدارة' : 'تم إضافة الإدارة', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ الإدارة'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              اسم الإدارة *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل اسم الإدارة"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف الإدارة *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف مختصر للإدارة ومهامها"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                رئيس الإدارة
              </label>
              <input
                type="text"
                value={formData.manager}
                onChange={(e) => handleChange('manager', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                placeholder="اسم رئيس الإدارة"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الميزانية (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.email ? 'border-red-500' : ''}`}
                placeholder="<EMAIL>"
              />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                رقم الهاتف
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
                placeholder="+966 11 123 4567"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

const Departments = () => {
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('departmentsListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  const [activeTab, setActiveTab] = useState('الأقسام');
  
  const notifications = useNotifications();

  // البيانات من قاعدة البيانات
  const [departments, setDepartments] = useState<Department[]>([]);

  useEffect(() => {
    fetchDepartments();
  }, []);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;

      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('departmentsListWidth', listWidth.toString());
      }
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing, listWidth]);

  const fetchDepartments = async () => {
    try {
      setLoading(true);
      const data = await DepartmentService.getAllDepartments();
      setDepartments(data);
    } catch (error) {
      console.error('Error fetching departments:', error);
      notifications.error('خطأ', 'فشل في تحميل الإدارات');
    } finally {
      setLoading(false);
    }
  };

  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };

  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };

  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }

    setSelectedItem(item);

    if (isEditMode) {
      setEditingItem(item);
    }
  };

  // Helper functions
  const handleSaveDepartment = async (departmentData) => {
    try {
      if (editingItem) {
        const updatedDepartment = await DepartmentService.updateDepartment(editingItem.id, {
          name: departmentData.name,
          description: departmentData.description,
          manager: departmentData.manager,
          email: departmentData.email,
          phone: departmentData.phone,
          budget: departmentData.budget || '0'
        });

        setDepartments(prev =>
          prev.map(dept =>
            dept.id === editingItem.id ? updatedDepartment : dept
          )
        );
        notifications.success('تم التحديث', 'تم تحديث الإدارة بنجاح');
      } else {
        const newDepartment = await DepartmentService.createDepartment({
          name: departmentData.name,
          description: departmentData.description,
          manager: departmentData.manager,
          email: departmentData.email,
          phone: departmentData.phone,
          budget: departmentData.budget || '0'
        });

        setDepartments(prev => [...prev, newDepartment]);
        notifications.success('تم الإضافة', 'تم إضافة الإدارة بنجاح');
      }

      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving department:', error);
      notifications.error('خطأ', 'فشل في حفظ الإدارة');
      throw error;
    }
  };

  const handleDeleteDepartment = async (departmentId) => {
    if (window.confirm('هل أنت متأكد من حذف الإدارة؟')) {
      try {
        await DepartmentService.deleteDepartment(departmentId);
        setDepartments(prev => prev.filter(dept => dept.id !== departmentId));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف الإدارة بنجاح');
      } catch (error) {
        console.error('Error deleting department:', error);
        notifications.error('خطأ', 'فشل في حذف الإدارة');
      }
    }
  };

  // دالة استيراد البيانات من الذكاء الصناعي
  const handleAIImport = async (importedData: any[]) => {
    try {
      // تنظيف البيانات وإضافة الحقول المطلوبة
      const cleanedData = importedData.map(item => ({
        name: item.name || '',
        description: item.description || '',
        manager: item.manager || '',
        email: item.email || '',
        phone: item.phone || '',
        employee_count: parseInt(item.employee_count) || 0,
        budget: parseFloat(item.budget) || 0,
        status: item.status || 'نشط'
      }));

      const results = [];
      for (const dept of cleanedData) {
        try {
          const newDepartment = await DepartmentService.createDepartment(dept);
          results.push(newDepartment);
        } catch (error) {
          console.error('Error creating department:', dept.name, error);
          throw new Error(`فشل في إنشاء القسم: ${dept.name}`);
        }
      }

      // تحديث القائمة المحلية
      setDepartments(prev => [...results, ...prev]);

      // العودة إلى تبويب الأقسام
      setActiveTab('الأقسام');

      notifications.success('تم الاستيراد', `تم استيراد ${importedData.length} قسم بنجاح`);
    } catch (error) {
      console.error('Error importing departments:', error);
      throw error;
    }
  };

  // فلترة البيانات
  const filteredDepartments = departments.filter(dept => {
    const matchesSearch = !searchTerm ||
                         dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dept.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dept.manager?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;

    return (
      <div
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500'
            : isSelected
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500'
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => onClick(item)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <h3 className="text-gray-900 dark:text-white font-medium text-sm">{item.name}</h3>
            {isBeingEdited && (
              <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                قيد التحرير
              </span>
            )}
          </div>
          <span className="badge text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            {item.status || 'نشط'}
          </span>
        </div>

        <p className="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">
          {item.description}
        </p>

        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>المدير: {item.manager || 'غير محدد'}</span>
          <span>{item.employee_count || 0} موظف</span>
        </div>
      </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineDepartmentForm
            editData={editingItem}
            onSave={handleSaveDepartment}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Building2 className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر قسم لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر قسم من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-blue-500">
                <Building2 size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.name}
                  </h2>
                  <span className="text-xs px-2 py-1 rounded-full badge bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                    {selectedItem.status || 'نشط'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  رئيس القسم: {selectedItem.manager || 'غير محدد'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleEdit(selectedItem)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                title="تعديل"
              >
                <Edit size={18} />
              </button>
              <button
                onClick={() => handleDeleteDepartment(selectedItem.id)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                title="حذف"
              >
                <Trash2 size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">وصف القسم</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* الإحصائيات */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">الإحصائيات</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{selectedItem.employee_count || 0}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الموظفين</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">عدد</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {selectedItem.budget ? (parseInt(selectedItem.budget) / 1000).toFixed(0) : 0}K
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الميزانية</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">ريال</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {selectedItem.status === 'نشط' ? 'نشط' : 'غير نشط'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الحالة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">وضع</div>
                  </div>
                </div>
              </div>

              {/* معلومات الاتصال */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">معلومات الاتصال</h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-900 dark:text-white text-sm">{selectedItem.email || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-900 dark:text-white text-sm">{selectedItem.phone || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-900 dark:text-white text-sm">
                      رئيس القسم: {selectedItem.manager || 'غير محدد'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-neutral-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <div className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-primary-600" />
          <h1 className="text-2xl font-bold text-neutral-800">إدارة الأقسام</h1>
        </div>
        <p className="text-neutral-600 text-sm mt-1">إدارة وتنظيم أقسام المؤسسة</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex overflow-x-auto">
          <button
            className={`px-6 py-3 border-b-2 ${activeTab === 'الأقسام' ? 'border-blue-500 text-blue-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveTab('الأقسام')}
          >
            الأقسام
          </button>
          <button
            className={`px-6 py-3 border-b-2 ${activeTab === 'الاستيراد بالذكاء الصناعي' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap flex items-center gap-2`}
            onClick={() => setActiveTab('الاستيراد بالذكاء الصناعي')}
          >
            <Brain className="w-4 h-4" />
            الاستيراد بالذكاء الصناعي
          </button>
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'الاستيراد بالذكاء الصناعي' ? (
        <AIImport
          dataType="departments"
          onImport={handleAIImport}
        />
      ) : (
        /* Split View Content */
        <div className="flex gap-6 h-[calc(100vh-200px)]" ref={containerRef}>
        {/* List Panel */}
        <div
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
          style={{ width: `${listWidth}%` }}
        >
          {/* Search Header */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 w-5 h-5" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pr-10 pl-4 py-2 focus:outline-none text-sm"
                  placeholder="البحث في الإدارات..."
                />
              </div>

              <button
                onClick={handleAddNew}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
              >
                <Plus className="w-4 h-4" />
                <span>جديد</span>
              </button>
            </div>
          </div>

          {/* List Items */}
          <div className="flex-1 overflow-y-auto">
            <div className="space-y-1 p-2">
              {filteredDepartments.map(dept => (
                <ListItem
                  key={dept.id}
                  item={dept}
                  onClick={handleItemClick}
                  isSelected={selectedItem?.id === dept.id}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Resize Handle */}
        <div
          className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
            isResizing
              ? 'bg-blue-500'
              : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
          }`}
          onMouseDown={() => setIsResizing(true)}
        >
          <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
        </div>

        {/* Detail Panel */}
        <div className="flex-1 overflow-hidden">
          <DetailPanel />
        </div>
        </div>
      )}
    </div>
  );
};

export default Departments;
