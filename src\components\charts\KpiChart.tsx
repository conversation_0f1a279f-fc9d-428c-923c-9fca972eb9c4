import { useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

// This is a simplified chart component with mock data
// In a real application, we would fetch this data from Supabase

const KpiChart = () => {
  const [chartData] = useState<{options: ApexOptions, series: any[]}>({
    options: {
      chart: {
        type: 'bar',
        toolbar: {
          show: false
        },
        fontFamily: 'Noto Sans Arabic, sans-serif',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 4,
        },
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: [
          'الربع 1', 'الربع 2', 'الربع 3', 'الربع 4'
        ],
      },
      yaxis: {
        title: {
          text: 'نسبة الإنجاز %',
          style: {
            fontFamily: 'Noto Sans Arabic, sans-serif',
          }
        },
        max: 100
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + "%";
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        offsetY: 0,
        fontFamily: 'Noto Sans Arabic, sans-serif',
      },
      colors: ['#1565C0', '#00695C', '#FFC107'],
    },
    series: [{
      name: 'مستهدف',
      data: [80, 85, 90, 95]
    }, {
      name: 'متحقق',
      data: [75, 78, 83, 0]
    }, {
      name: 'متوقع',
      data: [0, 0, 0, 87]
    }]
  });

  return (
    <Chart 
      options={chartData.options} 
      series={chartData.series} 
      type="bar" 
      height="100%" 
    />
  );
};

export default KpiChart;