import React, { useState } from 'react';
import { Brain, Copy, Upload, <PERSON>rk<PERSON>, CheckCircle, AlertCircle, FileText, Target, BarChart3, Building2, Activity, ArrowLeft } from 'lucide-react';
import { useNotifications } from '../ui/NotificationSystem';
import { supabase } from '../../lib/supabase';

interface SmartImportProps {
  onImport: (data: any, importType: string) => Promise<void>;
}

const SmartImport: React.FC<SmartImportProps> = ({ onImport }) => {
  const [step, setStep] = useState<'select' | 'prompt' | 'paste' | 'preview' | 'importing'>('select');
  const [selectedType, setSelectedType] = useState<string>('');
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [userInput, setUserInput] = useState('');
  const [parsedData, setParsedData] = useState<any>({});
  const [itemCount] = useState(5); // عدد ثابت
  const [activeTab, setActiveTab] = useState<'prompt' | 'current-data'>('prompt');
  const [currentData, setCurrentData] = useState<any>({});
  const notifications = useNotifications();

  // خيارات الاستيراد
  const importOptions = [
    {
      id: 'objectives',
      title: 'الأهداف الاستراتيجية فقط',
      description: 'استيراد الأهداف الاستراتيجية الأساسية',
      icon: Target,
      color: 'blue',
      includes: ['strategic_objectives']
    },
    {
      id: 'objectives_kpis',
      title: 'الأهداف + المؤشرات',
      description: 'استيراد الأهداف مع مؤشرات الأداء المرتبطة',
      icon: BarChart3,
      color: 'green',
      includes: ['strategic_objectives', 'kpis']
    },
    {
      id: 'objectives_kpis_projects',
      title: 'الأهداف + المؤشرات + المشاريع + الأقسام',
      description: 'استيراد الأهداف والمؤشرات والمشاريع مع الأقسام',
      icon: Building2,
      color: 'purple',
      includes: ['strategic_objectives', 'kpis', 'projects', 'departments']
    },
    {
      id: 'complete',
      title: 'الحزمة الكاملة',
      description: 'استيراد جميع العناصر: الأهداف + المؤشرات + المشاريع + الأنشطة + الأقسام',
      icon: Activity,
      color: 'orange',
      includes: ['strategic_objectives', 'kpis', 'projects', 'activities', 'departments']
    }
  ];

  // قوالب البيانات
  const dataTemplates = {
    strategic_objectives: {
      name: 'الأهداف الاستراتيجية',
      fields: {
        title: 'عنوان الهدف (نص مطلوب)',
        description: 'وصف الهدف (نص)',
        owner: 'مالك الهدف (نص مطلوب)',
        department: 'القسم المسؤول (نص مطلوب)',
        priority: 'الأولوية (عالية/منخفضة)',
        status: 'الحالة (نشط/غير نشط)',
        progress: 'نسبة الإنجاز (رقم من 0 إلى 100)',
        start_date: 'تاريخ البداية (YYYY-MM-DD)',
        end_date: 'تاريخ النهاية (YYYY-MM-DD)',
        budget: 'الميزانية (رقم)',
        expected_outcome: 'النتائج المتوقعة (نص)',
        success_metrics: 'مؤشرات النجاح (نص)',
        manager: 'المدير المسؤول (نص)'
      }
    },
    kpis: {
      name: 'مؤشرات الأداء',
      fields: {
        name: 'اسم المؤشر (نص مطلوب)',
        category: 'فئة المؤشر (نص مطلوب)',
        objective: 'الهدف المرتبط (نص)',
        related_project: 'المشروع المرتبط (نص)',
        current_value: 'القيمة الحالية (رقم)',
        target_value: 'القيمة المستهدفة (رقم مطلوب)',
        unit: 'وحدة القياس (نص مطلوب)',
        trend: 'الاتجاه (صاعد/نازل/ثابت)',
        change_percentage: 'نسبة التغيير (رقم)',
        status: 'الحالة (نشط/غير نشط)',
        description: 'وصف المؤشر (نص)',
        frequency: 'تكرار القياس (يومي/أسبوعي/شهري/ربع سنوي/سنوي)',
        data_source: 'مصدر البيانات (نص)',
        formula: 'معادلة الحساب (نص)',
        responsible: 'المسؤول (نص مطلوب)',
        last_updated: 'آخر تحديث (YYYY-MM-DD)'
      }
    },
    projects: {
      name: 'المشاريع',
      fields: {
        title: 'اسم المشروع (نص مطلوب)',
        description: 'وصف المشروع (نص)',
        objective: 'الهدف المرتبط (نص)',
        related_kpi: 'المؤشر المرتبط (نص)',
        department: 'القسم المسؤول (نص مطلوب)',
        manager: 'مدير المشروع (نص مطلوب)',
        priority: 'الأولوية (عالية/منخفضة)',
        status: 'الحالة (جاري التنفيذ/مكتمل/متأخر/متعثر)',
        progress: 'نسبة الإنجاز (رقم من 0 إلى 100)',
        start_date: 'تاريخ البداية (YYYY-MM-DD)',
        end_date: 'تاريخ النهاية (YYYY-MM-DD)',
        budget: 'الميزانية (رقم)'
      }
    },
    activities: {
      name: 'الأنشطة',
      fields: {
        title: 'عنوان النشاط (نص مطلوب)',
        description: 'وصف النشاط (نص)',
        project: 'المشروع المرتبط (نص)',
        related_project: 'المشروع المرتبط (نص)',
        department: 'القسم المسؤول (نص مطلوب)',
        assignee: 'المسؤول عن النشاط (نص مطلوب)',
        status: 'الحالة (جاري التنفيذ/مكتمل/متأخر/متعثر)',
        priority: 'الأولوية (عالية/منخفضة)',
        progress: 'نسبة الإنجاز (رقم من 0 إلى 100)',
        start_date: 'تاريخ البداية (YYYY-MM-DD)',
        end_date: 'تاريخ النهاية (YYYY-MM-DD)',
        budget: 'الميزانية (رقم)'
      }
    },
    departments: {
      name: 'الأقسام',
      fields: {
        name: 'اسم القسم (نص مطلوب)',
        description: 'وصف القسم (نص)',
        manager: 'رئيس القسم (نص)',
        email: 'البريد الإلكتروني (email)',
        phone: 'رقم الهاتف (نص)',
        employee_count: 'عدد الموظفين (رقم صحيح)',
        budget: 'الميزانية (رقم)',
        status: 'الحالة (نشط/غير نشط)'
      }
    }
  };

  // جلب البيانات الحالية المطلوبة
  const fetchCurrentData = async (typeId?: string) => {
    const targetType = typeId || selectedType;
    const selectedOption = importOptions.find(opt => opt.id === targetType);
    if (!selectedOption) return;

    const data = {};

    try {
      // تحديد البيانات المطلوبة بناءً على نوع الاستيراد
      const dependencies = getDependencies(targetType);

      for (const dep of dependencies) {
        const { data: result, error } = await supabase
          .from(dep.table)
          .select(dep.fields)
          .limit(50); // حد أقصى 50 عنصر لكل نوع

        if (!error && result) {
          data[dep.name] = result;
        }
      }

      setCurrentData(data);
    } catch (error) {
      console.error('Error fetching current data:', error);
    }
  };

  // تحديد قيود وأنواع الحقول
  const getFieldConstraints = (dataType: string, fieldName: string): string => {
    const constraints = {
      strategic_objectives: {
        title: '(نص مطلوب)',
        description: '(نص)',
        owner: '(نص مطلوب)',
        department: '(نص مطلوب)',
        priority: '(نص: عالية/متوسطة/منخفضة)',
        status: '(نص: جديد/جاري/مكتمل/متأخر/معلق)',
        progress: '(رقم: 0-100)',
        start_date: '(تاريخ: YYYY-MM-DD)',
        end_date: '(تاريخ: YYYY-MM-DD)',
        budget: '(رقم)',
        expected_outcome: '(نص)',
        success_metrics: '(نص)',
        manager: '(نص)'
      },
      kpis: {
        name: '(نص مطلوب)',
        category: '(نص مطلوب)',
        objective: '(نص)',
        related_project: '(نص)',
        current_value: '(رقم)',
        target_value: '(رقم مطلوب)',
        unit: '(نص مطلوب)',
        trend: '(نص: صاعد/نازل/ثابت)',
        change_percentage: '(رقم)',
        status: '(نص: ممتاز/جيد/متوسط/يحتاج تحسين)',
        description: '(نص)',
        frequency: '(نص: يومي/أسبوعي/شهري/ربع سنوي/سنوي)',
        data_source: '(نص)',
        formula: '(نص)',
        responsible: '(نص مطلوب)',
        last_updated: '(تاريخ: YYYY-MM-DD)'
      },
      projects: {
        title: '(نص مطلوب)',
        description: '(نص)',
        objective: '(نص)',
        related_kpi: '(نص)',
        department: '(نص مطلوب)',
        manager: '(نص مطلوب)',
        priority: '(نص: عالية/متوسطة/منخفضة)',
        status: '(نص: جديدة/جاري التنفيذ/مكتملة/متأخرة/التخطيط/معلقة)',
        progress: '(رقم: 0-100)',
        start_date: '(تاريخ: YYYY-MM-DD)',
        end_date: '(تاريخ: YYYY-MM-DD)',
        budget: '(رقم)',
        expected_outcome: '(نص)',
        success_metrics: '(نص)'
      },
      activities: {
        title: '(نص مطلوب)',
        description: '(نص)',
        initiative: '(نص)',
        related_project: '(نص)',
        department: '(نص مطلوب)',
        assignee: '(نص مطلوب)',
        status: '(نص: جاري التنفيذ/مكتمل/متأخر/متعثر)',
        status: '(نص: لم يبدأ/جاري التنفيذ/مكتمل/متأخر/متعثر)',
        priority: '(نص: عالية/متوسطة/منخفضة)',
        progress: '(رقم: 0-100)',
        start_date: '(تاريخ: YYYY-MM-DD)',
        end_date: '(تاريخ: YYYY-MM-DD)',
        budget: '(رقم)',
        participants: '(رقم)',
        estimated_hours: '(رقم)',
        actual_hours: '(رقم)'
      },
      departments: {
        name: '(نص مطلوب)',
        description: '(نص)',
        manager: '(نص)',
        email: '(بريد إلكتروني)',
        phone: '(رقم هاتف)',
        employee_count: '(رقم)',
        budget: '(رقم)',
        status: '(نص: نشط/غير نشط)'
      }
    };

    return constraints[dataType]?.[fieldName] || '';
  };

  // تحديد التبعيات لكل نوع استيراد
  const getDependencies = (importType: string) => {
    switch (importType) {
      case 'complete':
        return [
          { name: 'الأهداف الاستراتيجية', table: 'strategic_objectives', fields: 'title, description, owner' },
          { name: 'مؤشرات الأداء', table: 'kpis', fields: 'name, category, objective' },
          { name: 'المشاريع', table: 'initiatives', fields: 'title, description, objective' }
        ];
      case 'objectives_kpis_projects':
        return [
          { name: 'الأهداف الاستراتيجية', table: 'strategic_objectives', fields: 'title, description, owner' },
          { name: 'مؤشرات الأداء', table: 'kpis', fields: 'name, category, objective' }
        ];
      case 'objectives_kpis':
        return [
          { name: 'الأهداف الاستراتيجية', table: 'strategic_objectives', fields: 'title, description, owner' }
        ];
      case 'objectives':
        return [
          { name: 'الأهداف الاستراتيجية', table: 'strategic_objectives', fields: 'title, description, owner' }
        ];
      default:
        return [];
    }
  };

  const generatePromptForType = (typeId: string) => {
    const selectedOption = importOptions.find(opt => opt.id === typeId);
    if (!selectedOption) return;

    generatePromptWithOption(selectedOption);
  };

  const generatePrompt = () => {
    const selectedOption = importOptions.find(opt => opt.id === selectedType);
    if (!selectedOption) return;

    generatePromptWithOption(selectedOption);
  };

  const generatePromptWithOption = (selectedOption: any) => {

    let prompt = `أنت مساعد ذكي متخصص في إنشاء بيانات منظمة للتخطيط الاستراتيجي. المطلوب منك إنشاء ${itemCount} عنصر لكل نوع من البيانات التالية:\n\n`;

    // إضافة تفاصيل كل نوع بيانات مع أنواع الحقول والقيم المسموحة
    selectedOption.includes.forEach(dataType => {
      const template = dataTemplates[dataType];
      prompt += `## ${template.name}:\n`;
      prompt += `### الحقول المطلوبة:\n`;
      Object.entries(template.fields).forEach(([key, desc]) => {
        prompt += `- **${key}**: ${desc}`;

        // إضافة نوع الحقل والقيم المسموحة
        const fieldConstraints = getFieldConstraints(dataType, key);
        if (fieldConstraints) {
          prompt += ` ${fieldConstraints}`;
        }
        prompt += `\n`;
      });
      prompt += `\n`;
    });

    prompt += `## قالب JSON المطلوب:\n\`\`\`json\n{\n`;
    selectedOption.includes.forEach((dataType, index) => {
      const template = dataTemplates[dataType];
      prompt += `  "${dataType}": [\n    {\n`;
      Object.keys(template.fields).forEach(key => {
        prompt += `      "${key}": "قيمة_${key}",\n`;
      });
      prompt += `    }\n  ]`;
      if (index < selectedOption.includes.length - 1) prompt += ',';
      prompt += '\n';
    });
    prompt += `}\n\`\`\`\n\n`;

    prompt += `## التعليمات المهمة:\n`;
    prompt += `1. أنشئ ${itemCount} عنصر لكل نوع بيانات\n`;
    prompt += `2. استخدم بيانات واقعية ومنطقية باللغة العربية\n`;
    prompt += `3. اربط البيانات ببعضها منطقياً (الأهداف ← المؤشرات ← المشاريع ← الأنشطة)\n`;
    prompt += `4. في الحزم الكاملة، بعض المعلومات الاختيارية قد تكون ناقصة وهذا مقبول\n`;
    prompt += `5. تأكد من صحة تنسيق JSON\n`;
    prompt += `6. استخدم التواريخ بصيغة YYYY-MM-DD\n\n`;

    prompt += `الآن، بناءً على المعلومات التالية، أنشئ البيانات المطلوبة:\n\n[هنا سيضع المستخدم المعلومات التي يريد إنشاء البيانات بناءً عليها]`;

    setGeneratedPrompt(prompt);

    // جلب البيانات الحالية
    fetchCurrentData(selectedOption.id);

    setStep('prompt');
  };

  const copyPrompt = () => {
    navigator.clipboard.writeText(generatedPrompt);
    notifications.success('تم النسخ', 'تم نسخ المطالبة إلى الحافظة');
  };

  const copyCurrentData = () => {
    const formattedData = Object.entries(currentData)
      .map(([dataType, items]: [string, any[]]) => {
        return `## ${dataType} (${items.length} عنصر):\n` +
               items.map((item, index) =>
                 `${index + 1}. ${item.title || item.name || `عنصر ${index + 1}`}` +
                 (item.description ? `\n   الوصف: ${item.description}` : '')
               ).join('\n') + '\n';
      })
      .join('\n');

    navigator.clipboard.writeText(formattedData);
    notifications.success('تم النسخ', 'تم نسخ المعطيات الحالية إلى الحافظة');
  };

  const parseAndValidateData = () => {
    try {
      const cleanInput = userInput.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const data = JSON.parse(cleanInput);
      
      const selectedOption = importOptions.find(opt => opt.id === selectedType);
      if (!selectedOption) throw new Error('نوع الاستيراد غير محدد');

      // التحقق من وجود جميع الأنواع المطلوبة
      const missingTypes = selectedOption.includes.filter(type => !data[type]);
      if (missingTypes.length > 0) {
        throw new Error(`البيانات تفتقد للأنواع التالية: ${missingTypes.join(', ')}`);
      }

      setParsedData(data);
      setStep('preview');
      
      const totalItems = selectedOption.includes.reduce((sum, type) => sum + (data[type]?.length || 0), 0);
      notifications.success('تم التحليل', `تم تحليل ${totalItems} عنصر بنجاح`);
    } catch (error) {
      notifications.error('خطأ في التحليل', error.message);
    }
  };

  const handleImport = async () => {
    try {
      setStep('importing');
      await onImport(parsedData, selectedType);
      setStep('select');
      setUserInput('');
      setParsedData({});
    } catch (error) {
      notifications.error('خطأ في الاستيراد', 'فشل في استيراد البيانات');
      setStep('preview');
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 'select':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Brain className="w-16 h-16 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                الاستيراد الذكي للبيانات
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                اختر نوع البيانات التي تريد استيرادها
              </p>
            </div>

            <div className="grid gap-4">
              {importOptions.map(option => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.id}
                    onClick={() => {
                      setSelectedType(option.id);
                      // إنشاء المطالبة تلقائياً بعد اختيار النوع
                      setTimeout(() => {
                        generatePromptForType(option.id);
                      }, 100);
                    }}
                    className={`p-4 rounded-lg border-2 transition-all text-right ${
                      selectedType === option.id
                        ? `border-${option.color}-500 bg-${option.color}-50 dark:bg-${option.color}-900/20`
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className={`w-6 h-6 text-${option.color}-500 mt-1`} />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                          {option.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {option.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {option.includes.map(type => (
                            <span key={type} className={`px-2 py-1 text-xs rounded bg-${option.color}-100 dark:bg-${option.color}-900/30 text-${option.color}-700 dark:text-${option.color}-300`}>
                              {dataTemplates[type]?.name}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>


          </div>
        );

      case 'prompt':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button
                  onClick={() => {
                    setStep('select');
                    setSelectedType('');
                    setCurrentData({});
                    setActiveTab('prompt');
                  }}
                  className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-4 h-4" />
                  رجوع
                </button>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  مطالبة الذكاء الصناعي
                </h3>
              </div>
              <div className="flex gap-2">
                {activeTab === 'current-data' && Object.keys(currentData).length > 0 && (
                  <button
                    onClick={copyCurrentData}
                    className="flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-700 hover:bg-green-200 dark:hover:bg-green-600 text-green-900 dark:text-white rounded-lg transition-colors"
                  >
                    <Copy className="w-4 h-4" />
                    نسخ المعطيات
                  </button>
                )}
                <button
                  onClick={copyPrompt}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
                >
                  <Copy className="w-4 h-4" />
                  نسخ المطالبة
                </button>
              </div>
            </div>

            {/* تبويبات المطالبة والمعطيات الحالية */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="flex space-x-8" aria-label="Tabs">
                <button
                  onClick={() => setActiveTab('prompt')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'prompt'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  المطالبة
                </button>
                <button
                  onClick={() => setActiveTab('current-data')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'current-data'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  المعطيات الحالية
                </button>
              </nav>
            </div>

            {/* محتوى التبويبات */}
            {activeTab === 'prompt' ? (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                  {generatedPrompt}
                </pre>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
                <div className="space-y-4">
                  {Object.keys(currentData).length === 0 ? (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                      <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>جاري تحميل المعطيات الحالية...</p>
                    </div>
                  ) : (
                    Object.entries(currentData).map(([dataType, items]: [string, any[]]) => (
                      <div key={dataType} className="border-b border-gray-200 dark:border-gray-600 pb-3 last:border-b-0">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                          {dataType} ({items.length} عنصر)
                        </h4>
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                          {items.slice(0, 10).map((item, index) => (
                            <div key={index} className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 rounded px-2 py-1">
                              • {item.title || item.name || `عنصر ${index + 1}`}
                              {item.description && (
                                <span className="text-xs text-gray-500 block truncate">
                                  {item.description}
                                </span>
                              )}
                            </div>
                          ))}
                          {items.length > 10 && (
                            <div className="text-xs text-gray-500 dark:text-gray-500 text-center">
                              ... و {items.length - 10} عنصر آخر
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">خطوات الاستخدام:</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>انسخ المطالبة أعلاه</li>
                    <li>الصقها في أي نموذج ذكاء صناعي (ChatGPT, Claude, إلخ)</li>
                    <li>أضف المعلومات التي تريد إنشاء البيانات بناءً عليها</li>
                    <li>انسخ النتيجة والصقها في الحقل أدناه</li>
                  </ol>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الصق نتيجة الذكاء الصناعي هنا
              </label>
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                rows={10}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500"
                placeholder="الصق كود JSON هنا..."
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setStep('select')}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
              >
                رجوع
              </button>
              <button
                onClick={parseAndValidateData}
                disabled={!userInput.trim()}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <CheckCircle className="w-4 h-4" />
                تحليل البيانات
              </button>
            </div>
          </div>
        );

      case 'preview':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setStep('prompt')}
                  className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-4 h-4" />
                  رجوع
                </button>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  معاينة البيانات المستوردة
                </h3>
              </div>
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="text-sm font-medium">تم التحليل بنجاح</span>
              </div>
            </div>

            {/* عرض ملخص لكل نوع بيانات */}
            <div className="space-y-4">
              {Object.entries(parsedData).map(([dataType, items]: [string, any[]]) => {
                const template = dataTemplates[dataType];
                if (!template) return null;

                return (
                  <div key={dataType} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {template.name}
                      </h4>
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-sm">
                        {items.length} عنصر
                      </span>
                    </div>

                    <div className="space-y-2">
                      {items.slice(0, 3).map((item, index) => (
                        <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                          • {item.title || item.name || `عنصر ${index + 1}`}
                        </div>
                      ))}
                      {items.length > 3 && (
                        <div className="text-sm text-gray-500 dark:text-gray-500">
                          ... و {items.length - 3} عنصر آخر
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleImport}
                className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
              >
                <Upload className="w-4 h-4" />
                استيراد البيانات
              </button>
            </div>
          </div>
        );

      case 'importing':
        return (
          <div className="text-center py-12">
            <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
              جاري الاستيراد...
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              يتم استيراد البيانات إلى قاعدة البيانات
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {renderStepContent()}
    </div>
  );
};

export default SmartImport;
