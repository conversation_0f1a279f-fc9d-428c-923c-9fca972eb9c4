import { useState } from 'react';
import { X, Save, Loader2, <PERSON>, EyeOff } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface UserFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (user: any) => Promise<void>;
  editData?: any;
}

const UserForm = ({ isOpen, onClose, onSave, editData }: UserFormProps) => {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    name: editData?.name || '',
    email: editData?.email || '',
    password: '',
    confirmPassword: '',
    phone: editData?.phone || '',
    department: editData?.department || '',
    position: editData?.position || '',
    role: editData?.role || 'user',
    employeeId: editData?.employeeId || '',
    joinDate: editData?.joinDate || '',
    manager: editData?.manager || '',
    location: editData?.location || '',
    bio: editData?.bio || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من كلمة المرور للمستخدمين الجدد فقط
    if (!editData) {
      if (!formData.password.trim()) {
        newErrors.password = 'كلمة المرور مطلوبة';
      } else if (formData.password.length < 6) {
        newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (!formData.confirmPassword.trim()) {
        newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
      }
    } else if (formData.password && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'الإدارة مطلوبة';
    }

    if (!formData.position.trim()) {
      newErrors.position = 'المنصب مطلوب';
    }

    if (!formData.employeeId.trim()) {
      newErrors.employeeId = 'رقم الموظف مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      const userData = {
        ...formData,
        id: editData?.id || `user-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: editData?.status || 'نشط',
        lastLogin: editData?.lastLogin || null,
        avatar: editData?.avatar || null
      };

      // إزالة كلمة المرور إذا كانت فارغة في التعديل
      if (editData && !formData.password) {
        delete userData.password;
        delete userData.confirmPassword;
      }

      await onSave(userData);
      
      notifications.success(
        editData ? 'تم تحديث المستخدم' : 'تم إضافة المستخدم', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* الاسم */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الاسم الكامل *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`input-field ${errors.name ? 'border-red-500' : ''}`}
              placeholder="أدخل الاسم الكامل"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* البريد الإلكتروني */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              className={`input-field ${errors.email ? 'border-red-500' : ''}`}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
          </div>

          {/* كلمة المرور */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كلمة المرور {!editData && '*'}
                {editData && <span className="text-xs text-gray-500">(اتركها فارغة للاحتفاظ بالحالية)</span>}
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  className={`input-field pr-10 ${errors.password ? 'border-red-500' : ''}`}
                  placeholder="أدخل كلمة المرور"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تأكيد كلمة المرور {!editData && '*'}
              </label>
              <input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleChange('confirmPassword', e.target.value)}
                className={`input-field ${errors.confirmPassword ? 'border-red-500' : ''}`}
                placeholder="أعد إدخال كلمة المرور"
              />
              {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
            </div>
          </div>

          {/* الهاتف ورقم الموظف */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم الهاتف
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className="input-field"
                placeholder="+966 50 123 4567"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم الموظف *
              </label>
              <input
                type="text"
                value={formData.employeeId}
                onChange={(e) => handleChange('employeeId', e.target.value)}
                className={`input-field ${errors.employeeId ? 'border-red-500' : ''}`}
                placeholder="EMP001"
              />
              {errors.employeeId && <p className="text-red-500 text-xs mt-1">{errors.employeeId}</p>}
            </div>
          </div>

          {/* الإدارة والمنصب */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الإدارة *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`input-field ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الإدارة</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
                <option value="الإدارة المالية">الإدارة المالية</option>
                <option value="إدارة الموارد البشرية">إدارة الموارد البشرية</option>
                <option value="إدارة تقنية المعلومات">إدارة تقنية المعلومات</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المنصب *
              </label>
              <input
                type="text"
                value={formData.position}
                onChange={(e) => handleChange('position', e.target.value)}
                className={`input-field ${errors.position ? 'border-red-500' : ''}`}
                placeholder="مثل: مدير، موظف، أخصائي"
              />
              {errors.position && <p className="text-red-500 text-xs mt-1">{errors.position}</p>}
            </div>
          </div>

          {/* الدور والمدير المباشر */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                دور المستخدم
              </label>
              <select
                value={formData.role}
                onChange={(e) => handleChange('role', e.target.value)}
                className="input-field"
              >
                <option value="user">مستخدم عادي</option>
                <option value="manager">مدير</option>
                <option value="admin">مدير نظام</option>
                <option value="viewer">مشاهد فقط</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المدير المباشر
              </label>
              <input
                type="text"
                value={formData.manager}
                onChange={(e) => handleChange('manager', e.target.value)}
                className="input-field"
                placeholder="اسم المدير المباشر"
              />
            </div>
          </div>

          {/* تاريخ الانضمام والموقع */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ الانضمام
              </label>
              <input
                type="date"
                value={formData.joinDate}
                onChange={(e) => handleChange('joinDate', e.target.value)}
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الموقع
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleChange('location', e.target.value)}
                className="input-field"
                placeholder="مثل: الطابق الأول، المكتب 101"
              />
            </div>
          </div>

          {/* النبذة الشخصية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              النبذة الشخصية
            </label>
            <textarea
              value={formData.bio}
              onChange={(e) => handleChange('bio', e.target.value)}
              rows={3}
              className="input-field"
              placeholder="نبذة مختصرة عن المستخدم ومهاراته"
            />
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ المستخدم'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserForm;
