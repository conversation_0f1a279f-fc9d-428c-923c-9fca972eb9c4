-- س<PERSON><PERSON>ي<PERSON><PERSON> SQL لإضافة المستخدمين الإداريين
-- يجب تشغيل هذا السكريبت في Supabase SQL Editor

-- ✅ تم إنشاء الأقسام المطلوبة بنجاح:
-- قسم التطوع: c55f9a2d-9c98-4326-a076-cfb6786646fb
-- الإدارة التنفيذية: 6da8a045-c0ea-449a-8353-5ca70d5d6163
-- البرامج والمشاريع: 1289ad7b-5e1d-4eda-b68f-b2ab19274027
-- الموارد المالية: 712793b0-9459-4e4f-b055-724970808039

-- ✅ الأدوار المتاحة:
-- ADMIN: 1a6bfbe0-f9d0-4377-93fd-9411d01ce444
-- MANAGER: 2d06f576-2625-46e4-849a-0e79b1db8d14
-- SUPERVISOR: db256056-2dc8-4e75-83a8-abaf9432173b
-- EMPLOYEE: 60e526d6-8797-461d-8dd8-a94329a549b7

-- خطوات إضافة المستخدمين:

-- الخطوة 1: إنشاء المستخدمين في نظام المصادقة
-- يجب تشغيل هذه الأوامر في Supabase Dashboard > Authentication > Users > Add User

/*
إضافة المستخدمين التالين في Supabase Dashboard:

1. أحمد الدريهم
   Email: <EMAIL>
   Password: TempPass123!

2. سعد الدريهم
   Email: <EMAIL>
   Password: TempPass123!

3. سعد القحيز
   Email: <EMAIL>
   Password: TempPass123!

4. عبدالعزيز المطرد
   Email: <EMAIL>
   Password: TempPass123!

5. عبدالله المحسن
   Email: <EMAIL>
   Password: TempPass123!

6. معتصم العرفج
   Email: <EMAIL>
   Password: TempPass123!

7. ناصر اليحيى
   Email: <EMAIL>
   Password: TempPass123!
*/

-- الخطوة 2: بعد إنشاء المستخدمين في نظام المصادقة، قم بتشغيل الاستعلامات التالية
-- لإنشاء ملفاتهم الشخصية (استبدل USER_ID بالمعرف الفعلي من auth.users)

-- للمساعدة في إنشاء المستخدمين، إليك البيانات المطلوبة:

/*
المستخدمون المطلوب إضافتهم:

1. أحمد الدريهم
   - البريد: <EMAIL>
   - المنصب: مدير قسم التطوع
   - القسم: قسم التطوع
   - الدور: MANAGER
   - الهاتف: 966531685640

2. سعد الدريهم
   - البريد: <EMAIL>
   - المنصب: المدير التنفيذي
   - القسم: الإدارة التنفيذية
   - الدور: ADMIN
   - الهاتف: 966555104096

3. سعد القحيز
   - البريد: <EMAIL>
   - المنصب: مدير مشاريع
   - القسم: البرامج والمشاريع
   - الدور: MANAGER
   - الهاتف: 966555108628

4. عبدالعزيز المطرد
   - البريد: <EMAIL>
   - المنصب: مدير مشاريع
   - القسم: البرامج والمشاريع
   - الدور: MANAGER
   - الهاتف: 966544303202

5. عبدالله المحسن
   - البريد: <EMAIL>
   - المنصب: مدير قسم الموارد المالية
   - القسم: الموارد المالية
   - الدور: MANAGER
   - الهاتف: 966555106645

6. معتصم العرفج
   - البريد: <EMAIL>
   - المنصب: مدير قسم البرامج
   - القسم: البرامج والمشاريع
   - الدور: MANAGER
   - الهاتف: 9665608194

7. ناصر اليحيى
   - البريد: <EMAIL>
   - المنصب: منسق البيئات
   - القسم: البرامج والمشاريع
   - الدور: SUPERVISOR
   - الهاتف: 966500977270
*/

-- دالة مساعدة لإنشاء ملف مستخدم (يجب استبدال USER_ID بالمعرف الفعلي من نظام المصادقة)
-- مثال لإنشاء ملف مستخدم:

-- أحمد الدريهم - مدير قسم التطوع
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
SELECT
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'أحمد الدريهم',
  '2d06f576-2625-46e4-849a-0e79b1db8d14', -- MANAGER role
  'c55f9a2d-9c98-4326-a076-cfb6786646fb', -- قسم التطوع
  true;

-- سعد الدريهم - المدير التنفيذي
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
VALUES (
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'سعد الدريهم',
  '1a6bfbe0-f9d0-4377-93fd-9411d01ce444', -- ADMIN role
  '6da8a045-c0ea-449a-8353-5ca70d5d6163', -- الإدارة التنفيذية
  true
);

-- سعد القحيز - مدير مشاريع
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
VALUES (
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'سعد القحيز',
  '2d06f576-2625-46e4-849a-0e79b1db8d14', -- MANAGER role
  '1289ad7b-5e1d-4eda-b68f-b2ab19274027', -- البرامج والمشاريع
  true
);

-- عبدالعزيز المطرد - مدير مشاريع
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
VALUES (
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'عبدالعزيز المطرد',
  '2d06f576-2625-46e4-849a-0e79b1db8d14', -- MANAGER role
  '1289ad7b-5e1d-4eda-b68f-b2ab19274027', -- البرامج والمشاريع
  true
);

-- عبدالله المحسن
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
SELECT 
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'عبدالله المحسن',
  r.id,
  d.id,
  true
FROM roles r, departments d
WHERE r.name = 'MANAGER' AND d.name = 'الموارد المالية';

-- معتصم العرفج
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
SELECT 
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'معتصم العرفج',
  r.id,
  d.id,
  true
FROM roles r, departments d
WHERE r.name = 'MANAGER' AND d.name = 'البرامج والمشاريع';

-- ناصر اليحيى
INSERT INTO user_profiles (id, full_name, role_id, department_id, is_active)
SELECT 
  'USER_ID_HERE', -- استبدل بالمعرف الفعلي من auth.users
  'ناصر اليحيى',
  r.id,
  d.id,
  true
FROM roles r, departments d
WHERE r.name = 'SUPERVISOR' AND d.name = 'البرامج والمشاريع';
*/

-- للتحقق من الأقسام والأدوار المتاحة:
SELECT 'الأقسام المتاحة:' as info;
SELECT id, name, description FROM departments ORDER BY name;

SELECT 'الأدوار المتاحة:' as info;
SELECT id, name, description FROM roles ORDER BY name;

-- للتحقق من المستخدمين الموجودين:
SELECT 'المستخدمين الحاليين:' as info;
SELECT 
  up.id,
  up.full_name,
  r.name as role,
  d.name as department,
  up.is_active
FROM user_profiles up
LEFT JOIN roles r ON up.role_id = r.id
LEFT JOIN departments d ON up.department_id = d.id
ORDER BY up.full_name;
