import { supabase } from './supabase';

export interface StrategicObjective {
  id: string;
  title: string;
  description?: string;
  owner: string;
  department: string;
  priority: string;
  status: string;
  progress: number;
  start_date?: string;
  end_date?: string;
  budget: number;
  expected_outcome?: string;
  success_metrics?: string;
  manager?: string;
  created_at: string;
  updated_at: string;
}

export class StrategicObjectiveService {
  // جلب جميع الأهداف الاستراتيجية
  static async getAllObjectives(): Promise<StrategicObjective[]> {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching strategic objectives:', error);
      throw error;
    }
  }

  // إنشاء هدف استراتيجي جديد
  static async createObjective(objectiveData: {
    title: string;
    description?: string;
    owner: string;
    department: string;
    priority?: string;
    start_date?: string;
    end_date?: string;
    budget?: number;
    expected_outcome?: string;
    success_metrics?: string;
  }): Promise<StrategicObjective> {
    try {
      const insertData = {
        title: objectiveData.title,
        description: objectiveData.description,
        owner: objectiveData.owner,
        department: objectiveData.department,
        priority: objectiveData.priority || 'متوسطة',
        status: 'جديد',
        progress: 0,
        start_date: objectiveData.start_date,
        end_date: objectiveData.end_date,
        budget: objectiveData.budget || 0,
        expected_outcome: objectiveData.expected_outcome,
        success_metrics: objectiveData.success_metrics
      };

      console.log('Creating objective with data:', insertData);

      const { data, error } = await supabase
        .from('strategic_objectives')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error creating strategic objective:', error);
      throw error;
    }
  }

  // تحديث هدف استراتيجي
  static async updateObjective(objectiveId: string, updates: Partial<StrategicObjective>): Promise<StrategicObjective> {
    try {
      // تنظيف البيانات وإزالة الحقول غير المطلوبة
      const cleanUpdates = {
        title: updates.title,
        description: updates.description,
        owner: updates.owner,
        department: updates.department,
        priority: updates.priority,
        status: updates.status,
        progress: updates.progress,
        start_date: updates.start_date,
        end_date: updates.end_date,
        budget: updates.budget,
        expected_outcome: updates.expected_outcome,
        success_metrics: updates.success_metrics,
        manager: updates.manager,
        updated_at: new Date().toISOString()
      };

      // إزالة الحقول الفارغة أو undefined
      Object.keys(cleanUpdates).forEach(key => {
        if (cleanUpdates[key] === undefined || cleanUpdates[key] === null) {
          delete cleanUpdates[key];
        }
      });

      console.log('Updating objective with data:', cleanUpdates);

      const { data, error } = await supabase
        .from('strategic_objectives')
        .update(cleanUpdates)
        .eq('id', objectiveId)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error updating strategic objective:', error);
      throw error;
    }
  }

  // حذف هدف استراتيجي
  static async deleteObjective(objectiveId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('strategic_objectives')
        .delete()
        .eq('id', objectiveId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting strategic objective:', error);
      throw error;
    }
  }

  // تحديث تقدم الهدف
  static async updateObjectiveProgress(objectiveId: string, progress: number): Promise<StrategicObjective> {
    try {
      // تحديد الحالة بناءً على التقدم
      let status = 'قيد التنفيذ';
      if (progress === 0) status = 'مخطط';
      else if (progress >= 100) status = 'مكتمل';
      else if (progress < 25) status = 'متأخر';

      const { data, error } = await supabase
        .from('strategic_objectives')
        .update({
          progress: Math.min(Math.max(progress, 0), 100),
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', objectiveId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating objective progress:', error);
      throw error;
    }
  }

  // جلب إحصائيات الأهداف الاستراتيجية
  static async getObjectiveStats(): Promise<{
    total: number;
    planned: number;
    inProgress: number;
    completed: number;
    delayed: number;
    averageProgress: number;
    totalBudget: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('status, progress, budget');

      if (error) throw error;

      const total = data?.length || 0;
      const planned = data?.filter(obj => obj.status === 'مخطط').length || 0;
      const inProgress = data?.filter(obj => obj.status === 'قيد التنفيذ').length || 0;
      const completed = data?.filter(obj => obj.status === 'مكتمل').length || 0;
      const delayed = data?.filter(obj => obj.status === 'متأخر').length || 0;

      const totalProgress = data?.reduce((sum, obj) => sum + (obj.progress || 0), 0) || 0;
      const averageProgress = total > 0 ? totalProgress / total : 0;

      const totalBudget = data?.reduce((sum, obj) => sum + (obj.budget || 0), 0) || 0;

      return {
        total,
        planned,
        inProgress,
        completed,
        delayed,
        averageProgress: Math.round(averageProgress * 100) / 100,
        totalBudget
      };
    } catch (error) {
      console.error('Error fetching objective stats:', error);
      return { total: 0, planned: 0, inProgress: 0, completed: 0, delayed: 0, averageProgress: 0, totalBudget: 0 };
    }
  }

  // جلب الأهداف حسب القسم
  static async getObjectivesByDepartment(): Promise<{ [department: string]: StrategicObjective[] }> {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('*')
        .order('department', { ascending: true });

      if (error) throw error;

      const groupedObjectives = (data || []).reduce((acc, objective) => {
        const department = objective.department || 'غير محدد';
        if (!acc[department]) acc[department] = [];
        acc[department].push(objective);
        return acc;
      }, {} as { [department: string]: StrategicObjective[] });

      return groupedObjectives;
    } catch (error) {
      console.error('Error fetching objectives by department:', error);
      return {};
    }
  }

  // جلب الأهداف حسب الأولوية
  static async getObjectivesByPriority(): Promise<{ [priority: string]: StrategicObjective[] }> {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('*')
        .order('priority', { ascending: true });

      if (error) throw error;

      const groupedObjectives = (data || []).reduce((acc, objective) => {
        const priority = objective.priority || 'متوسط';
        if (!acc[priority]) acc[priority] = [];
        acc[priority].push(objective);
        return acc;
      }, {} as { [priority: string]: StrategicObjective[] });

      return groupedObjectives;
    } catch (error) {
      console.error('Error fetching objectives by priority:', error);
      return {};
    }
  }

  // جلب هدف استراتيجي واحد
  static async getObjectiveById(objectiveId: string): Promise<StrategicObjective | null> {
    try {
      const { data, error } = await supabase
        .from('strategic_objectives')
        .select('*')
        .eq('id', objectiveId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching strategic objective:', error);
      return null;
    }
  }

  // جلب مؤشرات الأداء المتاحة للربط
  static async getAvailableKPIs(): Promise<Array<{ id: string; name: string; category: string }>> {
    try {
      const { data, error } = await supabase
        .from('kpis')
        .select('id, name, category')
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching available KPIs:', error);
      return [];
    }
  }
}
