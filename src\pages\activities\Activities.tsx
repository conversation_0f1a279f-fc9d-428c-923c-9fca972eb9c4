import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CheckSquare, Plus, Search, Filter, Calendar, User, Clock, AlertCircle, ChevronDown, MoreHorizontal, Edit, Trash2, Shield, <PERSON>r<PERSON><PERSON>ck, Eye, Sun, Moon, Save, Loader2, Brain } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import AIImport from '../../components/ai-import/AIImport';
import { supabase } from '../../lib/supabase';
import { UserService } from '../../lib/userService';
import { DepartmentService } from '../../lib/departmentService';
import { useNotifications } from '../../components/ui/NotificationSystem';
import { UserService } from '../../lib/userService';
import { DepartmentService } from '../../lib/departmentService';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../lib/database.types';

type Activity = Database['public']['Tables']['activities']['Row'];
type ActivityInsert = Database['public']['Tables']['activities']['Insert'];

// مكون النموذج المدمج
const InlineActivityForm = ({ editData, onSave, onCancel, onDataChange, availableUsers = [], availableDepartments = [] }) => {
  const [loading, setLoading] = useState(false);
  const notifications = useNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    initiative: editData?.initiative || '',
    relatedProject: editData?.related_project || '',
    department: editData?.department || '',
    assignee: editData?.assignee || '',
    status: editData?.status || 'لم يبدأ',
    priority: editData?.priority || 'متوسطة',
    startDate: editData?.start_date || '',
    endDate: editData?.end_date || '',
    progress: editData?.progress || 0,
    participants: editData?.participants || '',
    budget: editData?.budget || '',
    estimatedHours: editData?.estimated_hours || '',
    actualHours: editData?.actual_hours || 0
  });

  const [errors, setErrors] = useState({});

  // استخدام البيانات الممررة من المكون الأب

  // تحديث البيانات عند تغيير editData
  useEffect(() => {
    if (editData) {
      setFormData({
        title: editData?.title || '',
        description: editData?.description || '',
        initiative: editData?.initiative || '',
        relatedProject: editData?.related_project || '',
        department: editData?.department || '',
        assignee: editData?.assignee || '',
        status: editData?.status || 'لم يبدأ',
        priority: editData?.priority || 'متوسطة',
        startDate: editData?.start_date || '',
        endDate: editData?.end_date || '',
        progress: editData?.progress || 0,
        participants: editData?.participants || '',
        budget: editData?.budget || '',
        estimatedHours: editData?.estimated_hours || '',
        actualHours: editData?.actual_hours || 0
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان النشاط مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف النشاط مطلوب';
    }

    if (!formData.assignee.trim()) {
      newErrors.assignee = 'المسؤول عن النشاط مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'الإدارة المسؤولة مطلوبة';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية مطلوب';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        participants: formData.participants ? parseInt(formData.participants) : 0,
        estimatedHours: formData.estimatedHours ? parseInt(formData.estimatedHours) : 0,
        actualHours: parseInt(formData.actualHours.toString()),
        progress: parseInt(formData.progress.toString()),
        id: editData?.id || `activity-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      notifications.success(
        editData ? 'تم تحديث النشاط' : 'تم إضافة النشاط', 
        'تم حفظ البيانات بنجاح'
      );
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    // إشعار المكون الأب بوجود تغييرات غير محفوظة
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Control buttons at top */}
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ النشاط'}
        </button>
        <button 
          type="button" 
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      {/* Form content */}
      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              عنوان النشاط *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان النشاط"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف النشاط *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للنشاط"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* السطر الأول: المشروع والإدارة والمسؤول والأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                المشروع المرتبط *
              </label>
              <select
                value={formData.relatedProject || formData.initiative}
                onChange={(e) => handleChange('relatedProject', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="">اختر المشروع</option>
                <option value="مشروع تطوير المهارات الرقمية للشباب">مشروع تطوير المهارات الرقمية</option>
                <option value="مشروع دعم رواد الأعمال الشباب">مشروع دعم رواد الأعمال</option>
                <option value="مشروع التطوع المجتمعي الشبابي">مشروع التطوع المجتمعي</option>
                <option value="مشروع تعزيز الهوية الوطنية والتراث">مشروع تعزيز الهوية والتراث</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الإدارة المسؤولة *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الإدارة</option>
                {availableDepartments.map((dept, index) => (
                  <option key={index} value={dept}>{dept}</option>
                ))}
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                المسؤول عن النشاط *
              </label>
              <select
                value={formData.assignee}
                onChange={(e) => handleChange('assignee', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.assignee ? 'border-red-500' : ''}`}
              >
                <option value="">اختر المسؤول</option>
                {availableUsers.map((user, index) => (
                  <option key={index} value={user}>{user}</option>
                ))}
              </select>
              {errors.assignee && <p className="text-red-500 text-xs mt-1">{errors.assignee}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                مستوى الأولوية
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="عالية">عالية</option>
                <option value="متوسطة">متوسطة</option>
                <option value="منخفضة">منخفضة</option>
              </select>
            </div>
          </div>

          {/* السطر الثاني: الحالة والتواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                حالة النشاط
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="لم يبدأ">لم يبدأ</option>
                <option value="جاري التنفيذ">جاري التنفيذ</option>
                <option value="مكتمل">مكتمل</option>
                <option value="متأخر">متأخر</option>
                <option value="متعثر">متعثر</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.endDate ? 'border-red-500' : ''}`}
              />
              {errors.endDate && <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>}
            </div>
          </div>

          {/* السطر الثالث: المشاركين والميزانية والساعات */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                عدد المشاركين
              </label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => handleChange('participants', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الميزانية (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الساعات المقدرة
              </label>
              <input
                type="number"
                value={formData.estimatedHours}
                onChange={(e) => handleChange('estimatedHours', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الساعات الفعلية
              </label>
              <input
                type="number"
                value={formData.actualHours}
                onChange={(e) => handleChange('actualHours', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          {/* نسبة الإنجاز */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              نسبة الإنجاز (%)
            </label>
            <div className="space-y-2">
              <input
                type="range"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) => handleChange('progress', e.target.value)}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>0%</span>
                <span className="font-medium">{formData.progress}%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

const Activities = () => {
  const [searchParams] = useSearchParams();
  const [selectedItem, setSelectedItem] = useState(null);
  const [userRole, setUserRole] = useState('مدير');
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      if (saved !== null) {
        return JSON.parse(saved);
      }
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddMode, setIsAddMode] = useState(false);
  
  // تحديد activeView بناءً على معامل URL
  const tabParam = searchParams.get('tab');
  const initialView = tabParam === 'my-tasks' ? 'مهامي' : tabParam === 'ai-import' ? 'الاستيراد بالذكاء الصناعي' : 'الكل';
  const [activeView, setActiveView] = useState(initialView);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [projectFilter, setProjectFilter] = useState('');
  const [editingItem, setEditingItem] = useState(null);
  const [listWidth, setListWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('activitiesListWidth');
      return saved ? parseFloat(saved) : 50;
    }
    return 50;
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  
  const { user } = useAuth();
  const notifications = useNotifications();

  // State for data management
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableUsers, setAvailableUsers] = useState<string[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [availableProjects, setAvailableProjects] = useState<string[]>([]);

  // تحميل البيانات من قاعدة البيانات
  useEffect(() => {
    fetchActivities();
    fetchUsers();
    fetchDepartments();
    fetchProjects();
  }, []);

  const fetchUsers = async () => {
    try {
      const users = await UserService.getUserNames();
      setAvailableUsers(users);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const fetchDepartments = async () => {
    try {
      const departments = await DepartmentService.getDepartmentNames();
      setAvailableDepartments(departments);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('title')
        .order('title', { ascending: true });

      if (error) throw error;
      setAvailableProjects(data?.map(project => project.title) || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
    }
  };

  const fetchActivities = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('activities')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
      notifications.error('خطأ في تحميل البيانات', 'فشل في تحميل الأنشطة');
    } finally {
      setLoading(false);
    }
  };

  // دالة استيراد البيانات من الذكاء الصناعي
  const handleAIImport = async (importedData: any[]) => {
    try {
      // تنظيف البيانات وإضافة الحقول المطلوبة
      const cleanedData = importedData.map(item => {
        // تطبيع قيم الحالة
        let status = item.status || 'جاري التنفيذ';
        if (status === 'قيد التنفيذ' || status === 'مخطط') {
          status = 'جاري التنفيذ';
        } else if (status === 'ملغي' || status === 'متوقف') {
          status = 'متعثر';
        }

        // تطبيع قيم الأولوية
        let priority = item.priority || 'عالية';
        if (priority === 'متوسطة') {
          priority = 'عالية'; // تحويل متوسطة إلى عالية لأن متوسطة غير مدعومة
        }

        return {
          title: item.title || '',
          description: item.description || '',
          initiative: item.initiative || '',
          related_project: item.related_project || '',
          department: item.department || '',
          assignee: item.assignee || '',
          status: status,
          priority: priority,
          progress: Math.min(Math.max(parseInt(item.progress) || 0, 0), 100), // ضمان أن تكون بين 0 و 100
          start_date: item.start_date || null,
          end_date: item.end_date || null,
          budget: parseFloat(item.budget) || 0,
          participants: parseInt(item.participants) || 0,
          estimated_hours: parseInt(item.estimated_hours) || 0,
          actual_hours: parseInt(item.actual_hours) || 0
        };
      });

      // التحقق من المبادرات وإضافة المفقودة
      notifications.info('جاري المعالجة...', 'يتم التحقق من المبادرات وإضافة المفقودة');
      const newInitiatives = await checkAndAddMissingInitiatives(cleanedData);

      const { data, error } = await supabase
        .from('activities')
        .insert(cleanedData)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw new Error(`خطأ في قاعدة البيانات: ${error.message}`);
      }

      // تحديث القائمة المحلية
      setActivities(prev => [...(data || []), ...prev]);

      // العودة إلى تبويب جميع الأنشطة
      setActiveView('الكل');

      // رسالة نجاح مع معلومات المبادرات الجديدة
      if (newInitiatives.length > 0) {
        notifications.success(
          'تم الاستيراد بنجاح! 🎉',
          `تم استيراد ${importedData.length} نشاط بنجاح`
        );
        notifications.info(
          `تم إضافة ${newInitiatives.length} مبادرة جديدة 📋`,
          `المبادرات الجديدة:\n• ${newInitiatives.join('\n• ')}`
        );
      } else {
        notifications.success('تم الاستيراد', `تم استيراد ${importedData.length} نشاط بنجاح`);
      }
    } catch (error) {
      console.error('Error importing activities:', error);
      throw error;
    }
  };

  // دالة للتحقق من المبادرات وإضافة المفقودة
  const checkAndAddMissingInitiatives = async (activitiesData: any[]) => {
    try {
      // استخراج المبادرات الفريدة من البيانات المستوردة
      const initiatives = [...new Set(
        activitiesData
          .map(item => item.initiative)
          .filter(initiative => initiative && initiative.trim() !== '')
      )];

      if (initiatives.length === 0) return [];

      // التحقق من المبادرات الموجودة في قاعدة البيانات
      const { data: existingInitiatives, error: fetchError } = await supabase
        .from('initiatives')
        .select('title')
        .in('title', initiatives);

      if (fetchError) {
        console.error('Error fetching existing initiatives:', fetchError);
        return [];
      }

      // تحديد المبادرات المفقودة
      const existingNames = existingInitiatives?.map(p => p.title) || [];
      const missingInitiatives = initiatives.filter(initiative =>
        !existingNames.includes(initiative)
      );

      if (missingInitiatives.length === 0) return [];

      // إنشاء المبادرات الجديدة
      const newInitiatives = missingInitiatives.map(initiative => ({
        title: initiative,
        description: `مبادرة تم إنشاؤها تلقائياً من استيراد الأنشطة`,
        objective: 'تحقيق الأهداف الاستراتيجية',
        department: 'غير محدد',
        manager: 'غير محدد',
        status: 'جاري التنفيذ',
        priority: 'عالية',
        progress: 0,
        expected_outcome: 'نتائج إيجابية متوقعة',
        success_metrics: 'مؤشرات النجاح'
      }));

      // إدراج المبادرات الجديدة
      const { error: insertError } = await supabase
        .from('initiatives')
        .insert(newInitiatives);

      if (insertError) {
        console.error('Error inserting new initiatives:', insertError);
        notifications.warning(
          'تحذير',
          `تم استيراد الأنشطة بنجاح، لكن فشل في إضافة بعض المبادرات الجديدة`
        );
        return [];
      }

      return missingInitiatives;
    } catch (error) {
      console.error('Error in checkAndAddMissingInitiatives:', error);
      return [];
    }
  };

  // Initialize with mock data for now (will be replaced by database)
  const [mockActivities] = useState([
    // Mock data removed - using database
  ]);

  // تحديث activeView عند تغيير معامل URL
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'my-tasks') {
      setActiveView('مهامي');
    }
  }, [searchParams]);

  // تطبيق الوضع الليلي
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  // Handle resize
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = 100 - ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      if (newWidth >= 20 && newWidth <= 80) {
        setListWidth(newWidth);
      }
    };
    
    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('activitiesListWidth', listWidth.toString());
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // إدارة الصلاحيات
  const userRoles = {
    'مدير': {
      permissions: ['عرض', 'تعديل', 'حذف', 'إضافة', 'موافقة', 'تقارير'],
      color: 'text-red-400',
      icon: Shield
    },
    'منسق': {
      permissions: ['عرض', 'تعديل', 'إضافة', 'تقارير'],
      color: 'text-yellow-400',
      icon: UserCheck
    },
    'مستخدم': {
      permissions: ['عرض', 'تقارير'],
      color: 'text-green-400',
      icon: Eye
    }
  };

  // وظائف إدارة الأنشطة
  const handleSaveActivity = async (activityData: any) => {
    try {
      if (editingItem) {
        // تحديث نشاط موجود
        const { data, error } = await supabase
          .from('activities')
          .update({
            title: activityData.title,
            description: activityData.description,
            initiative: activityData.initiative,
            related_project: activityData.relatedProject,
            department: activityData.department,
            assignee: activityData.assignee,
            status: activityData.status,
            priority: activityData.priority,
            progress: activityData.progress,
            start_date: activityData.startDate,
            end_date: activityData.endDate,
            budget: activityData.budget,
            participants: activityData.participants,
            estimated_hours: activityData.estimatedHours,
            actual_hours: activityData.actualHours,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingItem.id)
          .select()
          .single();

        if (error) throw error;

        setActivities(prev =>
          prev.map(activity =>
            activity.id === editingItem.id ? data : activity
          )
        );
        setSelectedItem(data);
        notifications.success('تم التحديث', 'تم تحديث النشاط بنجاح');
      } else {
        // إنشاء نشاط جديد
        const { data, error } = await supabase
          .from('activities')
          .insert({
            title: activityData.title,
            description: activityData.description,
            initiative: activityData.initiative,
            related_project: activityData.relatedProject,
            department: activityData.department,
            assignee: activityData.assignee,
            status: activityData.status,
            priority: activityData.priority,
            progress: activityData.progress,
            start_date: activityData.startDate,
            end_date: activityData.endDate,
            budget: activityData.budget,
            participants: activityData.participants,
            estimated_hours: activityData.estimatedHours,
            actual_hours: activityData.actualHours
          })
          .select()
          .single();

        if (error) throw error;

        setActivities(prev => [data, ...prev]);
        setSelectedItem(data);
        notifications.success('تم الإضافة', 'تم إضافة النشاط بنجاح');
      }
      setIsEditMode(false);
      setIsAddMode(false);
      setEditingItem(null);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Error saving activity:', error);
      notifications.error('خطأ', 'فشل في حفظ النشاط');
      throw error;
    }
  };
  
  const handleDeleteActivity = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا النشاط؟')) {
      try {
        const { error } = await supabase
          .from('activities')
          .delete()
          .eq('id', id);

        if (error) throw error;

        setActivities(prev => prev.filter(activity => activity.id !== id));
        setSelectedItem(null);
        notifications.success('تم الحذف', 'تم حذف النشاط بنجاح');
      } catch (error) {
        console.error('Error deleting activity:', error);
        notifications.error('خطأ في الحذف', 'فشل في حذف النشاط');
      }
    }
  };
  
  // وظائف فتح النماذج
  const handleAddNew = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setSelectedItem(null);
    setEditingItem(null);
    setIsAddMode(true);
    setIsEditMode(false);
  };
  
  const handleEdit = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setEditingItem(item);
    setSelectedItem(item);
    setIsEditMode(true);
    setIsAddMode(false);
  };
  
  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    setIsEditMode(false);
    setIsAddMode(false);
    setEditingItem(null);
  };

  const handleItemClick = (item) => {
    if (hasUnsavedChanges) {
      if (confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟')) {
        setHasUnsavedChanges(false);
      } else {
        return;
      }
    }
    
    setSelectedItem(item);
    
    // إذا كنا في وضع التعديل، حدث العنصر المحرر إلى العنصر الجديد
    if (isEditMode) {
      setEditingItem(item);
    }
    // لا نغلق وضع التعديل - نبقيه مفتوحاً
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل': return 'bg-green-500';
      case 'جاري التنفيذ': return 'bg-blue-500';
      case 'متأخر': return 'bg-red-500';
      case 'لم يبدأ': return 'bg-gray-500';
      case 'متعثر': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'عالية': return 'text-red-400 bg-red-500/20';
      case 'متوسطة': return 'text-yellow-400 bg-yellow-500/20';
      case 'منخفضة': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const hasPermission = (permission) => {
    return userRoles[userRole]?.permissions.includes(permission);
  };

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      if (newMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    }
  };

  const isOverdue = (endDate, status) => {
    return new Date(endDate) < new Date() && status !== 'مكتمل';
  };

  // دالة تبديل حالة النشاط
  const toggleActivityCompletion = (activityId) => {
    setActivities(prev => 
      prev.map(activity => 
        activity.id === activityId 
          ? { 
              ...activity, 
              status: activity.status === 'مكتمل' ? 'جاري التنفيذ' : 'مكتمل',
              progress: activity.status === 'مكتمل' ? 75 : 100
            }
          : activity
      )
    );
  };

  // مكون العنصر في القائمة المبسط للمهام
  const TaskListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    const isCompleted = item.status === 'مكتملة' || item.status === 'مكتمل';
    const isOverdueTask = new Date(item.endDate) < new Date() && !isCompleted;
    
    return (
      <div 
        className={`p-3 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start gap-3">
          {/* Checkbox */}
          <button
            className={`mt-0.5 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
              isCompleted 
                ? 'bg-green-500 border-green-500 text-white' 
                : `border-gray-400 hover:border-green-500 ${isDarkMode ? 'hover:bg-green-500/10' : 'hover:bg-green-50'}`
            }`}
            onClick={(e) => {
              e.stopPropagation();
              toggleActivityCompletion(item.id);
            }}
          >
            {isCompleted && (
              <span className="text-white font-bold">✓</span>
            )}
          </button>
          
          <div className="flex-1 min-w-0">
            {/* عنوان المهمة */}
            <div className="flex items-center gap-2 mb-1">
              <h3 className={`font-medium text-sm ${
                isCompleted 
                  ? 'line-through text-gray-500 dark:text-gray-400' 
                  : 'text-gray-900 dark:text-white'
              }`}>
                {item.title}
              </h3>
              {isBeingEdited && (
                <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                  قيد التحرير
                </span>
              )}
              {isOverdueTask && (
                <AlertCircle className="w-4 h-4 text-red-500" />
              )}
            </div>
            
            {/* سطر واحد للمعلومات الأساسية */}
            <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-400">
              <span className={`px-2 py-0.5 rounded ${getPriorityColor(item.priority)}`}>
                {item.priority}
              </span>
              <span>
                {new Date(item.endDate).toLocaleDateString('ar-SA')}
              </span>
              <span>
                {item.relatedProject || item.initiative || item.project || 'غير محدد'}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // مكون العنصر في القائمة
  const ListItem = ({ item, onClick, isSelected }) => {
    const isBeingEdited = isEditMode && editingItem?.id === item.id;
    const isCompleted = item.status === 'مكتمل';
    
    return (
      <div 
        className={`p-4 cursor-pointer border-b border-gray-200 dark:border-gray-700 transition-colors ${
          isBeingEdited 
            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' 
            : isSelected 
              ? 'bg-blue-50 dark:bg-gray-700/50 border-l-4 border-l-blue-500' 
              : 'hover:bg-gray-50 dark:hover:bg-gray-700/30'
        }`}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-start gap-3">
          {/* Checkbox للنشاط */}
          <button
            className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
              isCompleted 
                ? 'bg-green-500 border-green-500 text-white' 
                : `border-gray-400 hover:border-green-500 ${isDarkMode ? 'hover:bg-green-500/10' : 'hover:bg-green-50'}`
            }`}
            onClick={(e) => {
              e.stopPropagation();
              toggleActivityCompletion(item.id);
            }}
          >
            {isCompleted && (
              <span className="text-white font-bold">✓</span>
            )}
          </button>

          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <h3 className={`font-medium text-sm ${
                  isCompleted 
                    ? 'line-through text-gray-500 dark:text-gray-400' 
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {item.title}
                </h3>
                {isBeingEdited && (
                  <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded">
                    قيد التحرير
                  </span>
                )}
                {isOverdue(item.endDate, item.status) && (
                  <AlertCircle className="w-3 h-3 text-red-500" />
                )}
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(item.priority)}`}>
                  {item.priority}
                </span>
                <span className={`text-xs px-2 py-1 rounded ${getStatusColor(item.status)} text-white`}>
                  {item.status}
                </span>
              </div>
            </div>
            
            <p className={`text-xs mb-2 line-clamp-2 ${
              isCompleted 
                ? 'text-gray-500 dark:text-gray-500' 
                : 'text-gray-600 dark:text-gray-400'
            }`}>
              {item.description}
            </p>
            
            <div className="mb-2">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600 dark:text-gray-400">التقدم</span>
                <span className="text-gray-900 dark:text-white">{item.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div 
                  className="bg-blue-500 h-1 rounded-full transition-all"
                  style={{ width: `${item.progress}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-gray-600 dark:text-gray-400 text-xs">
              المسؤول: {item.assignee}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // مكون لوحة التفاصيل
  const DetailPanel = () => {
    // إذا كنا في وضع الإضافة أو التعديل، اعرض النموذج مباشرة
    if (isAddMode || isEditMode) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          <InlineActivityForm
            editData={editingItem}
            onSave={handleSaveActivity}
            onCancel={handleCancelEdit}
            onDataChange={setHasUnsavedChanges}
            availableUsers={availableUsers}
            availableDepartments={availableDepartments}
          />
        </div>
      );
    }

    if (!selectedItem) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <CheckSquare className="text-gray-400 dark:text-gray-500" size={40} />
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">اختر نشاط لعرض التفاصيل</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">اختر نشاط من القائمة لعرض المعلومات التفصيلية</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-orange-500">
                <CheckSquare size={24} />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-gray-900 dark:text-white text-xl font-bold">
                    {selectedItem?.title}
                  </h2>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(selectedItem.status)} text-white`}>
                    {selectedItem.status}
                  </span>
                  {isOverdue(selectedItem.endDate, selectedItem.status) && (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-400">الأولوية:</span>
                    <span className={`px-2 py-0.5 rounded text-xs ${getPriorityColor(selectedItem.priority)}`}>
                      {selectedItem.priority}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {new Date(selectedItem.endDate).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">{selectedItem.assignee}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasPermission('تعديل') && (
                <>
                  <button 
                    onClick={() => handleEdit(selectedItem)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                    title="تعديل"
                  >
                    <Edit size={18} />
                  </button>
                  <button 
                    onClick={() => handleDeleteActivity(selectedItem.id)}
                    className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-red-500"
                    title="حذف"
                  >
                    <Trash2 size={18} />
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* التقدم العام */}
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-gray-600 dark:text-gray-400 text-sm">التقدم العام</span>
                <span className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.progress}%</span>
              </div>
              <div className="flex items-center gap-1">
                {selectedItem.progress < 30 && <AlertCircle className="w-4 h-4 text-red-500" />}
                {selectedItem.progress >= 30 && selectedItem.progress < 70 && <Clock className="w-4 h-4 text-yellow-500" />}
                {selectedItem.progress >= 70 && <CheckSquare className="w-4 h-4 text-green-500" />}
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all ${
                  selectedItem.progress < 30 ? 'bg-red-500' :
                  selectedItem.progress < 70 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${selectedItem.progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Content - عرض المعلومات مباشرة */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4">
            <div className="space-y-6">
              {/* الوصف */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-2">الوصف</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>

              {/* الإحصائيات الرئيسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">الإحصائيات الرئيسية</h4>
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {selectedItem.progress}%
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">نسبة الإنجاز</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">التقدم</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {selectedItem.participants || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">المشاركين</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">عدد</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {selectedItem.budget ? (selectedItem.budget / 1000).toFixed(0) : 0}K
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الميزانية</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">ريال</div>
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {selectedItem.actualHours || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">الساعات المنجزة</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">ساعة</div>
                  </div>
                </div>
              </div>

              {/* المعلومات الأساسية */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h4 className="text-gray-900 dark:text-white font-medium mb-3">المعلومات الأساسية</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">المشروع المرتبط</span>
                      <div className="flex items-center gap-2 mt-1">
                        <CheckSquare className="w-4 h-4 text-purple-500" />
                        <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.relatedProject || selectedItem.initiative}</p>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الإدارة المسؤولة</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.department}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">المسؤول</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.assignee}</p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">الحالة الزمنية</span>
                      <p className="text-gray-900 dark:text-white text-sm font-medium">
                        {isOverdue(selectedItem.endDate, selectedItem.status)
                          ? 'متأخر عن الموعد'
                          : `${Math.ceil((new Date(selectedItem.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم متبقي`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 text-xs block">الجدول الزمني</span>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-gray-900 dark:text-white text-sm">
                        {new Date(selectedItem.startDate).toLocaleDateString('ar-SA')}
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-400 rotate-90" />
                      <span className="text-gray-900 dark:text-white text-sm">
                        {new Date(selectedItem.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* التفاصيل التقنية */}
              {(selectedItem.estimatedHours || selectedItem.actualHours) && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <h4 className="text-gray-900 dark:text-white font-medium mb-3">التفاصيل التقنية</h4>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400 text-xs block">الساعات المقدرة</span>
                        <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.estimatedHours || 0} ساعة</p>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400 text-xs block">الساعات الفعلية</span>
                        <p className="text-gray-900 dark:text-white text-sm font-medium">{selectedItem.actualHours || 0} ساعة</p>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs block">كفاءة التنفيذ</span>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              selectedItem.actual_hours <= selectedItem.estimated_hours ? 'bg-green-500' : 'bg-yellow-500'
                            }`}
                            style={{ 
                              width: `${Math.min(100, (selectedItem.actual_hours / selectedItem.estimated_hours) * 100)}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {selectedItem.estimated_hours ? 
                            `${((selectedItem.actual_hours / selectedItem.estimated_hours) * 100).toFixed(0)}%` : 
                            '0%'
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // دوال مساعدة للتواريخ
  const isToday = (date) => {
    const today = new Date();
    const targetDate = new Date(date);
    return targetDate.toDateString() === today.toDateString();
  };

  const isThisWeek = (date) => {
    const today = new Date();
    const targetDate = new Date(date);
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return targetDate >= startOfWeek && targetDate <= endOfWeek;
  };

  // فلترة الأنشطة
  const filteredActivities = activities.filter(activity => {
    // فلترة حسب العرض
    let viewFilter = false;
    switch(activeView) {
      case 'مهامي':
        // المهام الخاصة بالمستخدم الحالي
        const currentUserName = user?.user_metadata?.full_name || user?.email || '<EMAIL>';
        viewFilter = activity.assignee === currentUserName ||
                    activity.assignee === user?.email ||
                    activity.assignee === '<EMAIL>'; // للحساب الافتراضي
        break;
      case 'الكل': viewFilter = true; break;
      case 'جاري التنفيذ': viewFilter = activity.status === 'جاري التنفيذ'; break;
      case 'مكتملة': viewFilter = activity.status === 'مكتمل'; break;
      case 'متأخرة': viewFilter = activity.status === 'متأخر' || isOverdue(activity.end_date, activity.status); break;
      default: viewFilter = true;
    }
    
    // فلترة حسب البحث
    const searchFilter = !searchTerm || 
      activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.assignee.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلترة حسب الحالة
    const statusMatch = !statusFilter || activity.status === statusFilter;
    
    // فلترة حسب الأولوية  
    const priorityMatch = !priorityFilter || activity.priority === priorityFilter;
    
    // فلترة حسب الإدارة
    const departmentMatch = !departmentFilter || activity.department === departmentFilter;
    
    // فلترة حسب المشروع المرتبط
    const projectMatch = !projectFilter || 
      activity.related_project === projectFilter || 
      activity.initiative === projectFilter;
    
    return viewFilter && searchFilter && statusMatch && priorityMatch && departmentMatch && projectMatch;
  });

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">الأنشطة التشغيلية</h1>
        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">إدارة ومتابعة الأنشطة والمهام التشغيلية</p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex overflow-x-auto">
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'مهامي' ? 'border-green-500 text-green-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('مهامي')}
          >
            مهامي
          </button>
          <button
            className={`px-6 py-3 border-b-2 ${activeView === 'الكل' ? 'border-orange-500 text-orange-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('الكل')}
          >
            جميع الأنشطة
          </button>
          <button
            className={`px-6 py-3 border-b-2 ${activeView === 'الاستيراد بالذكاء الصناعي' ? 'border-purple-500 text-purple-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap flex items-center gap-2`}
            onClick={() => setActiveView('الاستيراد بالذكاء الصناعي')}
          >
            <Brain className="w-4 h-4" />
            الاستيراد بالذكاء الصناعي
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'جاري التنفيذ' ? 'border-orange-500 text-orange-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('جاري التنفيذ')}
          >
            قيد التنفيذ
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'مكتملة' ? 'border-orange-500 text-orange-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('مكتملة')}
          >
            مكتملة
          </button>
          <button 
            className={`px-6 py-3 border-b-2 ${activeView === 'متأخرة' ? 'border-orange-500 text-orange-500' : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'} whitespace-nowrap`}
            onClick={() => setActiveView('متأخرة')}
          >
            متأخرة
          </button>
        </div>
        
        {/* Add Button */}
        <div className="px-4">
          {hasPermission('إضافة') && (
            <button 
              onClick={handleAddNew}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
            >
              <Plus size={16} />
              جديد
            </button>
          )}
        </div>
      </div>

      {/* Content based on active view */}
      {activeView === 'الاستيراد بالذكاء الصناعي' ? (
        <AIImport
          dataType="activities"
          onImport={handleAIImport}
        />
      ) : (
        /* Split View Content */
        <div className="flex gap-6 h-[calc(100vh-250px)]" ref={containerRef}>
          {/* List Panel */}
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col"
            style={{ width: `${listWidth}%` }}
          >
            {/* Search Header */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              {/* البحث والفلاتر في صف واحد */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400" size={16} />
                  <input 
                    type="text" 
                    placeholder="البحث..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg pl-10 pr-4 py-2 focus:outline-none text-sm"
                  />
                </div>
                
                {/* الفلاتر */}
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[100px]"
                >
                  <option value="">الحالة</option>
                  <option value="مكتمل">مكتمل</option>
                  <option value="جاري التنفيذ">جاري التنفيذ</option>
                  <option value="متأخر">متأخر</option>
                  <option value="لم يبدأ">لم يبدأ</option>
                  <option value="متعثر">متعثر</option>
                </select>
                
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[100px]"
                >
                  <option value="">الأولوية</option>
                  <option value="عالية">عالية</option>
                  <option value="متوسطة">متوسطة</option>
                  <option value="منخفضة">منخفضة</option>
                </select>
                
                {listWidth > 35 && (
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[120px]"
                  >
                    <option value="">الإدارة</option>
                    {availableDepartments.map((dept, index) => (
                      <option key={index} value={dept}>{dept}</option>
                    ))}
                  </select>
                )}
                
                {listWidth > 45 && (
                  <select
                    value={projectFilter}
                    onChange={(e) => setProjectFilter(e.target.value)}
                    className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none min-w-[140px]"
                  >
                    <option value="">المشروع المرتبط</option>
                    {availableProjects.map((project, index) => (
                      <option key={index} value={project}>{project}</option>
                    ))}
                  </select>
                )}
              </div>
            </div>
            
            {/* List Items */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="mr-2 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {filteredActivities.map(activity => (
                    activeView === 'مهامي' ? (
                      <TaskListItem 
                        key={activity.id} 
                        item={activity} 
                        onClick={handleItemClick}
                        isSelected={selectedItem?.id === activity.id}
                      />
                    ) : (
                      <ListItem 
                        key={activity.id} 
                        item={activity} 
                        onClick={handleItemClick}
                        isSelected={selectedItem?.id === activity.id}
                      />
                    )
                  ))}
                  {filteredActivities.length === 0 && !loading && (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">لا توجد أنشطة مطابقة للبحث</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Resize Handle */}
          <div 
            className={`relative w-1 hover:w-2 cursor-col-resize transition-all group ${
              isResizing 
                ? 'bg-blue-500' 
                : 'bg-gray-300 dark:bg-gray-700 hover:bg-blue-500'
            }`}
            onMouseDown={() => setIsResizing(true)}
          >
            <div className={`absolute inset-y-0 -left-2 -right-2 ${isResizing ? 'cursor-col-resize' : ''}`} />
          </div>

          {/* Detail Panel */}
          <div className="flex-1 overflow-hidden">
            <DetailPanel />
          </div>
        </div>
      )}

      {/* Role Switcher */}
      <div className="fixed bottom-4 right-4 z-20">
        <select 
          value={userRole}
          onChange={(e) => setUserRole(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500"
        >
          <option value="مدير">مدير</option>
          <option value="منسق">منسق</option>
          <option value="مستخدم">مستخدم</option>
        </select>
      </div>
    </div>
  );
};

export default Activities;