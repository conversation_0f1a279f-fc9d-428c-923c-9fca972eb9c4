import React, { useState, useEffect } from 'react';
import { Save, Loader2 } from 'lucide-react';

interface ObjectiveFormProps {
  editData?: any;
  onSave: (data: any) => Promise<void>;
  onCancel: () => void;
  onDataChange?: (hasChanges: boolean) => void;
  availableKPIs: string[];
}

const ObjectiveForm: React.FC<ObjectiveFormProps> = ({
  editData,
  onSave,
  onCancel,
  onDataChange,
  availableKPIs
}) => {
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    owner: editData?.owner || '',
    department: editData?.department || '',
    priority: editData?.priority || 'متوسطة',
    startDate: editData?.start_date || '',
    endDate: editData?.end_date || '',
    budget: editData?.budget || '',
    expectedOutcome: editData?.expected_outcome || '',
    successMetrics: editData?.success_metrics || '',
    manager: editData?.manager || '',
    status: editData?.status || 'جديد',
    progress: editData?.progress || 0,
    relatedKPIs: editData?.relatedKPIs || []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // تحديث البيانات عند تغيير editData
  useEffect(() => {
    if (editData) {
      setFormData({
        title: editData?.title || '',
        description: editData?.description || '',
        owner: editData?.owner || '',
        department: editData?.department || '',
        priority: editData?.priority || 'متوسطة',
        startDate: editData?.start_date || '',
        endDate: editData?.end_date || '',
        budget: editData?.budget || '',
        expectedOutcome: editData?.expected_outcome || '',
        successMetrics: editData?.success_metrics || '',
        manager: editData?.manager || '',
        status: editData?.status || 'جديد',
        progress: editData?.progress || 0,
        relatedKPIs: editData?.relatedKPIs || []
      });
    }
  }, [editData]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الهدف مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف الهدف مطلوب';
    }

    if (!formData.owner.trim()) {
      newErrors.owner = 'المسؤول عن الهدف مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'القسم المسؤول مطلوب';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية مطلوب';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const saveData = {
        title: formData.title,
        description: formData.description,
        owner: formData.owner,
        department: formData.department,
        priority: formData.priority,
        status: formData.status,
        progress: formData.progress,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        start_date: formData.startDate,
        end_date: formData.endDate,
        expected_outcome: formData.expectedOutcome,
        success_metrics: formData.successMetrics,
        manager: formData.manager
      };

      console.log('Saving objective with data:', saveData);
      await onSave(saveData);
    } catch (error) {
      console.error('Error in form submission:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (onDataChange) {
      onDataChange(true);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* أزرار التحكم */}
      <div className="flex gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          type="submit"
          onClick={handleSubmit}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {loading ? 'جاري الحفظ...' : 'حفظ الهدف'}
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
        >
          إلغاء
        </button>
      </div>

      {/* محتوى النموذج */}
      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* العنوان */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عنوان الهدف *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان الهدف الاستراتيجي"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              وصف الهدف *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للهدف الاستراتيجي"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* المسؤول والقسم */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المسؤول عن الهدف *
              </label>
              <input
                type="text"
                value={formData.owner}
                onChange={(e) => handleChange('owner', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none ${errors.owner ? 'border-red-500' : ''}`}
                placeholder="اسم المسؤول"
              />
              {errors.owner && <p className="text-red-500 text-xs mt-1">{errors.owner}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القسم المسؤول *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر القسم</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>
          </div>

          {/* الأولوية والميزانية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مستوى الأولوية
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="عالي">عالي</option>
                <option value="متوسط">متوسط</option>
                <option value="منخفض">منخفض</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الميزانية المقدرة (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => handleChange('budget', e.target.value)}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          {/* التواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={`w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none ${errors.endDate ? 'border-red-500' : ''}`}
              />
              {errors.endDate && <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>}
            </div>
          </div>

          {/* النتائج المتوقعة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              النتائج المتوقعة
            </label>
            <textarea
              value={formData.expectedOutcome}
              onChange={(e) => handleChange('expectedOutcome', e.target.value)}
              rows={2}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="النتائج والمخرجات المتوقعة من تحقيق هذا الهدف"
            />
          </div>

          {/* مؤشرات النجاح */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              مؤشرات النجاح
            </label>
            <textarea
              value={formData.successMetrics}
              onChange={(e) => handleChange('successMetrics', e.target.value)}
              rows={2}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none"
              placeholder="المؤشرات التي ستقيس نجاح تحقيق الهدف"
            />
          </div>

          {/* مؤشرات الأداء المرتبطة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              مؤشرات الأداء المرتبطة (KPIs)
            </label>
            <div className="space-y-2">
              {/* قائمة المؤشرات المختارة */}
              {formData.relatedKPIs.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.relatedKPIs.map((kpi, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 rounded-full text-sm"
                    >
                      {kpi}
                      <button
                        type="button"
                        onClick={() => {
                          const newKPIs = formData.relatedKPIs.filter((_, i) => i !== index);
                          handleChange('relatedKPIs', newKPIs);
                        }}
                        className="ml-1 hover:text-blue-600 dark:hover:text-blue-400"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* قائمة منسدلة لاختيار المؤشرات */}
              <select
                onChange={(e) => {
                  if (e.target.value && !formData.relatedKPIs.includes(e.target.value)) {
                    handleChange('relatedKPIs', [...formData.relatedKPIs, e.target.value]);
                    e.target.value = '';
                  }
                }}
                className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 focus:border-blue-500 text-gray-900 dark:text-white rounded-lg px-4 py-2 focus:outline-none"
              >
                <option value="">اختر مؤشر أداء لإضافته</option>
                {availableKPIs.map((kpi, index) => (
                  <option key={index} value={kpi}>{kpi}</option>
                ))}
              </select>
            </div>
          </div>

        </form>
      </div>
    </div>
  );
};

export default ObjectiveForm;
