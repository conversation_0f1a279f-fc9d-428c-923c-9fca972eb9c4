import React, { useState, useEffect } from 'react';
import { Settings, User, Database, Shield, CheckCircle, AlertTriangle, Loader2, RefreshCw } from 'lucide-react';
import { initializeDefaultAdmin, DEFAULT_ADMIN, resetSystem } from '../../lib/initializeAdmin';
import { UserService } from '../../lib/userService';
import { useNotifications } from '../../components/ui/NotificationSystem';

const SystemSetup = () => {
  const [loading, setLoading] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    adminExists: false,
    rolesCreated: false,
    departmentsCreated: false,
    databaseConnected: false
  });
  const [setupComplete, setSetupComplete] = useState(false);
  const notifications = useNotifications();

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    try {
      setLoading(true);
      
      // فحص الاتصال بقاعدة البيانات
      const roles = await UserService.getRoles();
      const departments = await UserService.getDepartments();
      const users = await UserService.getAllUsers();
      
      const adminUser = users.find(user => user.email === DEFAULT_ADMIN.email);
      
      setSystemStatus({
        databaseConnected: true,
        adminExists: !!adminUser,
        rolesCreated: roles.length > 0,
        departmentsCreated: departments.length > 0
      });

      const isComplete = adminUser && roles.length > 0 && departments.length > 0;
      setSetupComplete(!!isComplete);
      
    } catch (error) {
      console.error('Error checking system status:', error);
      setSystemStatus({
        adminExists: false,
        rolesCreated: false,
        departmentsCreated: false,
        databaseConnected: false
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInitializeSystem = async () => {
    try {
      setLoading(true);
      notifications.info('جاري الإعداد', 'يتم إعداد النظام...');

      const success = await initializeDefaultAdmin();
      
      if (success) {
        notifications.success('تم الإعداد', 'تم إعداد النظام بنجاح');
        await checkSystemStatus();
      } else {
        notifications.error('خطأ', 'فشل في إعداد النظام');
      }
    } catch (error) {
      console.error('Setup error:', error);
      notifications.error('خطأ', 'حدث خطأ أثناء الإعداد');
    } finally {
      setLoading(false);
    }
  };

  const handleResetSystem = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
      return;
    }

    try {
      setLoading(true);
      notifications.info('جاري الإعادة', 'يتم إعادة تعيين النظام...');

      const success = await resetSystem();
      
      if (success) {
        notifications.success('تم الإعادة', 'تم إعادة تعيين النظام بنجاح');
        await checkSystemStatus();
      } else {
        notifications.error('خطأ', 'فشل في إعادة تعيين النظام');
      }
    } catch (error) {
      console.error('Reset error:', error);
      notifications.error('خطأ', 'حدث خطأ أثناء إعادة التعيين');
    } finally {
      setLoading(false);
    }
  };

  const StatusItem = ({ icon: Icon, title, status, description }) => (
    <div className="flex items-center gap-4 p-4 bg-white rounded-lg border">
      <div className={`p-2 rounded-lg ${status ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
        <Icon className="w-5 h-5" />
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="flex items-center gap-2">
        {status ? (
          <CheckCircle className="w-5 h-5 text-green-500" />
        ) : (
          <AlertTriangle className="w-5 h-5 text-red-500" />
        )}
        <span className={`text-sm font-medium ${status ? 'text-green-600' : 'text-red-600'}`}>
          {status ? 'مكتمل' : 'غير مكتمل'}
        </span>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <Settings className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">إعداد نظام التخطيط الاستراتيجي</h1>
          <p className="text-gray-600">مرحباً بك! يرجى إكمال إعداد النظام للبدء</p>
        </div>

        {/* حالة النظام */}
        <div className="bg-white rounded-xl shadow-sm border p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">حالة النظام</h2>
            <button
              onClick={checkSystemStatus}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
          </div>

          <div className="space-y-4">
            <StatusItem
              icon={Database}
              title="الاتصال بقاعدة البيانات"
              status={systemStatus.databaseConnected}
              description="التحقق من الاتصال بـ Supabase"
            />
            <StatusItem
              icon={User}
              title="المستخدم الإداري"
              status={systemStatus.adminExists}
              description="إنشاء حساب المدير الافتراضي"
            />
            <StatusItem
              icon={Shield}
              title="الأدوار والصلاحيات"
              status={systemStatus.rolesCreated}
              description="إعداد أدوار المستخدمين"
            />
            <StatusItem
              icon={Settings}
              title="الأقسام والهيكل التنظيمي"
              status={systemStatus.departmentsCreated}
              description="إنشاء الأقسام الأساسية"
            />
          </div>
        </div>

        {/* بيانات الدخول */}
        {setupComplete && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <CheckCircle className="w-6 h-6 text-green-600" />
              <h3 className="text-lg font-semibold text-green-900">النظام جاهز للاستخدام!</h3>
            </div>
            <div className="bg-white rounded-lg p-4 border border-green-200">
              <h4 className="font-medium text-gray-900 mb-3">بيانات دخول المدير:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">البريد الإلكتروني:</span>
                  <div className="font-mono bg-gray-100 p-2 rounded mt-1">{DEFAULT_ADMIN.email}</div>
                </div>
                <div>
                  <span className="text-gray-600">كلمة المرور:</span>
                  <div className="font-mono bg-gray-100 p-2 rounded mt-1">{DEFAULT_ADMIN.password}</div>
                </div>
              </div>
              <p className="text-sm text-amber-600 mt-3">
                ⚠️ يُنصح بتغيير كلمة المرور بعد تسجيل الدخول لأول مرة
              </p>
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex flex-col sm:flex-row gap-4">
          {!setupComplete && (
            <button
              onClick={handleInitializeSystem}
              disabled={loading}
              className="flex-1 flex items-center justify-center gap-3 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Settings className="w-5 h-5" />
              )}
              إعداد النظام
            </button>
          )}
          
          <button
            onClick={handleResetSystem}
            disabled={loading}
            className="flex items-center justify-center gap-3 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <RefreshCw className="w-5 h-5" />
            )}
            إعادة تعيين النظام
          </button>
        </div>

        {setupComplete && (
          <div className="text-center mt-6">
            <a
              href="/login"
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium"
            >
              الانتقال إلى صفحة تسجيل الدخول ←
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemSetup;
