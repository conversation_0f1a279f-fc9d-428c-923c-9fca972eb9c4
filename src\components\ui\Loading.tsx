import { motion } from 'framer-motion';

const Loading = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-neutral-50">
      <div className="flex flex-col items-center gap-4">
        <div className="flex gap-2">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              animate={{
                y: ["0%", "-50%", "0%"],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut",
                delay: i * 0.2,
              }}
              className="w-3 h-3 rounded-full bg-primary-600"
            />
          ))}
        </div>
        <p className="text-neutral-600 font-medium">جاري التحميل...</p>
      </div>
    </div>
  );
};

export default Loading;