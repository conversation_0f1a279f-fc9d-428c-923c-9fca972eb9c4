import { supabase } from './supabase';

export interface Initiative {
  id: string;
  title: string;
  description?: string;
  objective?: string;
  related_kpi?: string;
  department: string;
  manager: string;
  priority: string;
  status: string;
  progress: number;
  start_date?: string;
  end_date?: string;
  budget: number;
  participants: number;
  expected_outcome?: string;
  success_metrics?: string;
  created_at: string;
  updated_at: string;
}

export class InitiativeService {
  // جلب جميع المبادرات
  static async getAllInitiatives(): Promise<Initiative[]> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching initiatives:', error);
      throw error;
    }
  }

  // إنشاء مبادرة جديدة
  static async createInitiative(initiativeData: {
    title: string;
    description?: string;
    objective?: string;
    department: string;
    manager: string;
    priority?: string;
    start_date?: string;
    end_date?: string;
    budget?: number;
    expected_outcome?: string;
  }): Promise<Initiative> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .insert({
          title: initiativeData.title,
          description: initiativeData.description,
          objective: initiativeData.objective,
          department: initiativeData.department,
          manager: initiativeData.manager,
          priority: initiativeData.priority || 'متوسط',
          status: 'مخطط',
          progress: 0,
          start_date: initiativeData.start_date,
          end_date: initiativeData.end_date,
          budget: initiativeData.budget || 0,
          participants: 0,
          expected_outcome: initiativeData.expected_outcome
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating initiative:', error);
      throw error;
    }
  }

  // تحديث مبادرة
  static async updateInitiative(initiativeId: string, updates: Partial<Initiative>): Promise<Initiative> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', initiativeId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating initiative:', error);
      throw error;
    }
  }

  // حذف مبادرة
  static async deleteInitiative(initiativeId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('initiatives')
        .delete()
        .eq('id', initiativeId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting initiative:', error);
      throw error;
    }
  }

  // تحديث تقدم المبادرة
  static async updateInitiativeProgress(initiativeId: string, progress: number): Promise<Initiative> {
    try {
      // تحديد الحالة بناءً على التقدم
      let status = 'قيد التنفيذ';
      if (progress === 0) status = 'مخطط';
      else if (progress >= 100) status = 'مكتمل';
      else if (progress < 25) status = 'متأخر';

      const { data, error } = await supabase
        .from('initiatives')
        .update({
          progress: Math.min(Math.max(progress, 0), 100),
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', initiativeId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating initiative progress:', error);
      throw error;
    }
  }

  // جلب إحصائيات المبادرات
  static async getInitiativeStats(): Promise<{
    total: number;
    planned: number;
    inProgress: number;
    completed: number;
    delayed: number;
    averageProgress: number;
    totalBudget: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('status, progress, budget');

      if (error) throw error;

      const total = data?.length || 0;
      const planned = data?.filter(init => init.status === 'مخطط').length || 0;
      const inProgress = data?.filter(init => init.status === 'قيد التنفيذ').length || 0;
      const completed = data?.filter(init => init.status === 'مكتمل').length || 0;
      const delayed = data?.filter(init => init.status === 'متأخر').length || 0;

      const totalProgress = data?.reduce((sum, init) => sum + (init.progress || 0), 0) || 0;
      const averageProgress = total > 0 ? totalProgress / total : 0;

      const totalBudget = data?.reduce((sum, init) => sum + (init.budget || 0), 0) || 0;

      return {
        total,
        planned,
        inProgress,
        completed,
        delayed,
        averageProgress: Math.round(averageProgress * 100) / 100,
        totalBudget
      };
    } catch (error) {
      console.error('Error fetching initiative stats:', error);
      return { total: 0, planned: 0, inProgress: 0, completed: 0, delayed: 0, averageProgress: 0, totalBudget: 0 };
    }
  }

  // جلب المبادرات حسب القسم
  static async getInitiativesByDepartment(): Promise<{ [department: string]: Initiative[] }> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('*')
        .order('department', { ascending: true });

      if (error) throw error;

      const groupedInitiatives = (data || []).reduce((acc, initiative) => {
        const department = initiative.department || 'غير محدد';
        if (!acc[department]) acc[department] = [];
        acc[department].push(initiative);
        return acc;
      }, {} as { [department: string]: Initiative[] });

      return groupedInitiatives;
    } catch (error) {
      console.error('Error fetching initiatives by department:', error);
      return {};
    }
  }

  // جلب المبادرات حسب الحالة
  static async getInitiativesByStatus(): Promise<{ [status: string]: Initiative[] }> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('*')
        .order('status', { ascending: true });

      if (error) throw error;

      const groupedInitiatives = (data || []).reduce((acc, initiative) => {
        const status = initiative.status || 'غير محدد';
        if (!acc[status]) acc[status] = [];
        acc[status].push(initiative);
        return acc;
      }, {} as { [status: string]: Initiative[] });

      return groupedInitiatives;
    } catch (error) {
      console.error('Error fetching initiatives by status:', error);
      return {};
    }
  }

  // جلب مبادرة واحدة
  static async getInitiativeById(initiativeId: string): Promise<Initiative | null> {
    try {
      const { data, error } = await supabase
        .from('initiatives')
        .select('*')
        .eq('id', initiativeId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching initiative:', error);
      return null;
    }
  }
}
