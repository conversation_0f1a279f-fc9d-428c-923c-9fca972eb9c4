import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Lock, Mail } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface LoginFormData {
  email: string;
  password: string;
}

const Login = () => {
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>();
  
  const onSubmit = async (data: LoginFormData) => {
    setLoading(true);
    setError(null);
    
    try {
      const { error } = await signIn(data.email, data.password);
      
      if (error) {
        setError('بيانات الدخول غير صحيحة. البريد الإلكتروني الافتراضي هو <EMAIL> وكلمة المرور هي Strategic@123');
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقًا.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-xl p-6 w-full"
    >
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-primary-700 mb-2">نظام التخطيط الاستراتيجي</h1>
        <p className="text-neutral-600">يرجى تسجيل الدخول للاستمرار</p>
      </div>
      
      {error && (
        <div className="bg-error-50 text-error-700 p-3 rounded-md mb-4 text-sm">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="email" className="label">
            البريد الإلكتروني
          </label>
          <div className="relative">
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500">
              <Mail className="w-5 h-5" />
            </div>
            <input
              id="email"
              type="email"
              className={`input-field pr-10 ${errors.email ? 'border-error-500 focus:ring-error-300' : ''}`}
              placeholder="أدخل البريد الإلكتروني"
              {...register('email', { 
                required: 'البريد الإلكتروني مطلوب',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'البريد الإلكتروني غير صالح'
                }
              })}
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="password" className="label">
            كلمة المرور
          </label>
          <div className="relative">
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500">
              <Lock className="w-5 h-5" />
            </div>
            <input
              id="password"
              type="password"
              className={`input-field pr-10 ${errors.password ? 'border-error-500 focus:ring-error-300' : ''}`}
              placeholder="أدخل كلمة المرور"
              {...register('password', { 
                required: 'كلمة المرور مطلوبة',
                minLength: {
                  value: 6,
                  message: 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل'
                }
              })}
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
          )}
        </div>
        
        <button
          type="submit"
          className={`btn btn-primary w-full ${loading ? 'opacity-75 cursor-not-allowed' : ''}`}
          disabled={loading}
        >
          {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
        </button>
      </form>
    </motion.div>
  );
};

export default Login;