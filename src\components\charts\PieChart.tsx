import { useState } from 'react';

export interface PieChartData {
  name: string;
  value: number;
  color: string;
}

interface PieChartProps {
  data: PieChartData[];
  title?: string;
  size?: 'sm' | 'md' | 'lg';
  showLegend?: boolean;
  showValues?: boolean;
  centerText?: string;
}

const PieChart = ({ 
  data, 
  title, 
  size = 'md', 
  showLegend = true, 
  showValues = true,
  centerText 
}: PieChartProps) => {
  const [hoveredSegment, setHoveredSegment] = useState<number | null>(null);

  // تحديد أحجام الرسم
  const sizeConfig = {
    sm: { radius: 30, viewBox: 80, textSize: 'text-xs' },
    md: { radius: 40, viewBox: 100, textSize: 'text-sm' },
    lg: { radius: 50, viewBox: 120, textSize: 'text-base' }
  };

  const config = sizeConfig[size];
  const centerX = config.viewBox / 2;
  const centerY = config.viewBox / 2;
  const radius = config.radius;

  // حساب المجموع الكلي
  const total = data.reduce((sum, item) => sum + item.value, 0);

  if (total === 0) {
    return (
      <div className="flex flex-col items-center">
        {title && <h4 className={`font-medium mb-2 ${config.textSize}`}>{title}</h4>}
        <div className={`relative`} style={{ width: config.viewBox, height: config.viewBox }}>
          <svg viewBox={`0 0 ${config.viewBox} ${config.viewBox}`} className="w-full h-full">
            <circle 
              cx={centerX} 
              cy={centerY} 
              r={radius} 
              fill="#e5e7eb" 
              className="opacity-50"
            />
            <text 
              x={centerX} 
              y={centerY} 
              textAnchor="middle" 
              dominantBaseline="middle" 
              className="text-xs fill-gray-500"
            >
              لا توجد بيانات
            </text>
          </svg>
        </div>
      </div>
    );
  }

  // حساب المسارات والزوايا
  let startAngle = 0;
  const segments = data.map((item, index) => {
    const percentage = item.value / total;
    const endAngle = startAngle + percentage * 2 * Math.PI;
    
    const x1 = centerX + radius * Math.cos(startAngle);
    const y1 = centerY + radius * Math.sin(startAngle);
    const x2 = centerX + radius * Math.cos(endAngle);
    const y2 = centerY + radius * Math.sin(endAngle);
    
    const largeArcFlag = percentage > 0.5 ? 1 : 0;
    
    const pathData = [
      `M ${centerX} ${centerY}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');

    // حساب موقع النص
    const midAngle = startAngle + (endAngle - startAngle) / 2;
    const textRadius = radius * 0.7;
    const textX = centerX + textRadius * Math.cos(midAngle);
    const textY = centerY + textRadius * Math.sin(midAngle);
    
    const segment = {
      path: pathData,
      color: item.color,
      percentage: Math.round(percentage * 100),
      textX,
      textY,
      midAngle,
      item,
      index
    };
    
    startAngle = endAngle;
    return segment;
  });

  return (
    <div className="flex flex-col items-center">
      {title && <h4 className={`font-medium mb-2 ${config.textSize}`}>{title}</h4>}
      
      <div className="relative">
        <svg 
          viewBox={`0 0 ${config.viewBox} ${config.viewBox}`} 
          className="w-full h-full"
          style={{ width: config.viewBox, height: config.viewBox }}
        >
          {segments.map((segment, index) => (
            <g key={index}>
              <path 
                d={segment.path} 
                fill={segment.color}
                className={`transition-all duration-200 cursor-pointer ${
                  hoveredSegment === index ? 'opacity-80 transform scale-105' : 'opacity-100'
                }`}
                onMouseEnter={() => setHoveredSegment(index)}
                onMouseLeave={() => setHoveredSegment(null)}
              />
              
              {/* عرض النسب المئوية على الرسم */}
              {showValues && segment.percentage >= 5 && (
                <text 
                  x={segment.textX} 
                  y={segment.textY} 
                  textAnchor="middle" 
                  dominantBaseline="middle" 
                  className="text-xs fill-white font-medium pointer-events-none"
                  style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                >
                  {segment.percentage}%
                </text>
              )}
            </g>
          ))}
          
          {/* النص المركزي */}
          {centerText && (
            <text 
              x={centerX} 
              y={centerY} 
              textAnchor="middle" 
              dominantBaseline="middle" 
              className="text-sm font-bold fill-gray-700"
            >
              {centerText}
            </text>
          )}
        </svg>
      </div>
      
      {/* وسيلة الإيضاح */}
      {showLegend && (
        <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-3 w-full max-w-xs">
          {data.map((item, index) => (
            <div 
              key={index} 
              className={`flex items-center text-xs cursor-pointer transition-opacity ${
                hoveredSegment !== null && hoveredSegment !== index ? 'opacity-50' : 'opacity-100'
              }`}
              onMouseEnter={() => setHoveredSegment(index)}
              onMouseLeave={() => setHoveredSegment(null)}
            >
              <div 
                className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="truncate">{item.name}: {item.value}</span>
            </div>
          ))}
        </div>
      )}
      
      {/* معلومات إضافية عند التمرير */}
      {hoveredSegment !== null && (
        <div className="mt-2 text-center">
          <div className={`font-medium ${config.textSize}`}>
            {segments[hoveredSegment].item.name}
          </div>
          <div className="text-xs text-gray-600">
            {segments[hoveredSegment].item.value} ({segments[hoveredSegment].percentage}%)
          </div>
        </div>
      )}
    </div>
  );
};

export default PieChart;
