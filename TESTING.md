# 🧪 دليل اختبار نظام التخطيط الاستراتيجي

## 🚀 خطوات الاختبار السريع

### 1. تحضير البيئة
```bash
# استنساخ المشروع
git clone https://github.com/sh33hemam/-2.git
cd -2

# تثبيت المتطلبات
npm install

# تشغيل المشروع
npm run dev
```

### 2. إعداد النظام (مطلوب للمرة الأولى)
1. **افتح المتصفح**: `http://localhost:5173/setup`
2. **اضغط على**: "إعداد النظام"
3. **انتظر**: حتى ظهور رسالة "النظام جاهز للاستخدام!"
4. **احفظ بيانات الدخول** المعروضة

### 3. تسجيل الدخول
1. **انتقل إلى**: `http://localhost:5173/login`
2. **أدخل البيانات**:
   - البريد: `<EMAIL>`
   - كلمة المرور: `Strategic@123`
3. **اضغط**: "تسجيل الدخول"

## 🔍 ما يجب اختباره

### ✅ الميزات الأساسية
- [ ] **لوحة التحكم**: عرض الإحصائيات والرسوم البيانية
- [ ] **الأهداف الاستراتيجية**: إضافة، تعديل، حذف
- [ ] **مؤشرات الأداء**: إدارة KPIs مع الرسوم البيانية
- [ ] **المشاريع**: إدارة المبادرات والمشاريع
- [ ] **الأنشطة**: إدارة الأنشطة التشغيلية
- [ ] **المستخدمون**: إدارة المستخدمين والصلاحيات
- [ ] **الأقسام**: إدارة الهيكل التنظيمي
- [ ] **التقارير**: إنشاء وعرض التقارير

### ✅ الميزات المتقدمة
- [ ] **البحث والفلترة**: في جميع الصفحات
- [ ] **التصدير**: تصدير البيانات والتقارير
- [ ] **الرسوم البيانية**: التفاعل مع المخططات
- [ ] **الإشعارات**: نظام الإشعارات
- [ ] **التصميم المتجاوب**: اختبار على أجهزة مختلفة

## 🐛 المشاكل المعروفة

### ⚠️ مشاكل بسيطة (لا تؤثر على الوظائف الأساسية)
1. **خطأ في Activities.tsx**: 
   - الخطأ: `Lightbulb is not defined`
   - التأثير: قد يظهر خطأ في وحدة التحكم
   - الحل: سيتم إصلاحه في التحديث القادم

2. **تحذيرات React Router**:
   - تحذيرات حول الإصدار المستقبلي
   - لا تؤثر على الوظائف

### ✅ ما تم إصلاحه
- ✅ نظام المستخدمين وقاعدة البيانات
- ✅ المصادقة والتسجيل
- ✅ ربط البيانات مع Supabase
- ✅ إعداد النظام التلقائي

## 📊 نتائج الاختبار المتوقعة

### 🎯 الأداء المتوقع
- **سرعة التحميل**: < 3 ثواني
- **استجابة الواجهة**: فورية
- **دقة البيانات**: 100%
- **الاستقرار**: عالي

### 🔐 الأمان
- **المصادقة**: آمنة مع Supabase Auth
- **الصلاحيات**: Row Level Security (RLS)
- **حماية البيانات**: تشفير كامل

## 🆘 استكشاف الأخطاء

### مشكلة: لا يمكن الوصول للنظام
**الحل**:
1. تأكد من تشغيل `npm run dev`
2. تحقق من المنفذ: `http://localhost:5173`
3. امسح cache المتصفح

### مشكلة: فشل في إعداد النظام
**الحل**:
1. تحقق من اتصال الإنترنت
2. تأكد من صحة متغيرات البيئة
3. جرب إعادة تحميل الصفحة

### مشكلة: خطأ في تسجيل الدخول
**الحل**:
1. تأكد من إكمال إعداد النظام أولاً
2. استخدم البيانات الصحيحة:
   - `<EMAIL>`
   - `Strategic@123`
3. جرب إعادة إعداد النظام من `/setup`

## 📝 تقرير الاختبار

بعد الاختبار، يرجى تسجيل:
- [ ] الميزات التي تعمل بشكل صحيح
- [ ] أي أخطاء أو مشاكل واجهتها
- [ ] اقتراحات للتحسين
- [ ] تقييم عام للنظام

## 🔗 روابط مفيدة

- **المستودع**: https://github.com/sh33hemam/-2
- **Supabase Dashboard**: https://supabase.com/dashboard
- **التوثيق**: README.md

---

**ملاحظة**: هذا نظام تجريبي للاختبار. في البيئة الإنتاجية، يجب تغيير جميع كلمات المرور الافتراضية وإعداد متغيرات البيئة بشكل آمن.
