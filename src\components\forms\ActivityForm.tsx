import { useState } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { useQuickNotifications } from '../ui/NotificationSystem';

interface ActivityFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (activity: any) => Promise<void>;
  editData?: any;
}

const ActivityForm = ({ isOpen, onClose, onSave, editData }: ActivityFormProps) => {
  const [loading, setLoading] = useState(false);
  const notifications = useQuickNotifications();
  
  const [formData, setFormData] = useState({
    title: editData?.title || '',
    description: editData?.description || '',
    assignee: editData?.assignee || '',
    department: editData?.department || '',
    initiative: editData?.initiative || '',
    priority: editData?.priority || 'متوسط',
    status: editData?.status || 'جديد',
    startDate: editData?.startDate || '',
    endDate: editData?.endDate || '',
    budget: editData?.budget || '',
    participants: editData?.participants || '',
    expectedOutcome: editData?.expectedOutcome || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان النشاط مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف النشاط مطلوب';
    }

    if (!formData.assignee.trim()) {
      newErrors.assignee = 'المسؤول عن النشاط مطلوب';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'القسم المسؤول مطلوب';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'تاريخ البداية مطلوب';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية مطلوب';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      notifications.error('خطأ في البيانات', 'يرجى تصحيح الأخطاء المذكورة');
      return;
    }

    setLoading(true);
    try {
      await onSave({
        ...formData,
        budget: formData.budget ? parseInt(formData.budget) : 0,
        participants: formData.participants ? parseInt(formData.participants) : 1,
        id: editData?.id || `activity-${Date.now()}`,
        createdAt: editData?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        progress: editData?.progress || 0
      });
      
      notifications.success(
        editData ? 'تم تحديث النشاط' : 'تم إضافة النشاط', 
        'تم حفظ البيانات بنجاح'
      );
      onClose();
    } catch (error) {
      notifications.error('خطأ في الحفظ', 'فشل في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">
            {editData ? 'تعديل النشاط التشغيلي' : 'إضافة نشاط تشغيلي جديد'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* العنوان */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عنوان النشاط *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`input-field ${errors.title ? 'border-red-500' : ''}`}
              placeholder="أدخل عنوان النشاط التشغيلي"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف النشاط *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="وصف تفصيلي للنشاط التشغيلي"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </div>

          {/* المسؤول والقسم */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المسؤول عن النشاط *
              </label>
              <input
                type="text"
                value={formData.assignee}
                onChange={(e) => handleChange('assignee', e.target.value)}
                className={`input-field ${errors.assignee ? 'border-red-500' : ''}`}
                placeholder="اسم المسؤول"
              />
              {errors.assignee && <p className="text-red-500 text-xs mt-1">{errors.assignee}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القسم المسؤول *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                className={`input-field ${errors.department ? 'border-red-500' : ''}`}
              >
                <option value="">اختر القسم</option>
                <option value="إدارة التطوير والابتكار">إدارة التطوير والابتكار</option>
                <option value="إدارة ريادة الأعمال">إدارة ريادة الأعمال</option>
                <option value="إدارة البرامج الشبابية">إدارة البرامج الشبابية</option>
                <option value="الإدارة الثقافية">الإدارة الثقافية</option>
                <option value="إدارة المرافق">إدارة المرافق</option>
              </select>
              {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
            </div>
          </div>

          {/* المبادرة والأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                المبادرة المرتبطة
              </label>
              <input
                type="text"
                value={formData.initiative}
                onChange={(e) => handleChange('initiative', e.target.value)}
                className="input-field"
                placeholder="اسم المبادرة أو البرنامج"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                مستوى الأولوية
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="input-field"
              >
                <option value="عالي">عالي</option>
                <option value="متوسط">متوسط</option>
                <option value="منخفض">منخفض</option>
              </select>
            </div>
          </div>

          {/* الحالة والمشاركين */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                حالة النشاط
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="input-field"
              >
                <option value="جديد">جديد</option>
                <option value="جاري التنفيذ">جاري التنفيذ</option>
                <option value="مكتمل">مكتمل</option>
                <option value="متأخر">متأخر</option>
                <option value="متعثر">متعثر</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                عدد المشاركين
              </label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => handleChange('participants', e.target.value)}
                className="input-field"
                placeholder="1"
                min="1"
              />
            </div>
          </div>

          {/* التواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ البداية *
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
                className={`input-field ${errors.startDate ? 'border-red-500' : ''}`}
              />
              {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ النهاية *
              </label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={`input-field ${errors.endDate ? 'border-red-500' : ''}`}
              />
              {errors.endDate && <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>}
            </div>
          </div>

          {/* الميزانية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الميزانية المقدرة (ريال)
            </label>
            <input
              type="number"
              value={formData.budget}
              onChange={(e) => handleChange('budget', e.target.value)}
              className="input-field"
              placeholder="0"
              min="0"
            />
          </div>

          {/* النتائج المتوقعة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              النتائج المتوقعة
            </label>
            <textarea
              value={formData.expectedOutcome}
              onChange={(e) => handleChange('expectedOutcome', e.target.value)}
              rows={2}
              className="input-field"
              placeholder="النتائج والمخرجات المتوقعة من هذا النشاط"
            />
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex-1 flex items-center justify-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {loading ? 'جاري الحفظ...' : 'حفظ النشاط'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-outline">
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ActivityForm;
